package com.huoli.ctar.cms.ctm.tcm.external.service.impl;


import com.huoli.ctar.cms.ctm.tcm.external.service.BookingConfigService;
import com.huoli.ctar.core.infra.constant.BaseShowColumn;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.exception.ServiceException;
import com.huoli.ctar.tmc.svc.clients.TmcSvcClient;
import com.huoli.ctar.tmc.svc.clients.exception.TmcSvcClientInvokeException;
import com.huoli.ctar.tmc.svc.clients.exception.TmcSvcClientUnavailableException;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.*;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.vo.std.*;
import com.huoli.ctar.tmccbs.biz.domain.constant.BizId;
import com.huoli.ctar.tmccbs.biz.domain.constant.CooperatedPlatform;
import com.huoli.ctar.tmccbs.biz.domain.constant.ProductShelf;
import com.huoli.ctar.tmccbs.biz.domain.constant.ServiceId;
import com.huoli.ctar.tmccbs.biz.domain.model.ServiceAllowedPay;
import com.huoli.ctar.tmccbs.biz.domain.vo.DefaultStdDataQueryCfgBuilder;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tbc.vo.tmc.common.BookingConfigExternalVO;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tbc.vo.tmc.common.BookingEntranceConfigExternalVO;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tbc.vo.tmc.common.ServiceNotEnabledReason;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 预订信息
 */
@Service("bookingConfigService")
public class BookingConfigServiceImpl implements BookingConfigService {

    private static final Logger log = LoggerFactory.getLogger(BookingConfigServiceImpl.class);

    @Autowired
    private TmcSvcClient tmcSvcClient;

    /**
     * http://192.168.3.248/ctar-docs/cms-doc/blob/master/0631/%E8%8E%B7%E5%8F%96%E9%A2%84%E8%AE%A2%E4%BA%BA%E9%A2%84%E5%AE%9A%E9%85%8D%E7%BD%AE.md
     * 获取预订人预订配置
     */
    @Override
    public BookingConfigExternalVO retrieveBookingConfig(final String phoneIdOfBookedBy, final String bizId) {
        final Long corpMemberId;
        try {
            corpMemberId = tmcSvcClient.chkCorpMember(phoneIdOfBookedBy);
        } catch (TmcSvcClientUnavailableException | TmcSvcClientInvokeException e) {
            throw new ServiceException(e.getMessage(), e);
        }

        final CorpMemberStdVO corpMember;
        try {
            corpMember = tmcSvcClient.getCorpMember(corpMemberId,
                    new CorpMemberQueryCfg.QueryCfgBuilder()
                            .needTravelConfigFlag(GlobalConstant.FLAG_YES_VALUE)
                            .travelConfigQueryCfg(new DefaultStdDataQueryCfgBuilder()
                                    .addShowColumn(CorpMemberTravelConfig.ShowColumn.SERVICE_ALLOWED_PAY.getName())
                                    .build())
                            .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(), CorpMember.ShowColumn.NAME.getName(),
                                    CorpMember.ShowColumn.STATUS.getName(), CorpMember.ShowColumn.MOBILE.getName()))
                            .build());
        } catch (TmcSvcClientUnavailableException | TmcSvcClientInvokeException e) {
            throw new ServiceException(e.getMessage(), e);
        }
        final CorpInfoStdVO corpInfo;
        try {
            corpInfo = tmcSvcClient.getCorpInfo(corpMember.getCorpId(),
                    new CorpInfoQueryCfg.QueryCfgBuilder()
                            .needPaymentCfgFlag(GlobalConstant.FLAG_YES_VALUE)
                            .paymentCfgQueryCfg(new DefaultStdDataQueryCfgBuilder()
                                    .addShowColumns(Collections.singletonList(PaymentConfig.ShowColumn.SERVICE_ALLOWED_PAY.getName()))
                                    .build())
                            .needBookingEntranceCfgFlag(GlobalConstant.FLAG_YES_VALUE)
                            .bookingEntranceCfgQueryCfg(new DefaultStdDataQueryCfgBuilder()
                                    .addShowColumns(Arrays.asList(BookingEntranceConfig.ShowColumn.BIZ_ID.getName(), BookingEntranceConfig.ShowColumn.SERVICE_ID.getName(),
                                            BookingEntranceConfig.ShowColumn.CORP_SHELF_SWITCH.getName(), BookingEntranceConfig.ShowColumn.DEFAULT_PRODUCT_SHELF.getName(),
                                            BookingEntranceConfig.ShowColumn.AVAILABLE_PRODUCT_SHELVES.getName()))
                                    .build())
                            .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(),
                                    CorpInfo.ShowColumn.CORP_NAME.getName(), CorpInfo.ShowColumn.CORP_TYPE.getName(),
                                    CorpInfo.ShowColumn.PLATFORM_COOPERATED.getName(), CorpInfo.ShowColumn.SOURCE_CHANNEL.getName(),
                                    CorpInfo.ShowColumn.SOURCE_CHANNEL.getName()))
                            .build());
        } catch (TmcSvcClientUnavailableException | TmcSvcClientInvokeException e) {
            throw new ServiceException(e.getMessage(), e);
        }

        if (log.isInfoEnabled()) {
            log.info(String.format("retrieve bookingConfig ==> phoneIdOfBookedBy: %s, corpMemberId: %s, corpMemberName: %s, corpId: %s, corpName: %s",
                    phoneIdOfBookedBy, corpMember.getId(), corpMember.getName(), corpInfo.getId(), corpInfo.getCorpName()));
        }

        final BookingConfigExternalVO bookingConfig = new BookingConfigExternalVO();

        final List<ServiceAllowedPay> serviceAllowedPaysOfCorpMember;
        if (Objects.isNull(corpMember.getTrvlCfg()) || StringUtils.isBlank(corpMember.getTrvlCfg().getServiceAllowedPay())) {
            serviceAllowedPaysOfCorpMember = Collections.emptyList();
        } else {
            serviceAllowedPaysOfCorpMember = ServiceAllowedPay.toServiceAllowsPays(corpMember.getTrvlCfg().getServiceAllowedPay());
        }
        final List<ServiceAllowedPay> serviceAllowedPaysOfCorp;
        if (Objects.isNull(corpInfo.getPaymentCfg()) || StringUtils.isBlank(corpInfo.getPaymentCfg().getServiceAllowedPay())) {
            serviceAllowedPaysOfCorp = Collections.emptyList();
        } else {
            serviceAllowedPaysOfCorp = ServiceAllowedPay.toServiceAllowsPays(corpInfo.getPaymentCfg().getServiceAllowedPay());
        }
        bookingConfig.setBookingEntranceConfigs(buildBkgEntranceCfgs(corpInfo.getBookingEntranceCfgs(), serviceAllowedPaysOfCorpMember, serviceAllowedPaysOfCorp, bizId));

        final CorpInfo.SourceChannel sourceChannel = CorpInfo.SourceChannel.getByCode(corpInfo.getSourceChannel());

        if (Objects.equals(corpInfo.getId(), 2L)
                || CorpInfo.CorpType.MINI_PPR.getId() == corpInfo.getCorpType()
                || CorpInfo.SourceChannel.CAISSA == sourceChannel
                || CorpInfo.SourceChannel.HUOLI_CAISSA == sourceChannel
                || (StringUtils.isNotBlank(corpInfo.getPlatformCooperated()) && StringUtils.equals(CooperatedPlatform.EASYPNP.getCode(), corpInfo.getPlatformCooperated()))) {
            // "活力世纪"或者"试用企业"或者"凯撒企业"或者"阳光印网"在使用非商旅APP时, 屏蔽企业货架，默认选中个人货架
            for (final BookingEntranceConfigExternalVO bookingEntranceConfig : bookingConfig.getBookingEntranceConfigs()) {
                bookingEntranceConfig.setCorpShelfSwitch(GlobalConstant.Switch.OFF);
                bookingEntranceConfig.setDefaultProductShelf(ProductShelf.PERSONAL_SHELF.getCode());
                bookingEntranceConfig.setAvailableProductShelves(Collections.singletonMap(ProductShelf.PERSONAL_SHELF.getCode(), ProductShelf.PERSONAL_SHELF.getDisplayName()));
            }
        }
        return bookingConfig;
    }

    private boolean bizEnabled(final List<ServiceAllowedPay> serviceAllowedPays, final BizId bizId) {
        if (CollectionUtils.isEmpty(serviceAllowedPays)) {
            return false;
        }
        for (final ServiceAllowedPay serviceAllowedPay : serviceAllowedPays) {
            if (StringUtils.isNotBlank(serviceAllowedPay.getCode())) {
                final ServiceId serviceId = ServiceId.getByCode(serviceAllowedPay.getCode());
                if (Objects.nonNull(serviceId) && bizId == serviceId.getBizId()) {
                    return true;
                }
            }
        }
        return false;
    }

    private List<BookingEntranceConfigExternalVO> buildBkgEntranceCfgs(final List<BookingEntranceConfigStdVO> bookingEntranceConfigs,
                                                                       final List<ServiceAllowedPay> serviceAllowedPaysOfCorpMember,
                                                                       final List<ServiceAllowedPay> serviceAllowedPaysOfCorp,
                                                                       final String givenBizId) {

        final Map<String, BookingEntranceConfigStdVO> bookingEntranceConfigMap;
        if (CollectionUtils.isEmpty(bookingEntranceConfigs)) {
            bookingEntranceConfigMap = Collections.emptyMap();
        } else {
            bookingEntranceConfigMap = new HashMap<>(bookingEntranceConfigs.size());
            bookingEntranceConfigs.forEach(bookingEntranceConfig -> {
                if (StringUtils.isNotBlank(bookingEntranceConfig.getBizId())) {
                    bookingEntranceConfigMap.put(bookingEntranceConfig.getBizId(), bookingEntranceConfig);
                }
            });
        }

        final BizId givenBizIdEnum = StringUtils.isBlank(givenBizId) ? null : BizId.getByCode(givenBizId);
        final BizId[] bizIds = Objects.isNull(givenBizIdEnum) ? BizId.values() : new BizId[]{givenBizIdEnum};
        final List<BookingEntranceConfigExternalVO> bkgEntranceCfgs = new ArrayList<>(bizIds.length);
        for (final BizId bizId : bizIds) {
            final boolean corpSupported = bizEnabled(serviceAllowedPaysOfCorp, bizId);
            final boolean corpMemberAllowed = bizEnabled(serviceAllowedPaysOfCorpMember, bizId);
            final BookingEntranceConfigExternalVO bkgEntranceCfg = new BookingEntranceConfigExternalVO();
            bkgEntranceCfg.setBizId(bizId.getCode());
            bkgEntranceCfg.setBizName(bizId.getName());

            if (bookingEntranceConfigMap.containsKey(bizId.getCode())) {
                final BookingEntranceConfigStdVO bookingEntranceConfig = bookingEntranceConfigMap.get(bizId.getCode());
                bkgEntranceCfg.setCorpShelfSwitch(bookingEntranceConfig.getCorpShelfSwitch());
                bkgEntranceCfg.setDefaultProductShelf(bookingEntranceConfig.getDefaultProductShelf());
                bkgEntranceCfg.setAvailableProductShelves(StringUtils.isBlank(bookingEntranceConfig.getAvailableProductShelves()) ?
                        Collections.emptyMap() : Arrays.stream(StringUtils.split(bookingEntranceConfig.getAvailableProductShelves(), ","))
                        .map(ProductShelf::get)
                        .collect(Collectors.toMap(ProductShelf::getCode, ProductShelf::getDisplayName)));
            } else {
                bkgEntranceCfg.setCorpShelfSwitch(corpMemberAllowed ? GlobalConstant.Switch.ON : GlobalConstant.Switch.OFF);
                bkgEntranceCfg.setDefaultProductShelf(corpMemberAllowed ? ProductShelf.CORP_SHELF.getCode() : ProductShelf.PERSONAL_SHELF.getCode());
                final Map<String, String> availableProductShelves = new HashMap<>();
                availableProductShelves.put(ProductShelf.PERSONAL_SHELF.getCode(), ProductShelf.PERSONAL_SHELF.getDisplayName());
                if (corpMemberAllowed) {
                    availableProductShelves.put(ProductShelf.CORP_SHELF.getCode(), ProductShelf.CORP_SHELF.getDisplayName());
                }
                bkgEntranceCfg.setAvailableProductShelves(availableProductShelves);
            }

            if (corpSupported && corpMemberAllowed) {
                bkgEntranceCfg.setCorpBizBookingEnabled(GlobalConstant.TRUE);
                bkgEntranceCfg.setCorpBizBookingNotEnabledReason(GlobalConstant.NONE_CN);
            } else {
                bkgEntranceCfg.setCorpBizBookingEnabled(GlobalConstant.FALSE);
                if (corpSupported) {
                    bkgEntranceCfg.setCorpBizBookingNotEnabledReason(ServiceNotEnabledReason.CORP_MEMBER_NOT_ALLOWED.getCode());
                } else {
                    bkgEntranceCfg.setCorpBizBookingNotEnabledReason(ServiceNotEnabledReason.CORP_NOT_SUPPORTED.getCode());
                }
            }
            bkgEntranceCfgs.add(bkgEntranceCfg);
        }

        return bkgEntranceCfgs;
    }
}
