package com.huoli.ctar.cms.ctm.tcm.external.service.impl;

import com.huoli.ctar.cms.ctm.tcm.external.service.TravelConfigService;
import com.huoli.ctar.common.utils.JsonUtil;
import com.huoli.ctar.core.infra.exception.ServiceException;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.BizPackageConfig;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.CorpMember;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.CorpMemberTravelConfig;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.PaymentConfig;
import com.huoli.ctar.tmccbs.biz.domain.exception.TmcCommonValidationException;
import com.huoli.ctar.tmccbs.biz.domain.model.ServiceAllowedPay;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tbc.vo.tmc.common.BizPackageInfoConfigExternalVO;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tbc.vo.tmc.common.TravelConfigExternalVO;
import com.huoli.ctar.tmccbs.cms.basic.cam.service.internal.BizPackageConfigInternalService;
import com.huoli.ctar.tmccbs.cms.cam.tmc.common.service.CorpBaseValidationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("travelConfigService")
public class TravelConfigServiceImpl implements TravelConfigService {

    @Autowired
    @Qualifier("corpBaseValidationService")
    private CorpBaseValidationService corpBaseValidationService;

    @Autowired
    @Qualifier("bizPackageConfigInternalService")
    private BizPackageConfigInternalService bizPackageConfigInternalService;

    private void handleTmcCommonValidationException(TmcCommonValidationException e) {
        Map<String, String> messageMap = new HashMap<>();
        messageMap.put("errorMsg", e.getMessage());
        messageMap.put("displayErrorMsg", e.getDisplayMsg());
        throw new ServiceException(e.getCode(), JsonUtil.toJson(messageMap), e);
    }


    /**
     * <AUTHOR>
     * 获取差旅配置信息接口
     * <api>http://192.168.3.248/ctar-docs/cms-doc/blob/master/0631/%E8%8E%B7%E5%8F%96%E5%B7%AE%E6%97%85%E9%85%8D%E7%BD%AE%E4%BF%A1%E6%81%AF%E6%8E%A5%E5%8F%A3.md</api>
     */
    @Override
    public TravelConfigExternalVO retrieveTravelConfig(String phoneIdOfBookedBy, String serviceId) {
        CorpMember bookedBy = null;
        try {
            bookedBy = corpBaseValidationService.checkCorpMember(phoneIdOfBookedBy);
        } catch (TmcCommonValidationException e) {
            handleTmcCommonValidationException(e);
        }

        final CorpMemberTravelConfig bookedByTravelConfig = corpBaseValidationService.getCorpMemberTravelConfig(Objects.requireNonNull(bookedBy).getSid());
        final String bookedByServiceAllowedPay = Objects.nonNull(bookedByTravelConfig) ? bookedByTravelConfig.getServiceAllowedPay() : null;

        final TravelConfigExternalVO travelConfigVO = new TravelConfigExternalVO();
        final PaymentConfig paymentConfig = corpBaseValidationService.getPaymentConfig(bookedBy.getCorpInfoSid());
        if (Objects.nonNull(paymentConfig)) {
            List<ServiceAllowedPay> servicesAllowCorpMemberToBook = ServiceAllowedPay.getServicesAllowCorpMemberToBook(bookedByServiceAllowedPay, paymentConfig.getServiceAllowedPay());
            travelConfigVO.setServiceAllowedPays(servicesAllowCorpMemberToBook);
        }

        travelConfigVO.setBizPackageInfoConfigs(getBizPackageInfoConfigs(bookedBy.getCorpInfoSid(), serviceId));
        return travelConfigVO;
    }

    private List<BizPackageInfoConfigExternalVO> getBizPackageInfoConfigs(Long corpInfoSidOfBookedBy, String serviceId) {
        List<BizPackageConfig> bizPackageConfigs = bizPackageConfigInternalService.findByCorpInfoSidAndServiceId(corpInfoSidOfBookedBy, serviceId);
        if (CollectionUtils.isEmpty(bizPackageConfigs)) {
            return null;
        }
        return bizPackageConfigs.stream()
                .map(BizPackageInfoConfigExternalVO::new)
                .collect(Collectors.toList());
    }
}
