package com.huoli.ctar.cms.ctm.tcm.external.service.impl;

import com.huoli.ctar.cms.ctm.tcm.external.service.BusinessConfigService;
import com.huoli.ctar.cms.ctm.tcm.external.service.TravelConfigService;
import com.huoli.ctar.core.infra.constant.AppName;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.exception.ServiceException;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.CorpInfo;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.CorpTravelConfig;
import com.huoli.ctar.tmccbs.biz.domain.cms.ctm.exception.PaymentValidationException;
import com.huoli.ctar.tmccbs.biz.domain.cms.ctm.tsm.constant.StarRate;
import com.huoli.ctar.tmccbs.biz.domain.cms.ctm.tsm.model.*;
import com.huoli.ctar.tmccbs.biz.domain.constant.BizId;
import com.huoli.ctar.tmccbs.biz.domain.constant.ProductShelf;
import com.huoli.ctar.tmccbs.biz.domain.constant.ServiceId;
import com.huoli.ctar.tmccbs.biz.domain.model.ProductAllowedPay;
import com.huoli.ctar.tmccbs.biz.domain.model.ServiceAllowedPay;
import com.huoli.ctar.tmccbs.client.domain.cms.cam.vo.tmc.common.CorpMemberGeneralVO;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.pm.vo.tmc.common.HotelTravelStandardHolder;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tbc.vo.tmc.common.BizPackageInfoConfigExternalVO;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tbc.vo.tmc.common.BusinessConfigVO;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tbc.vo.tmc.common.TravelConfigExternalVO;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tsm.vo.tmc.common.*;
import com.huoli.ctar.tmccbs.cms.basic.cam.service.internal.CorpTravelConfigInternalService;
import com.huoli.ctar.tmccbs.cms.basic.ctm.tsm.service.internal.TravelTempInternalService;
import com.huoli.ctar.tmccbs.cms.cam.tmc.common.service.CorpBaseValidationService;
import com.huoli.ctar.tmccbs.cms.cam.tmc.common.service.CorpMemberTmcCommonService;
import com.huoli.ctar.tmccbs.cms.ctm.common.service.hotel.HotelTravelStandardValidationService;
import com.huoli.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service("businessConfigService")
public class BusinessConfigServiceImpl implements BusinessConfigService {
    @Autowired
    @Qualifier("corpMemberTmcCommonService")
    private CorpMemberTmcCommonService corpMemberTmcCommonService;

    @Autowired
    @Qualifier("travelConfigService")
    private TravelConfigService travelConfigService;

    @Autowired
    @Qualifier("travelTempInternalService")
    private TravelTempInternalService travelTempInternalService;

    @Autowired
    @Qualifier("corpBaseValidationService")
    private CorpBaseValidationService corpBaseValidationService;

    @Autowired
    @Qualifier("corpTravelConfigInternalService")
    private CorpTravelConfigInternalService corpTravelConfigInternalService;

    @Autowired
    @Qualifier("hotelTravelStandardValidationService")
    private HotelTravelStandardValidationService hotelTravelStandardValidationService;

    @Override
    public BusinessConfigVO retrieveBusinessConfig(String phoneIdOfBookedBy, String serviceId, String productId, String cityCodeOfHotelBooked,String p) {
        ServiceId serviceIdEnum = ServiceId.getByCode(serviceId);
        if (Objects.isNull(serviceIdEnum) || Objects.isNull(serviceIdEnum.getBizId())) {
            throw new ServiceException(String.format("获取失败，暂时支持的服务为：%s", getSupportServiceIds()));
        }

        //预定酒店所在城市码(酒店业务必传)
        if (Objects.equals(ServiceId.HOTEL.getCode(), serviceId) && StringUtil.isEmpty(cityCodeOfHotelBooked)) {
            throw new ServiceException("预定酒店所在城市码缺失");
        }

        BusinessConfigVO businessConfigVO = new BusinessConfigVO();

        BizId bizId = serviceIdEnum.getBizId();
        CorpMemberGeneralVO corpMemberGeneralVO = queryCorpMemberGeneralInfo(phoneIdOfBookedBy, bizId);

        TravelConfigExternalVO travelConfigExternalVO = travelConfigService.retrieveTravelConfig(phoneIdOfBookedBy, serviceId);
        if (Objects.nonNull(travelConfigExternalVO)) {
            List<ServiceAllowedPay> serviceAllowedPays = travelConfigExternalVO.getServiceAllowedPays();
            if (!CollectionUtils.isEmpty(serviceAllowedPays)) {
                serviceAllowedPays = serviceAllowedPays.stream().filter(item -> serviceId.equals(item.getCode())).collect(Collectors.toList());
            }

            businessConfigVO.setServiceEnabled(CollectionUtils.isEmpty(serviceAllowedPays) ? GlobalConstant.FALSE : GlobalConstant.TRUE);

            List<String> erpCodes = new ArrayList<>();
            if (Objects.nonNull(serviceAllowedPays)) {
                serviceAllowedPays.forEach(serviceAllowedPay -> {
                    List<ProductAllowedPay> productAllowedPays = serviceAllowedPay.getPrdIds();
                    if (Objects.nonNull(productAllowedPays)) {
                        productAllowedPays.forEach(productAllowedPay -> {
                            if (CollectionUtils.isEmpty(productAllowedPay.getErpCodes())) {
                                return;
                            }
                            if (StringUtil.isEmpty(productId) || productId.equals(productAllowedPay.getCode())) {
                                erpCodes.addAll(productAllowedPay.getErpCodes());
                            }
                        });
                    }
                });
            }

            businessConfigVO.setErpCodes(erpCodes);

            if (Objects.nonNull(travelConfigExternalVO.getBizPackageInfoConfigs())) {
                List<String> bizPackageIds = travelConfigExternalVO.getBizPackageInfoConfigs().stream().map(BizPackageInfoConfigExternalVO::getPackageId).collect(Collectors.toList());
                businessConfigVO.setBizPackageIds(bizPackageIds);
            }
        }
        //业务未开启，设置个人货架
        if (StringUtil.isNotBlank(businessConfigVO.getServiceEnabled()) && StringUtil.equals(businessConfigVO.getServiceEnabled(), GlobalConstant.FALSE)) {
            businessConfigVO.setTmc(ProductShelf.PERSONAL_SHELF.getCode());
        } else {
            if (Objects.nonNull(corpMemberGeneralVO)) {
                businessConfigVO.setTmc(Objects.nonNull(corpMemberGeneralVO.getDefaultProductShelf()) ? corpMemberGeneralVO.getDefaultProductShelf() : ProductShelf.PERSONAL_SHELF.getCode());
            }
        }

        //差标过滤国内机票，火车票开关
        CorpTravelConfig corpTravelConfig = corpTravelConfigInternalService.findByCorpInfoSid(corpMemberGeneralVO.getCorpId());

        if (Objects.equals(ServiceId.DOMESTIC_FLIGHT_TICKET.getCode(), serviceId)) {
            businessConfigVO.setFilterAgainstDftTravelStandardSwitch(GlobalConstant.Switch.OFF);
            if (Objects.nonNull(corpTravelConfig)) {
                businessConfigVO.setFilterAgainstDftTravelStandardSwitch(corpTravelConfig.getFilterAgainstDftTravelStandardSwitch());
            }
        }
        else if (Objects.equals(ServiceId.TRAIN_TICKET.getCode(), serviceId)) {
            businessConfigVO.setFilterAgainstTrainTicketTravelStandardSwitch(GlobalConstant.Switch.OFF);
            if (Objects.nonNull(corpTravelConfig)) {
                businessConfigVO.setFilterAgainstTrainTicketTravelStandardSwitch(corpTravelConfig.getFilterAgainstTrainTicketTravelStandardSwitch());
            }
        }
        else if (Objects.equals(ServiceId.INTERNATIONAL_FLIGHT_TICKET.getCode(), serviceId)) {
            businessConfigVO.setFilterAgainstIftTravelStandardSwitch(GlobalConstant.Switch.OFF);
            if (Objects.nonNull(corpTravelConfig)) {
                businessConfigVO.setFilterAgainstIftTravelStandardSwitch(corpTravelConfig.getFilterAgainstIftTravelStandardSwitch());
            }
        }
        else if (Objects.equals(ServiceId.HOTEL.getCode(), serviceId)) {
            businessConfigVO.setFilterAgainstHotelTravelStandardSwitch(GlobalConstant.Switch.OFF);
            if (Objects.nonNull(corpTravelConfig)) {
                businessConfigVO.setFilterAgainstHotelTravelStandardSwitch(corpTravelConfig.getFilterAgainstHotelTravelStandardSwitch());
            }
        }
        //获取预定人企业信息
        final CorpInfo corpInfoOfBookedBy;
        try {
            corpInfoOfBookedBy = corpBaseValidationService.checkCorpInfo(corpMemberGeneralVO.getCorpId(), String.valueOf(corpMemberGeneralVO.getPhoneId()));
        } catch (PaymentValidationException e) {
            log.error(String.format("企业信息校验失败,  phoneIdOfBookedBy: %s, corpId: %d, 错误消息: %s", phoneIdOfBookedBy, corpMemberGeneralVO.getCorpId(), e.getMessage()), e);
            throw new ServiceException(e);
        }

        //新增国内舱位标准和火车票坐席
        final TravelTempSetting travelTempSetting = travelTempInternalService.getByCorpMemberSid(corpMemberGeneralVO.getId());
        final boolean travelStandardEnabled = corpBaseValidationService.travelStandardEnabledAsBooking(corpInfoOfBookedBy, travelTempSetting, serviceId);

        if (travelStandardEnabled) {
            TravelStandardExternalVO travelStandardExternalVO = new TravelStandardExternalVO();
            if (Objects.equals(ServiceId.DOMESTIC_FLIGHT_TICKET.getCode(), serviceId)) {
                DomesticFlightTicketBookingTravelStandardSetting domesticFlightTicketTravelStandardSetting = travelTempSetting.getDomesticFlightTicketTravelStandardSetting().getBookingTravelStandardSetting();
                List<String> classLevels = domesticFlightTicketTravelStandardSetting.getClassLevels();
                Integer lowestPriceWindow = domesticFlightTicketTravelStandardSetting.getLowestPriceWindow();

                DomesticFlightTicketTravelStandardSettingExternalVO domesticFlightTicketTravelStandardSettingExternalVO = new DomesticFlightTicketTravelStandardSettingExternalVO();
                domesticFlightTicketTravelStandardSettingExternalVO.setClassLevels(classLevels);
                domesticFlightTicketTravelStandardSettingExternalVO.setLowestPriceWindow(Objects.isNull(lowestPriceWindow) ? -1 : lowestPriceWindow);
                travelStandardExternalVO.setDomesticFlightTicketTravelStandardSetting(domesticFlightTicketTravelStandardSettingExternalVO);

            }
            else if (Objects.equals(ServiceId.TRAIN_TICKET.getCode(), serviceId)) {
                TrainTicketBookingTravelStandardSetting trainTicketTravelStandardSetting = travelTempSetting.getTrainTicketTravelStandardSetting().getBookingTravelStandardSetting();
                List<String> seatTypes = trainTicketTravelStandardSetting.getSeatTypes();
                TrainTicketTravelStandardSettingExternalVO trainTicketTravelStandardSettingExternalVO = new TrainTicketTravelStandardSettingExternalVO();
                trainTicketTravelStandardSettingExternalVO.setSeatTypes(seatTypes);
                travelStandardExternalVO.setTrainTicketTravelStandardSetting(trainTicketTravelStandardSettingExternalVO);
            }
            else if (Objects.equals(ServiceId.INTERNATIONAL_FLIGHT_TICKET.getCode(), serviceId)) {
                InFlightTicketBookingTravelStandardSetting inFlightTicketBookingTravelStandardSetting = travelTempSetting.getInFlightTicketTravelStandardSetting().getBookingTravelStandardSetting();
                List<String> classLevels = inFlightTicketBookingTravelStandardSetting.getClassLevels();
                Integer lowestPriceWindow = inFlightTicketBookingTravelStandardSetting.getLowestPriceWindow();
                InternationalFlightTicketTravelStandardSettingExternalVO internationalFlightTicketTravelStandardSettingExternalVO = new InternationalFlightTicketTravelStandardSettingExternalVO();
                internationalFlightTicketTravelStandardSettingExternalVO.setClassLevels(classLevels);
                internationalFlightTicketTravelStandardSettingExternalVO.setLowestPriceWindow(Objects.isNull(lowestPriceWindow) ? -1 : lowestPriceWindow);
                travelStandardExternalVO.setInternationalFlightTicketTravelStandardSetting(internationalFlightTicketTravelStandardSettingExternalVO);
            }
            else if (Objects.equals(ServiceId.HOTEL.getCode(), serviceId)) {
                HotelTravelStandardSetting bookingTravelStandardSetting = travelTempSetting.getHotelTravelStandardSetting();
                final Long cityGroupId = hotelTravelStandardValidationService.getCityGroupId(corpInfoOfBookedBy.getSid(), cityCodeOfHotelBooked, null);
                HotelTravelStandardHolder hotelTravelStandardHolder = hotelTravelStandardValidationService.getHotelTravelStandardHolder(bookingTravelStandardSetting, cityGroupId);
                HotelTravelStandardSettingExternalVO hotelTravelStandardSettingExternalVO = new HotelTravelStandardSettingExternalVO();
                hotelTravelStandardSettingExternalVO.setMaxCostAllowed(hotelTravelStandardHolder.getMaxCostAllowed());
                hotelTravelStandardSettingExternalVO.setStarRateUpperLimit(StarRate.getByCode(hotelTravelStandardHolder.getStarRateUpperLimit()).getCode());
                travelStandardExternalVO.setHotelTravelStandardSetting(hotelTravelStandardSettingExternalVO);

            }
            else if (Objects.equals(ServiceId.CAR.getCode(), serviceId)) {
                CarBookingTravelStandardSetting bookingTravelStandardSetting = travelTempSetting.getCarTravelStandardSetting().getBookingTravelStandardSetting();
                CarTravelStandardSettingExternalVO carTravelStandardSettingExternalVO = new CarTravelStandardSettingExternalVO();
                carTravelStandardSettingExternalVO.setCarTypes(bookingTravelStandardSetting.getCarTypes());
                travelStandardExternalVO.setCarTravelStandardSetting(carTravelStandardSettingExternalVO);
            }
            businessConfigVO.setTravelStandard(travelStandardExternalVO);

            //新增国内机票货架控制的开关参
            businessConfigVO.setJustLoadLowestPriceAsPerDftTravelStandardSwitch(corpTravelConfig.getJustLoadLowestPriceAsPerDftTravelStandardSwitch());
            businessConfigVO.setExcludeLimitedDftLowestPriceSwitch(corpTravelConfig.getExcludeLimitedDftLowestPriceSwitch());
        } else{
            //新增国内机票货架控制的开关参数
            businessConfigVO.setJustLoadLowestPriceAsPerDftTravelStandardSwitch(GlobalConstant.Switch.OFF);
            businessConfigVO.setExcludeLimitedDftLowestPriceSwitch(GlobalConstant.Switch.OFF);
        }
        if(changeDefaultConfigOfKssl( p , corpMemberGeneralVO)){
            businessConfigVO.setTmc(ProductShelf.PERSONAL_SHELF.getCode());
        }else if (Objects.nonNull(corpMemberGeneralVO)) {
            businessConfigVO.setTmc(Objects.nonNull(corpMemberGeneralVO.getDefaultProductShelf()) ? corpMemberGeneralVO.getDefaultProductShelf() : ProductShelf.PERSONAL_SHELF.getCode());
        }
        return businessConfigVO;
    }

    private boolean changeDefaultConfigOfKssl(String p, CorpMemberGeneralVO corpMemberGeneralVO) {
        if(Objects.isNull(p) || Objects.isNull(corpMemberGeneralVO.getSourceChannel())){
            return false;
        }
        if(!corpMemberGeneralVO.getSourceChannel().equals(CorpInfo.SourceChannel.CAISSA.getCode()) && !corpMemberGeneralVO.getSourceChannel().equals(CorpInfo.SourceChannel.HUOLI_CAISSA.getCode())){
            return false;
        }
        if(p.contains(AppName.KSSL.getCode()) || p.contains(AppName.HLSL.getCode())){
            return false;
        }
        return true;
    }

    private CorpMemberGeneralVO queryCorpMemberGeneralInfo(String phoneIdOfBookedBy, BizId bizId) {
        List<String> showColumns = new ArrayList<>();
        showColumns.add("corpInfoSid");
        showColumns.add("serviceAllowedPayOfCorp");
        showColumns.add("sid");

        return corpMemberTmcCommonService.queryCorpMember(phoneIdOfBookedBy, null, bizId.getCode(), showColumns);
    }

    private String getSupportServiceIds() {
        StringBuilder supportServiceNames = new StringBuilder();
        ServiceId[] serviceIds = ServiceId.values();
        for (ServiceId serviceId : serviceIds) {
            if (Objects.isNull(serviceId.getBizId())) {
                continue;
            }
            if (supportServiceNames.length() > 0) {
                supportServiceNames.append("、");
            }
            supportServiceNames.append(serviceId.getName());
        }
        return supportServiceNames.toString();
    }
}
