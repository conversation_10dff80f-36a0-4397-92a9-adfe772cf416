package com.huoli.ctar.cms.ctm.processing.mobile.service.impl;


import com.huoli.ctar.cms.ctm.processing.mobile.service.IftTravelRequestProcessingService;

import com.huoli.ctar.core.infra.exception.ServiceException;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.CorpMember;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.CorpMemberTravelConfig;
import com.huoli.ctar.tmccbs.biz.domain.cms.ctm.tam.entity.BaseTravelRequest;
import com.huoli.ctar.tmccbs.biz.domain.cms.ctm.tbm.entity.BaseTravelBooking;
import com.huoli.ctar.tmccbs.biz.domain.constant.ServiceId;
import com.huoli.ctar.tmccbs.biz.domain.exception.TmcCommonValidationException;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.TravelProcessingResultVO;
import com.huoli.ctar.tmccbs.cms.basic.ctm.tam.service.internal.proxy.IftTravelRequestProxyInternalService;
import com.huoli.ctar.tmccbs.cms.basic.ctm.tbm.service.internal.proxy.IftTravelBookingProxyInternalService;
import com.huoli.ctar.tmccbs.cms.cam.tmc.common.service.CorpBaseValidationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service("iftTravelRequestProcessingService")
public class IftTravelRequestProcessingServiceImpl extends BaseTravelRequestProcessingService implements IftTravelRequestProcessingService {

    @Autowired
    @Qualifier("corpBaseValidationService")
    private CorpBaseValidationService corpBaseValidationService;

    @Autowired
    @Qualifier("iftTravelRequestProxyInternalService")
    private IftTravelRequestProxyInternalService iftTravelRequestProxyInternalService;

    @Autowired
    @Qualifier("iftTravelBookingProxyInternalService")
    protected IftTravelBookingProxyInternalService iftTravelBookingProxyInternalService;

    @Override
    protected ServiceId getServiceId() {
        return ServiceId.INTERNATIONAL_FLIGHT_TICKET;
    }

    @Override
    public TravelProcessingResultVO travelProcessing(String phoneIdOfBookedBy, Long travelBookingId) {
        CorpMember bookedBy;
        try {
            bookedBy = corpBaseValidationService.checkCorpMember(phoneIdOfBookedBy);
        } catch (TmcCommonValidationException e) {
            log.error(String.format("预定人校验失败, phoneId: %s, 错误消息: %s",
                    phoneIdOfBookedBy, e.getMessage()), e);
            throw new ServiceException(e.getCode(), e.getDisplayMsg(), e);
        }
        //预订人差旅配置
        CorpMemberTravelConfig bookedByTravelConfig = corpBaseValidationService.getCorpMemberTravelConfig(bookedBy.getSid());
        //差旅预订信息
        BaseTravelBooking travelBooking = iftTravelBookingProxyInternalService.findBySid(travelBookingId, bookedBy.isPlatformMember());
        //获取差旅预订对应的差旅申请
        BaseTravelRequest travelRequest = null;
        if (Objects.nonNull(travelBooking) && Objects.nonNull(travelBooking.getTravelRequestSid())) {
            travelRequest = iftTravelRequestProxyInternalService.findBySid(travelBooking.getTravelRequestSid(), bookedBy.isPlatformMember());
        }
        //差旅预订处理，返回处理结果编码
        return travelProcessing(bookedBy, bookedByTravelConfig, travelBooking, travelRequest);
    }
}
