package com.huoli.ctar.cms.ctm.processing.mobile.service.impl;


import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.exception.ServiceException;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.CorpInfo;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.CorpMember;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.CorpMemberTravelConfig;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.PaymentConfig;
import com.huoli.ctar.tmccbs.biz.domain.cms.ctm.tam.entity.BaseTravelRequest;
import com.huoli.ctar.tmccbs.biz.domain.cms.ctm.tbm.entity.BaseTravelBooking;
import com.huoli.ctar.tmccbs.biz.domain.cms.ctm.tsm.constant.TreatmentOnTravelStandardViolation;
import com.huoli.ctar.tmccbs.biz.domain.constant.ServiceId;
import com.huoli.ctar.tmccbs.biz.domain.constant.TravelProcessingCode;
import com.huoli.ctar.tmccbs.biz.domain.model.ServiceAllowedPay;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.TravelProcessingResultVO;
import com.huoli.ctar.tmccbs.cms.basic.cam.service.internal.CorpInfoInternalService;
import com.huoli.ctar.tmccbs.cms.cam.tmc.common.service.CorpBaseValidationService;
import com.huoli.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public abstract class BaseTravelRequestProcessingService {

    @Autowired
    @Qualifier("corpBaseValidationService")
    private CorpBaseValidationService corpBaseValidationService;

    @Autowired
    @Qualifier("corpInfoInternalService")
    private CorpInfoInternalService corpInfoInternalService;

    protected abstract ServiceId getServiceId();

    protected TravelProcessingResultVO travelProcessing(CorpMember corpMember, CorpMemberTravelConfig bookedByTravelConfig, BaseTravelBooking travelBooking, BaseTravelRequest travelRequest) {
        TravelProcessingResultVO travelProcessingResultVO = new TravelProcessingResultVO();

        if (Objects.isNull(travelBooking)) {
            travelProcessingResultVO.setProcessingCode(TravelProcessingCode.BOOKING_NOT_EXIST.getCode());
            return travelProcessingResultVO;
        }

        PaymentConfig paymentConfig = corpBaseValidationService.getPaymentConfig(corpMember.getCorpInfoSid());
        if (Objects.isNull(paymentConfig) || StringUtil.isEmpty(paymentConfig.getServiceAllowedPay())) {
            travelProcessingResultVO.setProcessingCode(TravelProcessingCode.NOT_OPENED_BIZ.getCode());
            return travelProcessingResultVO;
        }


        String bookedByServiceAllowedPay = Objects.nonNull(bookedByTravelConfig) ? bookedByTravelConfig.getServiceAllowedPay() : null;
        if (StringUtil.isEmpty(bookedByServiceAllowedPay)) {
            bookedByServiceAllowedPay = paymentConfig.getServiceAllowedPay();
        }

        List<String> serviceIds = ServiceAllowedPay.getSvcIds(bookedByServiceAllowedPay);
        if (CollectionUtils.isEmpty(serviceIds) || !serviceIds.contains(getServiceId().getCode())) {
            travelProcessingResultVO.setProcessingCode(TravelProcessingCode.NOT_OPENED_BIZ.getCode());
            return travelProcessingResultVO;
        }


        //差旅超标处理配置
        //TravelTempVO travelTempVO = findTravelTempByPhoneId(corpMember.getSid());
        //TreatmentOnTravelStandardViolation travelStandardViolation = getTreatmentOnTravelStandardViolation(getServiceId().getCode(), travelTempVO);
        TreatmentOnTravelStandardViolation travelStandardViolation = TreatmentOnTravelStandardViolation.getByCode(travelBooking.getTreatmentOnViolation());

        //差旅预订处理，返回处理结果编码
        TravelProcessingCode travelProcessingCode = travelBookingHandle(corpMember, travelBooking, travelRequest, travelStandardViolation);


        travelProcessingResultVO.setTravelBookingId(travelBooking.getSid());
        travelProcessingResultVO.setTravelRequestId(travelBooking.getTravelRequestSid());
        travelProcessingResultVO.setProcessingCode(Objects.nonNull(travelProcessingCode) ? travelProcessingCode.getCode() : null);
        return travelProcessingResultVO;
    }

    /**
     * 差旅预订处理
     *
     * @param travelBooking           差旅预订信息
     * @param travelStandardViolation 差旅超标处理方式
     * @return 处理结果编码
     */
    protected TravelProcessingCode travelBookingHandle(CorpMember corpMember, BaseTravelBooking travelBooking, BaseTravelRequest travelRequest, TreatmentOnTravelStandardViolation travelStandardViolation) {
        boolean isViolation = GlobalConstant.FLAG_YES_VALUE.equals(travelBooking.getViolationFlag());
        boolean isApprove = GlobalConstant.FLAG_YES_VALUE.equals(travelBooking.getApprovalFlag());

        CorpInfo corpInfo = corpInfoInternalService.findBySid(corpMember.getCorpInfoSid(), Collections.singletonList("corpType"));
        if (Objects.isNull(corpInfo)) {
            throw new ServiceException(String.format("未找到成员[%s]的企业信息！", corpMember.getPhoneId()));
        }


        TravelProcessingCode travelProcessingCode = null;
        //违规且不需要审批
        if (isViolation && !isApprove) {
            if (Objects.equals(TreatmentOnTravelStandardViolation.PROVIDE_REASON, travelStandardViolation) || Objects.equals(TreatmentOnTravelStandardViolation.MIX_PAY, travelStandardViolation)) {
                travelProcessingCode = TravelProcessingCode.PROPOSE_VIOLATION;
            } else if (Objects.equals(corpInfo.getCorpType(), CorpInfo.CorpType.PRIVATE_PAYMENT_REIMBURSEMENT.getId())) {
                //个人现付强制性违规页
                travelProcessingCode = TravelProcessingCode.PRIVATE_PAYMENT_MANDATORY_VIOLATION;
            } else {
                travelProcessingCode = TravelProcessingCode.MANDATORY_VIOLATION;
            }
        }
        //违规且需审批
        else if (isViolation && isApprove) {
            //强制性违规
            if (Objects.equals(TreatmentOnTravelStandardViolation.DISALLOW_CORP_PAY, travelStandardViolation)) {
                if (Objects.equals(corpInfo.getCorpType(), CorpInfo.CorpType.PRIVATE_PAYMENT_REIMBURSEMENT.getId())) {
                    //个人现付强制性违规页
                    travelProcessingCode = TravelProcessingCode.PRIVATE_PAYMENT_MANDATORY_VIOLATION;
                } else {
                    travelProcessingCode = TravelProcessingCode.MANDATORY_VIOLATION;
                }
            }
            //建议性违规,未提交过审批申请或审批为失效状态（拒绝、已支付、超时、取消）
            else if ((Objects.equals(TreatmentOnTravelStandardViolation.PROVIDE_REASON, travelStandardViolation) || Objects.equals(TreatmentOnTravelStandardViolation.MIX_PAY, travelStandardViolation))
                    && (Objects.isNull(travelRequest) || travelRequest.isInvalid())) {
                if (Objects.equals(corpInfo.getCorpType(), CorpInfo.CorpType.PRIVATE_PAYMENT_REIMBURSEMENT.getId())) {
                    //个人现付建议性违规及提交审批页
                    travelProcessingCode = TravelProcessingCode.PRIVATE_PAYMENT_PROPOSE_VIOLATION_AND_SUBMIT_FOR_APPROVAL;
                } else {
                    travelProcessingCode = TravelProcessingCode.PROPOSE_VIOLATION_AND_SUBMIT_FOR_APPROVAL;
                }
            }
            //建议性违规且提交过审批，审批状态为待审批状态（申请）
            else if ((Objects.equals(TreatmentOnTravelStandardViolation.PROVIDE_REASON, travelStandardViolation) || Objects.equals(TreatmentOnTravelStandardViolation.MIX_PAY, travelStandardViolation))
                    && Objects.nonNull(travelRequest)
                    && travelRequest.isApprovalPending()) {
                travelProcessingCode = TravelProcessingCode.SAME_TRIP;
            }
        }
        //不违规且需要审批
        else if (!isViolation && isApprove) {
            //未提交过审批申请或审批状态为失效状态（拒绝、已支付、超时、取消）
            if (Objects.isNull(travelRequest) || travelRequest.isInvalid()) {
                travelProcessingCode = TravelProcessingCode.SUBMIT_FOR_APPROVAL;
            }
            //已审批，审批状态为待审批状态（申请）
            else if (Objects.nonNull(travelRequest) && travelRequest.isApprovalPending()) {
                travelProcessingCode = TravelProcessingCode.SAME_TRIP;
            }
        }
        return travelProcessingCode;
    }
}
