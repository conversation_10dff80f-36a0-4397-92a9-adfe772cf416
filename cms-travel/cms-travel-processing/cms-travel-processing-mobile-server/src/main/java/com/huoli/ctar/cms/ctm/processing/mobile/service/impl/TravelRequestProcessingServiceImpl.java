package com.huoli.ctar.cms.ctm.processing.mobile.service.impl;


import com.huoli.ctar.cms.ctm.processing.mobile.service.*;
import com.huoli.ctar.cms.ctm.processing.mobile.service.DomesticFlightTicketTravelRequestProcessingService;

import com.huoli.ctar.tmccbs.biz.domain.constant.ServiceId;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.TravelProcessingRequestParam;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.TravelProcessingResultVO;
import com.huoli.ctar.tmccbs.cms.ctm.common.service.TravelSupportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service("travelRequestProcessingService")
public class TravelRequestProcessingServiceImpl implements TravelRequestProcessingService, TravelSupportService {
    @Autowired
    @Qualifier("domesticFlightTicketTravelRequestProcessingService")
    private DomesticFlightTicketTravelRequestProcessingService domesticFlightTicketTravelRequestProcessingService;

    @Autowired
    @Qualifier("trainTicketTravelRequestProcessingService")
    private TrainTicketTravelRequestProcessingService trainTicketTravelRequestProcessingService;

    @Autowired
    @Qualifier("hotelTravelRequestProcessingService")
    private HotelTravelRequestProcessingService hotelTravelRequestProcessingService;

    @Autowired
    @Qualifier("carTravelRequestProcessingService")
    private CarTravelRequestProcessingService carTravelRequestProcessingService;

    @Autowired
    @Qualifier("iftTravelRequestProcessingService")
    private IftTravelRequestProcessingService iftTravelRequestProcessingService;

    @Override
    public TravelProcessingResultVO travelProcessing(TravelProcessingRequestParam travelProcessingRequestParam) {
        ServiceId serviceId = getAndCheckServiceId(travelProcessingRequestParam.getServiceId());
        TravelProcessingResultVO travelProcessingResultVO = null;
        switch (serviceId) {
            case DOMESTIC_FLIGHT_TICKET:
                travelProcessingResultVO = domesticFlightTicketTravelRequestProcessingService.travelProcessing(travelProcessingRequestParam.getPhoneid(), travelProcessingRequestParam.getTravelBookingId());
                break;
            case TRAIN_TICKET:
                travelProcessingResultVO = trainTicketTravelRequestProcessingService.travelProcessing(travelProcessingRequestParam.getPhoneid(), travelProcessingRequestParam.getTravelBookingId());
                break;
            case HOTEL:
                travelProcessingResultVO = hotelTravelRequestProcessingService.travelProcessing(travelProcessingRequestParam.getPhoneid(), travelProcessingRequestParam.getTravelBookingId());
                break;
            case CAR:
                travelProcessingResultVO = carTravelRequestProcessingService.travelProcessing(travelProcessingRequestParam.getPhoneid(), travelProcessingRequestParam.getTravelBookingId());
                break;
            case INTERNATIONAL_FLIGHT_TICKET:
                travelProcessingResultVO = iftTravelRequestProcessingService.travelProcessing(travelProcessingRequestParam.getPhoneid(), travelProcessingRequestParam.getTravelBookingId());
                break;
        }
        return travelProcessingResultVO;
    }
}
