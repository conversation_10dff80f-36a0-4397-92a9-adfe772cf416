package com.huoli.ctar.cms.tmc.mall.toc.service;

import com.huoli.ctar.biz.domain.mall.toc.CommodityVO;
import com.huoli.ctar.biz.domain.mall.toc.admin.CreateCommodityRequest;
import com.huoli.ctar.biz.domain.mall.toc.admin.UpdateCommodityRequest;
import com.huoli.ctar.core.infra.model.PageWrapper;
import com.huoli.ctar.tmccbs.biz.domain.mall.toc.entity.Commodity;
import com.huoli.ctar.tmccbs.biz.domain.mall.toc.vo.TmcCommodityOrderVO;
import com.huoli.ctar.tmccbs.biz.domain.mall.toc.vo.TmcCommodityProfitInfo;

import java.util.List;

public interface CommodityService {

    Commodity createCommodity(CreateCommodityRequest request);

    void updateCommodity(Long sid, UpdateCommodityRequest request);

    PageWrapper<List<CommodityVO>> loadCommoditysByPage(String name, String status, Integer pageNo, Integer pageSize);

    CommodityVO getCommodityDetail(Long sid);

    PageWrapper<List<TmcCommodityOrderVO>> loadOrderInfos(String orderId, String status, Integer pageNo, Integer pageSize);

    TmcCommodityProfitInfo statisProfit(String month);

    String exportStatistics(String startTime, String endTime);
}
