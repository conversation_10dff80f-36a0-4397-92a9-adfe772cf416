package com.huoli.ctar.cpcms.pbm.mobile.service.impl;


import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.cpcms.pbm.mobile.service.PccIftTravelTripInfoMobileService;
import com.huoli.ctar.tmccbs.biz.domain.cpcms.ptm.pbm.entity.PccIftTravelTripDetail;
import com.huoli.ctar.tmccbs.biz.domain.cpcms.ptm.pbm.entity.PccIftTravelTripInfo;
import com.huoli.ctar.tmccbs.biz.domain.cpcms.ptm.pbm.entity.PccTravelStandardViolationMsg;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.InFlightTicketTravelTripDetailMobileVO;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.InFlightTicketTravelTripInfoMobileVO;
import com.huoli.ctar.tmccbs.cpcms.basic.ptm.pbm.service.db.PccTravelStandardViolationMsgDbService;
import com.huoli.ctar.tmccbs.cpcms.basic.ptm.pbm.service.internal.PccIftTravelTripDetailInternalService;
import com.huoli.ctar.tmccbs.cpcms.basic.ptm.pbm.service.internal.PccIftTravelTripInfoInternalService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("pccIftTravelTripInfoMobileService")
public class PccIftTravelTripInfoMobileServiceImpl implements PccIftTravelTripInfoMobileService {

    @Autowired
    @Qualifier("pccIftTravelTripInfoInternalService")
    private PccIftTravelTripInfoInternalService pccIftTravelTripInfoInternalService;

    @Autowired
    @Qualifier("pccIftTravelTripDetailInternalService")
    private PccIftTravelTripDetailInternalService pccIftTravelTripDetailInternalService;

    @Autowired
    @Qualifier("pccTravelStandardViolationMsgDbService")
    private PccTravelStandardViolationMsgDbService pccTravelStandardViolationMsgDbService;

    @Override
    public InFlightTicketTravelTripInfoMobileVO retrieveIftTravelTripInfo(Long travelBookingId, String approveManner) {
        PccIftTravelTripInfo tripInfo = pccIftTravelTripInfoInternalService.findByTravelBookingSid(travelBookingId);
        if (Objects.isNull(tripInfo)) {
            return new InFlightTicketTravelTripInfoMobileVO();
        }

        List<PccIftTravelTripDetail> travelTripDetails = pccIftTravelTripDetailInternalService.findByTripInfoSid(tripInfo.getSid());
        if (CollectionUtils.isEmpty(travelTripDetails)) {
            return new InFlightTicketTravelTripInfoMobileVO(tripInfo);
        }
        InFlightTicketTravelTripInfoMobileVO inFlightTicketTravelTripInfoMobileVO = new InFlightTicketTravelTripInfoMobileVO(tripInfo, travelTripDetails);

        inFlightTicketTravelTripInfoMobileVO.getTravelTripDetails().forEach(this::fillTravelStandardViolationMsgs);

        return inFlightTicketTravelTripInfoMobileVO;
    }

    private void fillTravelStandardViolationMsgs(InFlightTicketTravelTripDetailMobileVO inFlightTicketTravelTripDetailMobileVO) {
        // 差标违规信息
        final List<String> travelStandardViolationMsgs = new ArrayList<>();
        final Map<String, Object> params = QueryParamBuilder.get()
                .param("travelTripDetailSid", inFlightTicketTravelTripDetailMobileVO.getId())
                .build();

        final List<PccTravelStandardViolationMsg> violationMsgs = pccTravelStandardViolationMsgDbService.findByParams(params);

        if (CollectionUtils.isNotEmpty(violationMsgs)) {
            for (PccTravelStandardViolationMsg violationMsg : violationMsgs) {
                travelStandardViolationMsgs.add(violationMsg.getContent());
            }
        }

        inFlightTicketTravelTripDetailMobileVO.setTravelStandardViolationMsgs(travelStandardViolationMsgs);
    }
}
