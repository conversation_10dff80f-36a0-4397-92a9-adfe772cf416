package com.huoli.ctar.cpcms.pbm.mobile.service.impl;


import com.huoli.ctar.cpcms.pbm.mobile.service.PccTravelTripInfoMobileService;
import com.huoli.ctar.tmccbs.biz.domain.cpcms.ptm.pbm.entity.PccTravelStandardViolationMsg;
import com.huoli.ctar.tmccbs.biz.domain.cpcms.ptm.pbm.entity.PccTravelTripDetail;
import com.huoli.ctar.tmccbs.biz.domain.cpcms.ptm.pbm.entity.PccTravelTripInfo;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.TravelTripDetailMobileVO;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.TravelTripInfoMobileVO;
import com.huoli.ctar.tmccbs.cpcms.basic.ptm.pbm.service.db.PccTravelStandardViolationMsgDbService;
import com.huoli.ctar.tmccbs.cpcms.basic.ptm.pbm.service.internal.PccTravelTripDetailInternalService;
import com.huoli.ctar.tmccbs.cpcms.basic.ptm.pbm.service.internal.PccTravelTripInfoInternalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

@Service("pccTravelTripInfoMobileService")
public class PccTravelTripInfoMobileServiceImpl implements PccTravelTripInfoMobileService {

    @Autowired
    @Qualifier("pccTravelTripInfoInternalService")
    private PccTravelTripInfoInternalService pccTravelTripInfoInternalService;

    @Autowired
    @Qualifier("pccTravelTripDetailInternalService")
    private PccTravelTripDetailInternalService pccTravelTripDetailInternalService;

    @Autowired
    @Qualifier("pccTravelStandardViolationMsgDbService")
    private PccTravelStandardViolationMsgDbService pccTravelStandardViolationMsgDbService;

    @Override
    public TravelTripInfoMobileVO retrieveTravelTripInfo(Long travelBookingId, BigDecimal maxDiscountBooked, String approveManner) {
        maxDiscountBooked = Objects.isNull(maxDiscountBooked) ? new BigDecimal("-1") : maxDiscountBooked;

        TravelTripInfoMobileVO travelTripInfo = new TravelTripInfoMobileVO();

        PccTravelTripInfo tripInfo = pccTravelTripInfoInternalService.findByTravelBookingSid(travelBookingId);
        if (Objects.isNull(tripInfo)) {
            return new TravelTripInfoMobileVO();
        }

        List<PccTravelTripDetail> travelTripDetails = pccTravelTripDetailInternalService.findByTripInfoSid(tripInfo.getSid());
        if (CollectionUtils.isEmpty(travelTripDetails)) {
            return new TravelTripInfoMobileVO(tripInfo);
        }

        TravelTripInfoMobileVO travelTripInfoMobileVO = new TravelTripInfoMobileVO(tripInfo, travelTripDetails, maxDiscountBooked);
        travelTripInfoMobileVO.getTravelTripDetails().forEach(this::fillTravelStandardViolationMsgs);
        return travelTripInfo;
    }

    private void fillTravelStandardViolationMsgs(TravelTripDetailMobileVO travelTripDetailMobileVO) {
        // 差标违规信息
        List<String> travelStandardViolationMsgs = new ArrayList<String>();
        Map<String, Object> params = new HashMap<>();
        params.put("travelTripDetailSid", travelTripDetailMobileVO.getId());

        List<PccTravelStandardViolationMsg> violationMsgs = pccTravelStandardViolationMsgDbService.findByParams(params);

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(violationMsgs)) {
            for (PccTravelStandardViolationMsg violationMsg : violationMsgs) {
                travelStandardViolationMsgs.add(violationMsg.getContent());
            }
        }

        travelTripDetailMobileVO.setTravelStandardViolationMsgs(travelStandardViolationMsgs);
    }
}
