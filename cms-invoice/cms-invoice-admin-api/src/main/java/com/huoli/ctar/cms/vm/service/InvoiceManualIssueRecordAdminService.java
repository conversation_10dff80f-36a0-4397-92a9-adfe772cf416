package com.huoli.ctar.cms.vm.service;

import com.huoli.ctar.biz.domain.cms.vm.vo.InvoiceETicketVO;
import com.huoli.ctar.biz.domain.cms.vm.vo.InvoiceManualIssueRecordVO;
import com.huoli.ctar.biz.domain.cms.vm.vo.InvoicePackRequest;
import com.huoli.ctar.biz.domain.cms.vm.vo.UpdateInvoiceManualIssueRecordVO;
import com.huoli.ctar.core.infra.model.PageWrapper;
import com.huoli.ctar.tmccbs.biz.domain.cms.vm.vo.PostInfoNew;
import com.huoli.ctar.tmccbs.biz.domain.constant.IntervalOfMonth;

import java.util.List;

public interface InvoiceManualIssueRecordAdminService {

    PageWrapper<List<InvoiceManualIssueRecordVO>> loadInvoiceManualIssueRecordsByPage(
            Long corpId, String invoiceStatus, String month, int type, String bizType, String orderId, Integer pageNo, Integer pageSize);

    void madeInvoice(PostInfoNew postInfoNew);

    void packingInvoice(InvoicePackRequest invoicePackRequest);

    void cancelInvoice(Long invoiceId);

    void getInvoiceTitle();

    void updateInvoiceManualIssueRecord(Long sid, UpdateInvoiceManualIssueRecordVO updateInvoiceManualIssueRecordVO);

    void syncInvoiceStatus(Long corpId, String month, IntervalOfMonth intervalOfMonth);


    void syncTrainETicket(Long corpId, String monthToSettle, IntervalOfMonth intervalOfMonth);

    InvoiceETicketVO getInvoiceETicket(Long id);

}
