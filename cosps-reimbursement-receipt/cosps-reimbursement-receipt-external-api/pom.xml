<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>cosps-reimbursement-receipt</artifactId>
    <groupId>com.huoli.ctar</groupId>
    <version>1.0.1</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>cosps-reimbursement-receipt-external-api</artifactId>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar.common</groupId>
      <artifactId>common-utils</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>cosps-omh-api</artifactId>
    </dependency>

    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger2</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar.core</groupId>
      <artifactId>core-infra</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-biz-domain</artifactId>
    </dependency>
  </dependencies>

</project>