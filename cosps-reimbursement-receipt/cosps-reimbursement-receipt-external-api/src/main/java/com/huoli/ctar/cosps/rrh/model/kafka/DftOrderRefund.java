package com.huoli.ctar.cosps.rrh.model.kafka;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 国内机票推送kafka消息订单退款信息
 */
@Getter
@Setter
public class DftOrderRefund {
    /**
     * 退票单id
     */
    private String rid;

    /**
     * 订单id
     */
    private String oid;

    /**
     * 退票类型 0：自愿退票 1：非自愿退票
     */
    private Integer type;

    /**
     * 审核状态 0：待审核 1：审核通过 3：申请待审核 4： 忽略退款 5：退票单取消
     */
    private Integer auditStatus;

    /**
     * 来源
     */
    private String source;

    /**
     * 不向用户发送短信 0:否，1: 是
     */
    private Integer noSms;

    /**
     * 不向用户退款 0:否，1: 是
     */
    private Integer noMoney;

    /**
     * 退单时限
     */
    private String limitDate;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 退票明细
     */
    private List<DftOrderRefundDetail> refundDetails;
}
