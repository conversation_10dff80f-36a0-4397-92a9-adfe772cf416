package com.huoli.ctar.cosps.rrh.model.kafka;

import lombok.Getter;
import lombok.Setter;

/**
 * 国内机票推送kafka消息订单规则信息
 */
@Getter
@Setter
public class DftOrderRule {
    /**
     * 退改签规则
     */
    private String rule;

    /**
     * 外部退改签规则(注：简略版rule，显示使用)
     */
    private String outRule;

    /**
     * 退票标识
     */
    private Integer refundFlag;

    /**
     * 签转标识
     */
    private Integer chgFlag;

    /**
     * 改签标识
     */
    private Integer reschdFlag;

    /**
     * 退票规则
     */
    private String ruleRefund;

    /**
     * 改期规则
     */
    private String ruleChange;

    /**
     * 签转规则
     */
    private String ruleChg;

    /**
     * 改期后退票规则
     */
    private String ruleChangeRefund;

    /**
     * 规则备注（如最低手续费信息）
     */
    private String ruleRemark;

    /**
     * 改期表达式
     */
    private String changeExpression;

    /**
     * 退票表达式
     */
    private String refundExpression;

    /**
     * 行李额规则
     */
    private String baggageRule;

    /**
     * 行李额（单位KG）
     */
    private Integer baggageQuota;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
