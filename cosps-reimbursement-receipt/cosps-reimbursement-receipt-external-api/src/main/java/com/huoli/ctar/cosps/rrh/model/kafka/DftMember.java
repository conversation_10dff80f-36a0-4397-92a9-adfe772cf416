package com.huoli.ctar.cosps.rrh.model.kafka;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 国内机票推送kafka消息会员信息
 */
@Getter
@Setter
public class DftMember {
    /**
     * 会员名称
     */
    private String name;
    /**
     * 会员等级
     */
    private int vipGrade;
    /**
     * 是否购买会员卡 0:无需购买，1：购买
     */
    private int buy;
    /**
     * 会员卡销售金额
     */
    private BigDecimal price;
    /**
     * 会员优惠立减金额
     */
    private BigDecimal discount;
    /**
     * 会员返现金额
     */
    private BigDecimal fx;
    /**
     * 0：充值可提现余额，1：充值积分，2：充值不可提现余额
     */
    private int fxFlag;
    /**
     * 让利金额
     */
    private BigDecimal discAmount;
}
