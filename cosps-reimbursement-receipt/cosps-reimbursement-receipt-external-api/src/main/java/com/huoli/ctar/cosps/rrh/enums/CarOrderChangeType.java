package com.huoli.ctar.cosps.rrh.enums;


import com.huoli.ctar.core.infra.constant.BaseEnum;

import java.util.Objects;

public enum CarOrderChangeType implements BaseEnum {
    ORDER_SETTLE_FINISHED(1, "order_settle_finished", "结算完成"),
    ORDER_REFUND(2, "order_refund", "订单退款"),
    ORDER_MAKE_UP_DIFF(3, "order_make_up_diff", "订单补款")
    ;

    private final Integer id;

    private final String code;

    private final String desc;

    CarOrderChangeType(Integer id, String code, String desc) {
        this.id = id;
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return this.code;
    }


    public Integer getId() {
        return this.id;
    }

    public static CarOrderChangeType getById(Integer id) {
        CarOrderChangeType[] values = CarOrderChangeType.values();
        for (CarOrderChangeType type : values) {
            if (Objects.equals(type.getId(), id)) {
                return type;
            }
        }
        return null;
    }
}
