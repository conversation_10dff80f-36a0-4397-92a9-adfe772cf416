package com.huoli.ctar.cosps.rrh.enums;

import com.huoli.ctar.core.infra.constant.BaseEnum;

public enum InvoiceType implements BaseEnum {

    FULL_INVOICE(1, "full_invoice", "全额发票"),
    BALANCE_INVOICE(2, "balance_invoice", "差额发票");

    private final Integer id;

    private final String code;

    private final String name;

    InvoiceType(Integer id, String code, String name) {
        this.id =  id;
        this.code = code;
        this.name = name;
    }

    public Integer getId() {
        return this.id;
    }

    @Override
    public String getCode() {
        return this.code;
    }
}
