package com.huoli.ctar.cosps.rrh.model.kafka;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DftClientData {
    /**
     * 设备id
     */
    private String uid;

    /**
     * 会员id
     */
    private long phoneid;

    /**
     * p参数 来源,操作系统, 操作系统版本,程序名,客户端版本,设备类型,是否越狱(是否root，是否解锁)  例如:p=appstore,ios,6.0,hbgj,4.0,iphone4.1,0
     */
    private String p;

    /**
     * 客户端版本号
     */
    private String cver;

    /**
     * 客户端平台，iphone|iphonepro|android|wp
     */
    private String client;

    /**
     * 设备标识
     */
    private String uuid;

    /**
     * 来源
     */
    private String source;

    /**
     * 设备类型
     */
    private String platform;

    /**
     * 移动设备国际辨识码
     */
    private String imei;

    /**
     * 客户端基础版本号
     */
    private String dver;

    /**
     * 客户端机场数据版本号
     */
    private String iver;

    /**
     * 公共验证参数
     */
    private String verify;

    /**
     * 纬度数据 base64加密
     */
    private String la;

    /**
     * 经度数据 base64加密
     */
    private String lo;

    /**
     * 连接模式：wifi/2G/3G/4G
     */
    private String linkmode;

    /**
     * 连接代码，在wifi下可以不传
     */
    private String linkcode;

    /**
     * 是否企业用户
     */
    private boolean corpUser;

    /**
     * 企业id
     */
    private long corpId;

    /**
     * 是否常旅客卡
     */
    private boolean ffpcard;

    /**
     * 系统时间
     */
    private long systemtime;

    /**
     * 是否为beta版本
     */
    private String beta;

    /**
     * 系统平台 如：hbgj，gtgj，hllx，web
     */
    private String system = "hbgj";

    /**
     * 语音验证码
     */
    private String speech;

    /**
     * 屏幕信息(对于老版本的没有传，此数据可能为空)，包含width,height,scale例如414,736,3
     */
    private String screen;

    /**
     * 用户IP
     */
    private String ip;

    /**
     * 服务器id
     */
    private String servercode;

    /**
     * 业务数据埋点
     */
    private String analyseSourceEntry;

    /**
     * 是否pro版本，1：是，0：否
     */
    private int pro;

    /**
     * 企业tmc标示 0：未知，1：企业，2：个人
     */
    private Integer tmc;
}
