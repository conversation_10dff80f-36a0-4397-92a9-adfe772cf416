package com.huoli.ctar.cosps.rrh.model.kafka;

import lombok.Getter;
import lombok.Setter;


/**
 * 国内机票推送kafka消息订单航程信息
 */
@Getter
@Setter
public class DftOrderSegment {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 航程序号 1,2,3...
     */
    private Integer tripNum;

    /**
     * 航段序号 1,2,3...
     */
    private Integer segNum;

    /**
     * 航段类型 OB：去程 IB：回程
     */
    private String segType;

    /**
     * 航空公司代码
     */
    private String acCode;

    /**
     * 航班号
     */
    private String flyNo;

    /**
     * 出发机场三字码
     */
    private String dep;

    /**
     * 到达机场三字码
     */
    private String arr;

    /**
     * 出发时间
     */
    private String depTime;

    /**
     * 到达时间
     */
    private String arrTime;

    /**
     * 出发地航站楼
     */
    private String depTerminal;

    /**
     * 到达地航站楼
     */
    private String arrTerminal;

    /**
     * 准点率
     */
    private String proximateRate;

    /**
     * 定座仓位
     */
    private String cabin;

    /**
     * 基础仓位等级
     */
    private String baseCabin;

    /**
     * 机型
     */
    private String model;

    /**
     * 经停点数量 0：不经停
     */
    private Integer stopNum;

    /**
     * 经停信息（Json格式 site：地点 time：时间 ）
     */
    private String stopInfo;

    /**
     * 是否是共享航班 0：否 1：是
     */
    private Integer isShare;

    /**
     * 实际承运航班号
     */
    private String carrierFlyNo;

    /**
     * 承运航司
     */
    private String carrierAirline;

    /**
     * 销售航司
     */
    private String sellAirline;

    /**
     * 是否有餐食 0：无 1：有
     */
    private Integer meal;

    /**
     * 是否有效 0：无效 1：有效 默认1
     */
    private Integer valid;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
