package com.huoli.ctar.cosps.rrh.model.vo;

import com.huoli.ctar.cfesag.external.sys.api.post.service.constans.VoucherSubType;
import com.huoli.ctar.cfesag.external.sys.api.post.service.model.InvoiceInfo;
import com.huoli.ctar.cfesag.external.sys.api.post.service.model.VoucherInfo;
import com.huoli.ctar.common.utils.DateUtil;
import com.huoli.ctar.cosps.rrh.enums.InvoiceType;
import com.huoli.ctar.tmccbs.biz.domain.cosps.rrh.constant.VoucherType;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.*;

@Getter
@Setter
public class DftOrderSummitPackageVO {
    /**
     * 订单对应的结算周期开始时间
     */
    private Date calStartDate;

    /**
     * 订单对应的结算周期结束时间
     */
    private Date calEndDate;

    /**
     * 结算周期日
     */
    private Date scheduleDate;

    /**
     * 订单对应的包裹邮寄时间
     */
    private Date postTime;

    /**
     * 当前订单
     */
    private String orderId;

    /**
     * 预订人
     */
    private String bookedBy;

    /**
     * 业务变更类型
     */
    private String orderChangeType;

    /**
     * 业务变更时间
     */
    private Date orderChangeTime;


    /**
     * 邮寄任务
     * key:任务对应订单号（出票时）
     */
    private Map<String, PostTask> postTaskMap = new HashMap<>();


    @Getter
    @Setter
    public static class PostTask {
        /**
         * 邮寄包裹ID
         */
        private String postId;

        /**
         * 邮寄任务原订单号
         */
        private String orderId;

        /**
         * 订单维度报销邮寄任务
         */
        private List<DftPackageOrderPostTask> orderPostTasks = new ArrayList<>();

        /**
         * 订单客票维度报销邮寄任务
         */
        private List<DftPackageOrderTicketPostTask> ticketPostTasks = new ArrayList<>();
    }

    @Getter
    @Setter
    public static class DftPackageOrderPostTask {
        /**
         * 邮寄详情id
         */
        private Long detailId;

        /**
         * 首单订单号
         */
        private String firstOrderId;

        /**
         * 报销凭证类型
         */
        private VoucherType voucherType;

        /**
         * 报销凭证自类型
         */
        private VoucherSubType voucherSubType;

        /**
         * 报销凭证对应金额
         */
        private BigDecimal voucherAmount;

        /**
         * 报销凭证产品类型
         */
        private Integer prdType;

        /**
         * 附加产品ID
         */
        private String addonId;

        /**
         * 报销凭证产品编码
         */
        private String prdCode;

        /**
         * 最早起飞时间
         */
        private Date earliestDepTime;

        /**
         * 最晚起飞时间
         */
        private Date latestDepTime;

        /**
         * 发票备注
         */
        private String invoiceRemark;

        /**
         * 虚拟唯一标志
         */
        private String identity = UUID.randomUUID().toString();

        public VoucherInfo buildVoucherInfo() {
            VoucherInfo voucherInfo = new VoucherInfo();
            voucherInfo.setDetailType(this.getVoucherSubType().getId());
            voucherInfo.setRemark(this.getInvoiceRemark());

            InvoiceInfo curInvoiceInfo = new InvoiceInfo();
            if (Objects.nonNull(this.getVoucherSubType())) {
                curInvoiceInfo.setInvoiceCategory(this.getVoucherSubType().getId());
            }
            curInvoiceInfo.setInvoiceAmount(this.getVoucherAmount());
            if (VoucherSubType.MAKE_UP_DIFF_INVOICE.equals(this.getVoucherSubType())) {
                curInvoiceInfo.setInvoiceType(InvoiceType.BALANCE_INVOICE.getId());
            } else {
                curInvoiceInfo.setInvoiceType(InvoiceType.FULL_INVOICE.getId());
            }

            voucherInfo.setPrdCode(this.prdCode);
            voucherInfo.setPassengerName("-");
            voucherInfo.setPassengerCardNo("-");
            voucherInfo.setEarliestDepTime(DateUtil.format(this.earliestDepTime, DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS));
            voucherInfo.setLatestDepTime(DateUtil.format(this.latestDepTime, DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS));
            voucherInfo.setInvoiceInfo(curInvoiceInfo);
            voucherInfo.setIdentity(identity);
            return voucherInfo;
        }
    }

    @Getter
    @Setter
    public static class DftPackageOrderTicketPostTask extends DftPackageOrderPostTask {
        /**
         * 客票ID
         */
        private String ticketId;

        /**
         * 客票号
         */
        private String ticketNo;

        /**
         * 乘机人名字
         */
        private String passengerName;

        /**
         * 乘机人证件类型
         */
        private String passengerIdType;

        /**
         * 乘机人证件号
         */
        private String passengerIdNo;

        /**
         * 报销凭证所属客票ID
         */
        private String currentTicketId;

        /**
         * 起飞日期
         */
        private String onBoardDate;

        /**
         * 起飞时间
         */
        private String onBoardTime;

        /**
         * 乘机人Id
         */
        private Long psgId;

        /**
         * 航段Id
         */
        private Long segId;

        /**
         * 是否为虚拟报销凭证
         */
        private Boolean virtualPostTask = Boolean.FALSE;

        public VoucherInfo buildVoucherInfo() {
            VoucherInfo voucherInfo = new VoucherInfo();
            voucherInfo.setTicketNo(this.getTicketNo());
            voucherInfo.setRemark(this.getInvoiceRemark());
            voucherInfo.setPassengerCardNo(this.getPassengerIdNo());
            voucherInfo.setPassengerName(this.getPassengerName());
            voucherInfo.setIdentity(this.getIdentity());
            voucherInfo.setEarliestDepTime(DateUtil.format(this.getEarliestDepTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS));
            voucherInfo.setLatestDepTime(DateUtil.format(this.getLatestDepTime(), DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS));
            voucherInfo.setPrdCode(this.getPrdCode());
            voucherInfo.setSegIds(Objects.nonNull(this.getSegId()) ? Arrays.asList(this.getSegId()) : null);
            voucherInfo.setPsgId(this.getPsgId());
            if (VoucherType.TICKET_RECEIPT.equals(this.getVoucherType())) {
                //行程单
                voucherInfo.setDetailType(VoucherSubType.ITINERARY.getId());
            } else {
                //发票
                InvoiceInfo curInvoiceInfo = new InvoiceInfo();
                if (Objects.nonNull(this.getVoucherSubType())) {
                    voucherInfo.setDetailType(this.getVoucherSubType().getId());
                    curInvoiceInfo.setInvoiceCategory(this.getVoucherSubType().getId());
                }
                curInvoiceInfo.setInvoiceAmount(this.getVoucherAmount());

                if (VoucherSubType.MAKE_UP_DIFF_INVOICE.equals(this.getVoucherSubType())) {
                    curInvoiceInfo.setInvoiceType(InvoiceType.BALANCE_INVOICE.getId());
                } else {
                    curInvoiceInfo.setInvoiceType(InvoiceType.FULL_INVOICE.getId());
                }

                //设置发票备注
                curInvoiceInfo.setRemark(this.getInvoiceRemark());

                voucherInfo.setInvoiceInfo(curInvoiceInfo);
            }
            return voucherInfo;
        }
    }

}
