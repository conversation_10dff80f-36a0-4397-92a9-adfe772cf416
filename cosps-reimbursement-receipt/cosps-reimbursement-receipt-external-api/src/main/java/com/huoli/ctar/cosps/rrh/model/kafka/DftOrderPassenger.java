package com.huoli.ctar.cosps.rrh.model.kafka;

import lombok.Getter;
import lombok.Setter;

/**
 * 国内机票推送kafka消息订单乘机人信息
 */
@Getter
@Setter
public class DftOrderPassenger {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 乘机人id
     */
    private String cdid;

    /**
     * 加密后的姓名
     */
    private String encryptionName;

    /**
     * 加密后的证件号
     */
    private String encryptionIdCard;

    /**
     * 证件类型
     */
    private Integer idType;

    /**
     * 证件有效期
     */
    private String idValidDate;

    /**
     * 类型 ADT：成人 CHD：儿童 INF：婴儿
     */
    private String type;

    /**
     * 加密后的手机号
     */
    private String encryptionPhone;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 性别 1：男 2：女
     */
    private Integer sex;

    /**
     * 国家类型 domestic：国内 international：国际
     */
    private String countryType;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 国籍id
     */
    private String nationalityId;

    /**
     * 是否本人 1：是 0：否
     */
    private Integer isSelf;

    /**
     * 姓名简拼
     */
    private String jianpin;

    /**
     * 姓名拼音详细（Json）
     */
    private String chinesePinyin;

    /**
     * 扩展字段1
     */
    private String ext1;

    /**
     * 扩展字段2
     */
    private String ext2;

    /**
     * 扩展字段3
     */
    private String ext3;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
