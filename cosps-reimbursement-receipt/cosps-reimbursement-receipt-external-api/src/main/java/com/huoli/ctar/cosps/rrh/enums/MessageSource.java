package com.huoli.ctar.cosps.rrh.enums;

import com.huoli.ctar.core.infra.constant.BaseEnum;

public enum MessageSource implements BaseEnum {

    KAFKA(1, "kafka", "kafka消息"),
    API(2, "api", "api调用");

    private final Integer id;

    private final String code;

    private final String name;

    MessageSource(Integer id, String code, String name) {
        this.id = id;
        this.code = code;
        this.name = name;
    }

    public Integer getId() {
        return this.id;
    }

    @Override
    public String getCode() {
        return this.code;
    }
}
