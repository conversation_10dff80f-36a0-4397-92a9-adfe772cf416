package com.huoli.ctar.cosps.rrh.model.kafka;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 国内机票推送kafka消息订单退款详细信息
 */
@Getter
@Setter
public class DftOrderRefundDetail {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 退票单id
     */
    private String rid;

    /**
     * 订单id
     */
    private String oid;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 机票id,逗号拼接
     */
    private String ticketIds;

    /**
     * 非自愿退票原因
     */
    private String reason;

    /**
     * 应退手续费
     */
    private BigDecimal fee;

    /**
     * 应退金额
     */
    private BigDecimal price;

    /**
     * 调整金额
     */
    private BigDecimal modifPrice;

    /**
     * 扣减优惠劵，积分金额
     */
    private BigDecimal reduceCouponPoint;

    /**
     * 渠道应退价格
     */
    private BigDecimal outReturnPrice;

    /**
     * 渠道实退价格
     */
    private BigDecimal outReturnPriceReal;

    /**
     * 退款规则 1：优先退现金余额，退款顺序：现金-余额-积分-优惠券 2：优先退优惠券积分，退款顺序：优惠券-积分-余额-现金 3：不退优惠券积分，退款顺序：现金-余额
     */
    private Integer rule;

    /**
     * 审核状态 0：待审核 1：审核通过 4：忽略退款
     */
    private Integer auditStatus;

    /**
     * 是否有效 0 无效/1 有效（默认有效）
     */
    private Integer valid;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
