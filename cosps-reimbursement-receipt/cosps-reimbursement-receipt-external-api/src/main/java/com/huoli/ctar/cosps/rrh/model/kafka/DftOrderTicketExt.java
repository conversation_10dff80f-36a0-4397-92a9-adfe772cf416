package com.huoli.ctar.cosps.rrh.model.kafka;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 国内机票推送kafka消息订单客票扩展信息
 */
@Getter
@Setter
public class DftOrderTicketExt {
    /**
     * 加价金额
     */
    private BigDecimal subprice;

    /**
     * 是否礼包产品
     */
    private Boolean isPackage;

    /**
     * 儿童是否享受礼包权益
     */
    private Boolean chdIsBack;

    /**
     * 业务侧数据来源加价
     */
    private BigDecimal searchSubPrice;

    /**
     * 用户侧搜索数据来源加价
     */
    private BigDecimal uSearchSubPrice;

    /**
     * 业务侧数据来源儿童加价
     */
    private BigDecimal chdSearchSubPrice;

    /**
     * 企业折扣优惠金额
     */
    private BigDecimal corpDiscount;

    /**
     * 会员直减优惠金额
     */
    private BigDecimal memberDiscount;

    /**
     * 分销货架加价
     */
    private BigDecimal resaleSubPrice;

    /**
     * 礼包/优惠券立减
     */
    private BigDecimal giftDiscount;

    /**
     * 附加产品立减 保险/专车...
     */
    private BigDecimal productDiscount;

    /**
     * 机票立减
     */
    private BigDecimal ticketDiscount;

    /**
     * 会员信息
     */
    private DftMember member;

    /**
     * 转出票差价 原票-新票
     */
    private BigDecimal rebookDiff;
}
