package com.huoli.ctar.cosps.rrh.model.kafka;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 国内机票推送kafka消息订单信息结构
 */
@Getter
@Setter
public class DftOrderInfo extends DftClientData {
    /**
     * 跟踪ID
     */
    private String traceId;

    /**
     * 订单号
     */
    private String oid;

    /**
     * 消息级别  order:订单级别   ticket:票级别
     */
    private String level;

    /**
     * 本次消息关联的机票id
     */
    private List<Integer> relateTicketIds;

    /**
     * 本次消息关联的附加产品id
     */
    private List<Integer> relateProductIds;

    /**
     * 首次订单信息,如果首次订单就是该订单为空
     */
    private DftOrderInfo firstOrder;

    /**
     * 改期原订单信息，改期新单才有
     */
    private DftOrderInfo originOrder;

    /**
     * 机票信息
     */
    private List<DftOrderTicket> tickets;

    /**
     * 支付信息
     */
    private List<DftOrderPay> pays;

    /**
     * 附加产品信息
     */
    private List<DftOrderProduct> products;

    /**
     * 退票单信息
     */
    private List<DftOrderRefund> refunds;

    /**
     * 创建机票关联的统一支付订单号
     */
    private String payOrderId;

    /**
     * 订单总价
     */
    private BigDecimal totalPrice;

    /**
     * 显示状态 1：等待付款，2：支付待确认 3：已取消 4：出票中 5：已出票 6：改升中 7：已改升 8：退票中 9：已退票 10：已使用
     */
    private Integer showStatus;

    /**
     * 行程类型 OW：单程 RT：往返  MP：多程
     */
    private String tripType;

    /**
     * 加密后联系人姓名
     */
    private String encryptionContactName;

    /**
     * 加密后联系人电话
     */
    private String encryptionContactPhone;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 预计出票时间 （分钟数，负值表示起飞前出票，正值表示支付后出票）
     */
    private String expectIssueTime;

    /**
     * 出票时间
     */
    private String ticketTime;

    /**
     * 行程开始时间
     */
    private String tripStartTime;

    /**
     * 行程结束时间
     */
    private String tripEndTime;

    /**
     * 发票类型 0：行程单 1：电子发票 2：行程单+电子发票
     */
    private Integer invoiceType;

    /**
     * 是否有改期 0：否；1：是
     */
    private Integer isReroute;

    /**
     * 是否有退票 0：否；1：是
     */
    private Integer isRefund;

    /**
     * 是否参加活动 0：否；1：是
     */
    private Integer isActivity;

    /**
     * 是否对用户展示 0：否；1：是
     */
    private Integer isShow;

    /**
     * 是否有退款 0：否；1：是
     */
    private Integer isRefundForm;

    /**
     * 订单创建时间
     */
    private String createTime;

    /**
     * 订单更新时间
     */
    private String updateTime;

    /**
     * 消息类型
     */
    private String msgType;

    /**
     * 1：出票 2：改期 3：抢票 4：一定让你走 5：机加酒 6：客服手工单 7:军警残订单 8:儿童单独预计需要补充成人票号 9:组合产品 10:团体票
     */
    private Integer action;

    /**
     * 判断订单是否为抢票订单
     *
     * @return 是否为抢票
     */
    public boolean shakedown() {
        return Objects.nonNull(action) && action == 3;
    }

}
