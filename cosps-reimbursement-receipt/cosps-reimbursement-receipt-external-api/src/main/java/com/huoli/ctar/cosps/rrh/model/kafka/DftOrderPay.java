package com.huoli.ctar.cosps.rrh.model.kafka;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 国内机票推送kafka消息订单支付信息
 */
@Getter
@Setter
public class DftOrderPay {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 类型 1：机票 2：补差价 3: 转出票补差价 4:邮寄费 5:补邮寄费
     */
    private Byte type;

    /**
     * 支付平台id
     */
    private String payOrderId;

    /**
     * 支付URL
     */
    private String payUrl;

    /**
     * 支付标题
     */
    private String title;

    /**
     * 支付总价
     */
    private BigDecimal price;

    /**
     * 支付状态 0：待支付 2：未支付 1：支付成功 21：支付待确认 22：支付失败 11：订单关闭 12：订单已删除
     */
    private Integer status;

    /**
     * 支付方式 1：企业账户 2：账户余额 3：微信 4：支付宝 5：银行卡 6：分期支付
     */
    private Integer payType;

    /**
     * 支付方式
     */
    private String payWay;

    /**
     * 支付方式名称
     */
    private String payWayName;

    /**
     * 支付来源
     */
    private String paySource;

    /**
     * 支付来源名称
     */
    private String paySourceName;

    /**
     * 积分
     */
    private Long points;

    /**
     * 积分抵扣现金金额
     */
    private BigDecimal pointPrice;

    /**
     * 现金
     */
    private BigDecimal cash;

    /**
     * 优惠券抵扣现金金额
     */
    private BigDecimal coupon;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 支付过期时间
     */
    private String expireTime;

    /**
     * Id
     */
    private String corpId;

    /**
     * UATP(0:1)
     */
    private Integer isUatp;

    /**
     * ext2
     */
    private DftOrderPayInfoExt ext2;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
