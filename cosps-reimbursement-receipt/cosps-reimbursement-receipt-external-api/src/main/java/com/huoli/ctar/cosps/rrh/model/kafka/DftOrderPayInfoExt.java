package com.huoli.ctar.cosps.rrh.model.kafka;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 国内机票推送kafka消息订单支付扩展信息
 */
@Getter
@Setter
public class DftOrderPayInfoExt {
    /**
     * 积分
     */
    private String points;

    /**
     * 优惠券
     */
    private String coupon;

    /**
     * 余额
     */
    private String balance;

    /**
     * 企业支付
     */
    @JsonProperty(value = "corp-balance")
    private String corpBalance;

    /**
     * 企业名字
     */
    private String corpName;

    /**
     * 现金
     */
    private String cash;

    /**
     * 实际支付优惠券ID
     */
    private String couponId;

    /**
     * 默认选择优惠券ID
     */
    private String selectCouponId;

    /**
     * 企业优惠总金额
     */
    private BigDecimal corpDiscount;

    /**
     * 会员直减优惠总金额
     */
    private BigDecimal memberDiscount;
}
