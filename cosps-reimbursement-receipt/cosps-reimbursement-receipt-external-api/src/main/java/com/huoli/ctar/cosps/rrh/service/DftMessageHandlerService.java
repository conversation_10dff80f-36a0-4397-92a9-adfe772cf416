package com.huoli.ctar.cosps.rrh.service;

import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.ordercenter.constant.DftOrderChangeMessageType;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.ordercenter.model.DftOrderInfo;
import com.huoli.ctar.cosps.rrh.service.kafka.DftOrderStatusChangeMessageHandler;

import java.util.List;

public interface DftMessageHandlerService {

    List<DftOrderStatusChangeMessageHandler> getHandlers(String messageType);

    List<DftOrderStatusChangeMessageHandler> getHandlers(DftOrderChangeMessageType messageType);

    void handle(String orderId);

    void handle(DftOrderInfo dftOrderInfo);
}
