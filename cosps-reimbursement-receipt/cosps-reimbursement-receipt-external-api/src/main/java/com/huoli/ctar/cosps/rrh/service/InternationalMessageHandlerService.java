package com.huoli.ctar.cosps.rrh.service;

import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.international.model.InFlightOrderPostInfo;
import com.huoli.ctar.cosps.rrh.enums.InternationalOrderChangeType;
import com.huoli.ctar.cosps.rrh.service.kafka.InternationalOrderStatusChangeMessageHandler;

import java.util.List;

public interface InternationalMessageHandlerService {

    List<InternationalOrderStatusChangeMessageHandler> getHandlers(String messageType);

    List<InternationalOrderStatusChangeMessageHandler> getHandlers(InternationalOrderChangeType messageType);

    void handle(String orderId);

    void handle(InFlightOrderPostInfo inFlightOrderPostInfo);
}
