package com.huoli.ctar.cosps.rrh.model.kafka;


import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 国内机票推送kafka消息订单客票信息
 */
@Getter
@Setter
public class DftOrderTicket {
    /**
     * 机票主键id
     */
    private Integer id;

    /**
     * 乘机人信息
     */
    private DftOrderPassenger passenger;

    /**
     * 航段信息
     */
    private DftOrderSegment segment;

    /**
     * 客规
     */
    private DftOrderRule rule;

    /**
     * 外部订单id
     */
    private String outOrderId;

    /**
     * 业务订单id
     */
    private String ticketOrderId;

    /**
     * 渠道订单id
     */
    private String ticketDetailId;

    /**
     * 查询渠道
     */
    private String queryChannel;

    /**
     * 查询总价
     */
    private BigDecimal queryTotalPrice;

    /**
     * 查询票价
     */
    private BigDecimal queryPrice;

    /**
     * 查询基建费
     */
    private BigDecimal queryAirportFee;

    /**
     * 查询燃油费
     */
    private BigDecimal queryOilFee;

    /**
     * 出票渠道
     */
    private String ticketChannel;

    /**
     * 出票供应商标识，当ticket_source＝supply时必填
     */
    private String ticketAgentId;

    /**
     * 出票总价
     */
    private BigDecimal ticketTotalPrice;

    /**
     * 渠道出票票价
     */
    private BigDecimal ticketChannelPrice;

    /**
     * 渠道对外支付价
     */
    private BigDecimal ticketChannelPayPrice;

    /**
     * 渠道结算价
     */
    private BigDecimal ticketChannelSettlePrice;

    /**
     * 出票基建费
     */
    private BigDecimal ticketAirportFee;

    /**
     * 出票燃油费
     */
    private BigDecimal ticketOilFee;

    /**
     * 出票完成时间
     */
    private String ticketTime;

    /**
     * 订座pnr编码
     */
    private String pnrNo;

    /**
     * 折扣
     */
    private Double discount;

    /**
     * 前返金额，票面价-结算价
     */
    private Double preReturn;

    /**
     * 后返金额
     */
    private Double serviceFee;

    /**
     * 电子客票号
     */
    private String eticket;

    /**
     * 票面价格
     */
    private BigDecimal ticketPrice;

    /**
     * 出票仓位
     */
    private String ticketCabin;

    /**
     * 出票基础仓位等级
     */
    private String ticketBaseCabin;

    /**
     * 机票状态 1：待出票 11：已取消 2：已出票 21：出票中 3：已改期 31：改期中 4：已退票 41：退票中 5：已使用
     */
    private Integer status;

    /**
     * 转出票标记 0：默认，未转出票 1：已转出票
     */
    private Integer channelChange;

    /**
     * 转单原单机票主键
     */
    private Integer rebookOrgTicketId;

    /**
     * 是否有改期 0：否 1：是
     */
    private Integer isReroute;

    /**
     * 渠道改期手续费
     */
    private BigDecimal rerouteChannelFee;

    /**
     * 渠道端期票面差价
     */
    private BigDecimal rerouteChannelPriceDiff;

    /**
     * 改期票面差价
     */
    private BigDecimal reroutePriceDiff;

    /**
     * 改期前机票ID
     */
    private Integer rerouteBeforeTicketId;

    /**
     * 改期前乘机人ID
     */
    private Integer rerouteBeforePsgId;

    /**
     * 改期后航段id
     */
    private Integer rerouteSegId;

    /**
     * 改期前航段id
     */
    private Integer rerouteBeforeSegId;

    /**
     * 改期提交时间
     */
    private String rerouteSubmitTime;

    /**
     * 改期完成时间
     */
    private String rerouteTime;

    /**
     * ext1
     */
    private DftOrderTicketExt ext1;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 退款完成时间
     */
    private String refundmentTime;

    /**
     * 报销凭证信息
     */
    private DftVoucher voucher;
}
