package com.huoli.ctar.cosps.rrh.model.kafka;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Setter
@Getter
@ApiModel(value = "专车推送消息体")
public class CarOrderInfo {

    @ApiModelProperty(value = "企业ID", required = true)
    private Long corpId;

    @ApiModelProperty(value = "订单号", required = true)
    private String orderId;

    @ApiModelProperty(value = "预订人信息", required = true)
    private String bookedBy;

    @ApiModelProperty(value = "订单变更时间", required = true)
    private String orderChangeTime;

    @ApiModelProperty(value = "订单变更类型", required = true)
    private Integer orderChangeType;

    @ApiModelProperty(value = "订单变更描述", required = true)
    private String orderChangeTypeDesc;

    @ApiModelProperty(value = "企业支付金额", required = true)
    private BigDecimal corpAmount;

    @ApiModelProperty(value = "到达地", required = true)
    private String endAddress;

    @ApiModelProperty(value = "出发地", required = true)
    private String startAddress;

    @ApiModelProperty(value = "服务日期", required = true)
    private String serviceDate;
}
