package com.huoli.ctar.cosps.rrh.utils;

import com.huoli.ctar.common.utils.DateUtil;

import java.util.Date;

public class InvoiceUtils {
    public static final int REMARK_MAX_LENGTH = 80;

    private InvoiceUtils() {
    }


    public static String getSingleDomesticFlightTicketRemark(String flyNo, String depAirportName, String arrAirportName, Date depTime, String passengerName) {
        String invoiceRemark = String.format("%s %s %s-%s 乘机人:%s", DateUtil.format(depTime, "yyyy年MM月dd日"), flyNo, depAirportName, arrAirportName, passengerName);
        if (invoiceRemark.length() <= REMARK_MAX_LENGTH) {
            return invoiceRemark;
        }

        invoiceRemark = String.format("%s %s-%s 乘机人:%s", DateUtil.format(depTime, "yyyy年MM月dd日"), depAirportName, arrAirportName, passengerName);
        if (invoiceRemark.length() <= REMARK_MAX_LENGTH) {
            return invoiceRemark;
        }

        invoiceRemark = String.format("%s 乘机人:%s", DateUtil.format(depTime, "yyyy年MM月dd日"), passengerName);
        if (invoiceRemark.length() <= REMARK_MAX_LENGTH) {
            return invoiceRemark;
        }

        invoiceRemark = String.format("乘机人:%s", passengerName);
        return invoiceRemark;
    }

    public static String getIntFlightTicketRemark(String flyNo, String flightPath, Date depTime, String passengerName) {
        String invoiceRemark = String.format("%s %s %s 乘机人:%s", DateUtil.format(depTime, "yyyy-MM-dd HH:mm"), flyNo, flightPath, passengerName);
        if (invoiceRemark.length() <= REMARK_MAX_LENGTH) {
            return invoiceRemark;
        }
        return invoiceRemark;
    }
}
