package com.huoli.ctar.cosps.rrh.enums;

import com.huoli.ctar.core.infra.constant.BaseEnum;

import java.util.Objects;

public enum InternationalOrderChangeType implements BaseEnum {
    ORDER_ISSUE(1, "issue", "出票"),
    ORDER_RESCHDULE(2, "reschdule", "改期"),
    ORDER_REFUND(3, "refund", "退票"),
    ORDER_REBATE(4, "rebate", "退款"),
    ORDER_SUPPLEMENTARY_PAY(5, " supplementary-pay", "补款");

    private final Integer id;

    private final String code;

    private final String desc;

    InternationalOrderChangeType(Integer id, String code, String desc) {
        this.id = id;
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return this.code;
    }


    public Integer getId() {
        return this.id;
    }

    public static InternationalOrderChangeType getById(Integer id) {
        InternationalOrderChangeType[] values = InternationalOrderChangeType.values();
        for (InternationalOrderChangeType type : values) {
            if (Objects.equals(type.getId(), id)) {
                return type;
            }
        }
        return null;
    }

    public static InternationalOrderChangeType getByCode(String code) {
        InternationalOrderChangeType[] values = InternationalOrderChangeType.values();
        for (InternationalOrderChangeType type : values) {
            if (Objects.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }
}
