package com.huoli.ctar.cosps.rrh.model.kafka;


import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 国内机票推送kafka消息订单附加产品信息
 */
@Getter
@Setter
public class DftOrderProduct {
    /**
     * 产品id
     */
    private Integer id;

    /**
     * 乘客id
     */
    private Integer psgId;

    /**
     * 航段id
     */
    private Integer segId;

    /**
     * 机票id
     */
    private Integer ticketId;

    /**
     * 产品类型 1：延误保 2：专车 3：保险 4：套餐 5：邮寄
     */
    private Integer prdType;

    /**
     * 价格类型：金额类型，0：购买；1：立减；2：赠送积分；21：赠送保险；22：赠送优惠券；23：赠送会员卡；3：可提现返现；31：不可提现返现；4：加价；
     */
    private Integer priceType;

    /**
     * 产品代码
     */
    private String prdCode;

    /**
     * 外部订单号
     */
    private String prdOrderId;

    /**
     * 支付信息id
     */
    private Integer payInfoId;

    /**
     * 支付平台id
     */
    private String payOrderId;

    /**
     * 金额
     */
    private BigDecimal price;

    /**
     * 实付金额
     */
    private BigDecimal paidPrice;

    /**
     * 发票金额
     */
    private BigDecimal invoicePrice;

    /**
     * 状态 0：未生效 1：已生效，待服务 2：已提供 3：已退 4：已改 31：待退订
     */
    private Integer status;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
