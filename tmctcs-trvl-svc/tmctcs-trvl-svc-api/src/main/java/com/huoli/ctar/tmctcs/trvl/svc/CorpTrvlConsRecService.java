package com.huoli.ctar.tmctcs.trvl.svc;

import com.huoli.ctar.cfesag.external.sys.api.payment.gateway.model.CorpTransRecordNew;
import com.huoli.ctar.tmccbs.client.domain.cms.cam.vo.tmc.mobile.TmcCorpTrvlConsRecVO;

import java.util.List;

public interface CorpTrvlConsRecService {

    List<CorpTransRecordNew> retrieveCorpTradeRecords(final Long corpId,
                                                      final String corpMemberPhoneId,
                                                      final Long transStartTime,
                                                      final Long transEndTime);

    List<TmcCorpTrvlConsRecVO> retrieveCorpTrvlConsRecs(final Long corpId,
                                                        final String corpMemberPhoneId,
                                                        final Long transStartDate,
                                                        final Long transEndDate);
}
