package com.huoli.ctar.tmctcs.trvl.svc;

import com.huoli.ctar.tmccbs.biz.domain.cms.cam.vo.std.CorpInfoSustainStdVO;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.vo.std.CorpMemberStdVO;
import com.huoli.ctar.tmccbs.biz.domain.vo.TmcEasyPnpUserCheckParam;
import com.huoli.ctar.tmccbs.client.domain.cms.cam.vo.tmc.common.CorpMemberGeneralQueryParam;
import com.huoli.ctar.tmccbs.client.domain.cms.cam.vo.tmc.common.CorpMemberGeneralVO;

public interface TmcUserService {

    CorpMemberGeneralVO queryCorpMember(final CorpMemberGeneralQueryParam queryParam);

    Long registerCorp(final CorpInfoSustainStdVO corpInfo);

    Long checkUser(final String userPhoneId);

    Long checkUserByMobile(final String mobile);

    void checkUserCorpInfo(final Long corpId, final String userPhoneId);

    CorpMemberStdVO validateUser(final String mobile, final String userChannel);

    CorpMemberStdVO validateUser(final TmcEasyPnpUserCheckParam easyPnpUserCheckParam);
}
