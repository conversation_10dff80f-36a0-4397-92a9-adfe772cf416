<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.huoli.ctar</groupId>
  <artifactId>cfesag-client-springboot-starter</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>jar</packaging>

  <name>cfesag-client-springboot-starter</name>
  <description>rest template based client for cfesag</description>

  <properties>
    <spring.boot.version>2.1.2.RELEASE</spring.boot.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>cfesag-external-sys-api</artifactId>
      <version>1.0.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    
    <dependency>
      <groupId>com.huoli.ctar.core</groupId>
      <artifactId>core-infra</artifactId>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmc-dependencies</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.18.1</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.7.0</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <!-- 发布到私服 -->
  <distributionManagement>
    <snapshotRepository>
      <id>huoli-snapshot</id>
      <name>User Porject Snapshot</name>
      <url>http://mvn.133ec.com:8081/nexus/content/repositories/snapshots/</url>
      <uniqueVersion>true</uniqueVersion>
    </snapshotRepository>
    <repository>
      <id>huoli-release</id>
      <name>User Porject Release</name>
      <url>http://mvn.133ec.com:8081/nexus/content/repositories/releases/</url>
    </repository>
  </distributionManagement>
</project>
