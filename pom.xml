<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.huoli.ctar</groupId>
  <artifactId>cosps</artifactId>
  <version>1.0.1</version>
  <name>cosps</name>
  <description>Corp Order Subscribing and Processing System</description>
  <packaging>pom</packaging>

  <modules>
    <module>cosps-web</module>
    <module>cosps-omh</module>
    <module>cosps-reimbursement-receipt</module>
    <module>cosps-sms-handle</module>
    <module>cosps-order-status-sync</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <cosps.version>1.0.1</cosps.version>
    <tmccbs-module-version>1.0.0-SNAPSHOT</tmccbs-module-version>
    <tmctms-module-version>1.0.0-SNAPSHOT</tmctms-module-version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmc-dependencies</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-biz-domain</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-client-domain</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-cms-basic-api</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-cms-basic-server</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-cpcms-basic-api</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-cpcms-basic-server</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-cosps-basic-api</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-cosps-basic-server</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-csa-basic-api</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-csa-basic-server</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-tfs-basic-svc-api</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-tfs-basic-svc-impl</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>


      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-cam-cache-api</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-cam-cache-server</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-tam-cache-api</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-tam-cache-server</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-tam-common-api</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-tam-common-server</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-cam-tmc-common-api</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-cam-tmc-common-server</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-budget-tmc-common-api</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-budget-tmc-common-server</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-tbm-tmc-common-api</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-tbm-tmc-common-server</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmccbs-common-web-config</artifactId>
        <version>${tmccbs-module-version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>tmc-client-springboot-starter</artifactId>
        <version>${tmc-clients.version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>cosps-omh-api</artifactId>
        <version>${cosps.version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>cosps-omh-server</artifactId>
        <version>${cosps.version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>cosps-reimbursement-receipt-cobjs</artifactId>
        <version>${cosps.version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>cosps-order-status-sync-api</artifactId>
        <version>${cosps.version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>cosps-order-status-sync-server</artifactId>
        <version>${cosps.version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>cosps-reimbursement-receipt-external-api</artifactId>
        <version>${cosps.version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>cosps-reimbursement-receipt-external-server</artifactId>
        <version>${cosps.version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>cosps-sms-handle-api</artifactId>
        <version>${cosps.version}</version>
      </dependency>

      <dependency>
        <groupId>com.huoli.ctar</groupId>
        <artifactId>cosps-sms-handle-server</artifactId>
        <version>${cosps.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <profiles>
    <profile>
      <id>dev</id>
      <properties>
        <profileActive>dev</profileActive>
        <tmccbs-module-version>1.0.0-SNAPSHOT</tmccbs-module-version>
        <tmctms-module-version>1.0.0-SNAPSHOT</tmctms-module-version>
        <tmc-clients.version>1.0.0-SNAPSHOT</tmc-clients.version>
      </properties>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
    </profile>
    <profile>
      <id>test</id>
      <properties>
        <profileActive>test</profileActive>
        <tmccbs-module-version>1.0.0-SNAPSHOT</tmccbs-module-version>
        <tmctms-module-version>1.0.0-SNAPSHOT</tmctms-module-version>
        <tmc-clients.version>1.0.0-SNAPSHOT</tmc-clients.version>
      </properties>
    </profile>
    <profile>
      <id>prod</id>
      <properties>
        <profileActive>prod</profileActive>
        <tmccbs-module-version>1.0.0.prod-SNAPSHOT</tmccbs-module-version>
        <tmctms-module-version>1.0.0.prod-SNAPSHOT</tmctms-module-version>
        <tmc-clients.version>1.0.0.prod-SNAPSHOT</tmc-clients.version>
      </properties>
    </profile>
    <profile>
      <id>preprod</id>
      <properties>
        <profileActive>preprod</profileActive>
        <tmccbs-module-version>1.0.0.preprod-SNAPSHOT</tmccbs-module-version>
        <tmctms-module-version>1.0.0.preprod-SNAPSHOT</tmctms-module-version>
        <tmc-clients.version>1.0.0.preprod-SNAPSHOT</tmc-clients.version>
      </properties>
    </profile>
  </profiles>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.7.0</version>
          <configuration>
            <source>1.8</source>
            <target>1.8</target>
            <encoding>UTF-8</encoding>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <distributionManagement>
    <snapshotRepository>
      <id>huoli-snapshot</id>
      <name>User Porject Snapshot</name>
      <url>http://mvn.133ec.com:8081/nexus/content/repositories/snapshots/</url>
      <uniqueVersion>true</uniqueVersion>
    </snapshotRepository>
    <repository>
      <id>huoli-release</id>
      <name>User Porject Release</name>
      <url>http://mvn.133ec.com:8081/nexus/content/repositories/releases/</url>
    </repository>
  </distributionManagement>
</project>
