package com.huoli.ctar.cms.cam.mobile.service.impl;

import com.huoli.ctar.cfesag.client.CfesagClient;
import com.huoli.ctar.cfesag.external.sys.api.usercenter.model.AuthCodeResult;
import com.huoli.ctar.cms.cam.mobile.service.DummyLoginMobileService;
import com.huoli.ctar.core.infra.exception.ServiceException;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.entity.CorpMember;
import com.huoli.ctar.tmccbs.biz.domain.constant.Status;
import com.huoli.ctar.tmccbs.cms.basic.cam.service.internal.CorpMemberInternalService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("dummyLoginMobileService")
public class DummyLoginMobileServiceImpl implements DummyLoginMobileService {
    private static final String DEFAULT_PASSWORD = "123454321";

    @Autowired
    @Qualifier("corpMemberInternalService")
    private CorpMemberInternalService corpMemberInternalService;

    @Autowired
    private CfesagClient cfesagClient;

    @Override
    public Map<String, Object> login(String mobile, String password) {
        checkPassword(password);
        CorpMember corpMember = queryCorpMemberByMobile(mobile);
        AuthCodeResult authCodeResult = cfesagClient.getAuthByPhoneIdTemp(corpMember.getPhoneId().toString());

        Map<String, Object> returnValues = new HashMap<>();
        returnValues.put("authCode", Objects.nonNull(authCodeResult) ? authCodeResult.getAuthcode() : null);
        returnValues.put("corpId", corpMember.getCorpInfoSid());
        return returnValues;
    }

    private void checkPassword(String password) {
        if (!DEFAULT_PASSWORD.equals(password)) {
            throw new ServiceException("手机号或密码错误");
        }
    }


    private CorpMember queryCorpMemberByMobile(String mobile) {
        Map<String, Object> queryParams = new HashMap<String, Object>();
        queryParams.put("mobile", mobile);
        queryParams.put("status", Status.ENABLE);

        List<String> showColumns = new ArrayList<>();
        showColumns.add("phoneId");
        showColumns.add("corpInfoSid");


        List<CorpMember> corpMembers = corpMemberInternalService.findByParams(queryParams, showColumns);
        if (CollectionUtils.isEmpty(corpMembers)) {
            throw new ServiceException("手机号不存在");
        }

        return corpMembers.get(0);
    }
}
