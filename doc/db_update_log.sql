CREATE TABLE `rrh_reimbursement_receipt_pkg_record` (
  `sid` bigint(15) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `corp_id` varchar(30) NOT NULL COMMENT '企业ID',
  `pkg_id` varchar(30) NOT NULL COMMENT '报销凭证包裹ID',
  `cal_start_date` date NOT NULL COMMENT '结算开始日期',
  `cal_end_date` date NOT NULL COMMENT '结算结束日期',
  `report_export_url` varchar(128) DEFAULT NULL COMMENT '报销清单报表导出路径',
  `invoice_pack_export_url` varchar(128) DEFAULT NULL COMMENT '发票压缩包下载路径',
  `created_by` varchar(50) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更改人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version_num` int(9) NOT NULL COMMENT '版本号',
  PRIMARY KEY (`sid`),
  UNIQUE KEY `uniq_pkg_id` (`pkg_id`) USING BTREE,
  KEY `idx_corp_id` (`corp_id`) USING BTREE,
  KEY `idx_combine` (`cal_start_date`,`cal_end_date`,`corp_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报销凭证处理-报销凭证包裹记录表';

CREATE TABLE `rrh_reimbursement_receipt_summit_record` (
  `sid` bigint(15) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `corp_id` varchar(30) NOT NULL COMMENT '企业ID',
  `cal_start_date` date NOT NULL COMMENT '结算开始日期',
  `cal_end_date` date NOT NULL COMMENT '结算结束日期',
  `service_id` varchar(20) NOT NULL COMMENT '业务类型（0:国内机票,68:国际机票,29:火车票,1:酒店,6:专车,2:保险）',
  `post_time` datetime NOT NULL COMMENT '邮寄时间',
  `post_id` varchar(60) NOT NULL COMMENT '邮寄包裹ID',
  `status` varchar(20) NOT NULL COMMENT '报销凭证处理状态(处理中: handling, 已处理: done)',
  `pkg_id` varchar(30) DEFAULT NULL COMMENT '报销凭证包裹ID',
  `created_by` varchar(20) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(20) DEFAULT NULL COMMENT '更改人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version_num` int(9) NOT NULL COMMENT '版本号',
  PRIMARY KEY (`sid`),
  UNIQUE KEY `uniq_corp_summit_record` (`corp_id`,`cal_start_date`,`cal_end_date`,`service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报销凭证处理-报销凭证提交记录表';

CREATE TABLE `rrh_car_reimbursement_receipt_record` (
  `sid` bigint(15) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `corp_id` varchar(30) NOT NULL COMMENT '企业ID',
  `order_id` varchar(50) NOT NULL COMMENT '业务订单号',
  `booked_by` varchar(30) NOT NULL COMMENT '预订人phoneId',
  `order_change_type` varchar(30) NOT NULL COMMENT '业务变更类型(付款并结算完成, 退款, 部分退款, 补款)',
  `order_change_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '业务变更时间',
  `start_address` varchar(100) DEFAULT NULL COMMENT '出发地',
  `end_address` varchar(100) DEFAULT NULL COMMENT '到达地',
  `service_date` varchar(20) DEFAULT NULL COMMENT '出发日期',
  `voucher_type` varchar(20) NOT NULL COMMENT '凭证类型(发票: invoice)',
  `voucher_sub_type` varchar(20) NOT NULL COMMENT '凭证子类型(专车服务费发票)',
  `voucher_amount` decimal(10,2) NOT NULL COMMENT '报销凭证金额',
  `post_id` varchar(30) NOT NULL COMMENT '邮寄包裹ID',
  `invoice_id` bigint(15) DEFAULT NULL COMMENT '发票ID',
  `invoice_url` varchar(1024) DEFAULT NULL COMMENT '发票下载地址',
  `invoice_status` tinyint(4) DEFAULT NULL COMMENT '发票状态（0:正常，1:作废）',
  `raw_msg_data` blob  NULL COMMENT '原生消息数据',
  `created_by` varchar(20) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(20) DEFAULT NULL COMMENT '更改人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version_num` int(9) NOT NULL COMMENT '版本号',
  PRIMARY KEY (`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报销凭证处理-专车报销凭证记录表';


CREATE TABLE `rrh_dft_reimbursement_receipt_record` (
  `sid` bigint(15) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `corp_id` varchar(30) NOT NULL COMMENT '企业ID',
  `order_id` varchar(50) NOT NULL COMMENT '业务订单号',
  `booked_by` varchar(30) NOT NULL COMMENT '预订人phoneId',
  `order_change_type` varchar(30) NOT NULL COMMENT '业务变更类型(issue: 出票, reschedule: 改期, refund: 退票, rebate: 退款, supplementary-pay: 补款 等(注: 如有遗漏请补充)',
  `order_change_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '业务变更时间',
  `post_id` varchar(30) NOT NULL COMMENT '邮寄包裹ID',
  `raw_msg_data` blob NULL COMMENT '原生消息数据',
  `created_by` varchar(50) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更改人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version_num` int(9) NOT NULL COMMENT '版本号',
  PRIMARY KEY (`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报销凭证处理-国内机票报销凭证记录表';


CREATE TABLE `rrh_dft_reimbursement_receipt_order_detail_record` (
  `sid` bigint(15) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `addon_id` varchar(20) DEFAULT NULL COMMENT '附加产品ID',
  `voucher_type` varchar(20) NOT NULL COMMENT '凭证类型(发票: invoice, 行程单: ticket-receipt)',
  `voucher_sub_type` varchar(30) NOT NULL DEFAULT 'none' COMMENT '凭证子类型(当凭证类型为发票时, 此类型来用指定发票的类型(代订机票款发票(如有遗漏请补充)))',
  `voucher_amount` decimal(10,2) NOT NULL COMMENT '报销凭证金额',
  `product_type` tinyint(4) DEFAULT NULL COMMENT '附加产品类型（1：延误保 2：专车 3：保险 4：套餐 5：邮寄）',
  `product_code` varchar(128) DEFAULT NULL COMMENT '附加产品代码',
  `order_id` varchar(50) DEFAULT NULL COMMENT '订单号',
  `first_order_id` varchar(50) DEFAULT NULL COMMENT '首单订单号',
  `detail_id` bigint(15) DEFAULT NULL COMMENT '邮寄任务详情ID',
  `task_status` tinyint(4) DEFAULT NULL COMMENT '邮寄任务状态（0:已创建，1:已取消）',
  `record_sid` bigint(15) NOT NULL COMMENT '国内机票报销凭证记录ID',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(20) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(20) DEFAULT NULL COMMENT '更改人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version_num` int(9) NOT NULL COMMENT '版本号',
  PRIMARY KEY (`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报销凭证处理-国内机票订单维度报销凭证详情记录表';

CREATE TABLE `rrh_dft_reimbursement_receipt_ticket_detail_record` (
  `sid` bigint(15) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `ticket_id` varchar(20) NOT NULL COMMENT '客票ID',
  `ticket_no` varchar(30) DEFAULT NULL COMMENT '电子客票号',
  `passenger_id_type` varchar(50) DEFAULT NULL COMMENT '乘机人证件类型',
  `passenger_id_no` varchar(60) DEFAULT NULL COMMENT '乘机人证件号',
  `passenger_name` varchar(40) DEFAULT NULL COMMENT '乘机人姓名',
  `onboard_date` varchar(10) DEFAULT NULL COMMENT '出发日期(yyyy-MM-dd)',
  `onboard_time` varchar(5) DEFAULT NULL COMMENT '乘机时间(HH:mm)',
  `voucher_type` varchar(20) NOT NULL COMMENT '凭证类型(发票: invoice, 行程单: ticket-receipt)',
  `voucher_sub_type` varchar(30) NOT NULL DEFAULT 'none' COMMENT '凭证子类型(当凭证类型为发票时, 此类型来用指定发票的类型(代订机票款发票(如有遗漏请补充)))',
  `voucher_amount` decimal(10,2) NOT NULL COMMENT '报销凭证金额',
  `addon_id` varchar(20) DEFAULT NULL COMMENT '附加产品ID',
  `product_type` tinyint(4) DEFAULT NULL COMMENT '附加产品类型（1：延误保 2：专车 3：保险 4：套餐 5：邮寄）',
  `product_code` varchar(128) DEFAULT NULL COMMENT '附加产品代码',
  `current_ticket_id` varchar(20) DEFAULT NULL COMMENT '最新客票ID',
  `order_id` varchar(50) DEFAULT NULL COMMENT '订单号',
  `first_order_id` varchar(50) DEFAULT NULL COMMENT '首单订单号',
  `detail_id` bigint(15) DEFAULT NULL COMMENT '邮寄任务详情ID',
  `task_status` tinyint(4) DEFAULT NULL COMMENT '邮寄任务状态（0:已创建，1:已取消）',
  `record_sid` bigint(15) NOT NULL COMMENT '国内机票报销凭证记录ID',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(20) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(20) DEFAULT NULL COMMENT '更改人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version_num` int(9) NOT NULL COMMENT '版本号',
  PRIMARY KEY (`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报销凭证处理-国内机票客票维度报销凭证详情记录表';

CREATE TABLE `rrh_failure_order_change_msg` (
  `sid` bigint(15) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `trace_id` varchar(50) DEFAULT NULL COMMENT '消息跟踪ID',
  `first_order_id` varchar(50) NOT NULL COMMENT '首单订单号',
  `raw_msg_data` blob NULL COMMENT '原生消息数据',
  `order_change_time` datetime NOT NULL COMMENT '订单变更时间',
  `msg_status` varchar(10) NOT NULL COMMENT '消息处理状态（failure:失败，success:成功）',
  `service_id` varchar(10) NOT NULL COMMENT '消息业务类型（0:国内机票,68:国际机票,29:火车票,1:酒店,6:专车,2:保险）',
  `created_by` varchar(20) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(20) DEFAULT NULL COMMENT '更改人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version_num` int(9) NOT NULL COMMENT '版本号',
  PRIMARY KEY (`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报销凭证处理失败的订单变更消息';
## 已执行

ALTER TABLE rrh_dft_reimbursement_receipt_record MODIFY COLUMN raw_msg_data MEDIUMBLOB NULL COMMENT '原生消息数据';
ALTER TABLE rrh_failure_order_change_msg MODIFY COLUMN raw_msg_data MEDIUMBLOB NULL COMMENT '原生消息数据';
ALTER TABLE rrh_car_reimbursement_receipt_record MODIFY COLUMN raw_msg_data MEDIUMBLOB NULL COMMENT '原生消息数据';
## 已执行

CREATE TABLE `rrh_int_reimbursement_receipt_record`
(
    `sid`               bigint(15)  NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `corp_id`           varchar(30) NOT NULL COMMENT '企业ID',
    `order_id`          varchar(50) NOT NULL COMMENT '业务订单号',
    `booked_by`         varchar(30) NOT NULL COMMENT '预订人phoneId',
    `order_change_type` varchar(30) NOT NULL COMMENT '业务变更类型(issue: 出票, reschedule: 改期, refund: 退票, rebate: 退款, supplementary-pay: 补款 等(注: 如有遗漏请补充)',
    `order_change_time` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '业务变更时间',
    `post_id`           varchar(30) NOT NULL COMMENT '邮寄包裹ID',
    `raw_msg_data`      MEDIUMBLOB        NULL COMMENT '原生消息数据',
    `created_by`        varchar(50) NOT NULL COMMENT '创建人',
    `created_time`      datetime    NOT NULL COMMENT '创建时间',
    `updated_by`        varchar(50)          DEFAULT NULL COMMENT '更改人',
    `updated_time`      datetime             DEFAULT NULL COMMENT '更新时间',
    `version_num`       int(9)      NOT NULL COMMENT '版本号',
    PRIMARY KEY (`sid`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='报销凭证处理-国际机票报销凭证记录表';

CREATE TABLE `rrh_int_reimbursement_receipt_order_detail_record`
(
    `sid`              bigint(15)     NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `addon_id`         varchar(40)             DEFAULT NULL COMMENT '附加产品ID',
    `voucher_type`     varchar(20)    NOT NULL COMMENT '凭证类型(发票: invoice, 行程单: ticket-receipt)',
    `voucher_sub_type` varchar(30)    NOT NULL DEFAULT 'none' COMMENT '凭证子类型(当凭证类型为发票时, 此类型来用指定发票的类型(代订机票款发票(如有遗漏请补充)))',
    `voucher_amount`   decimal(10, 2) NOT NULL COMMENT '报销凭证金额',
    `product_type`     tinyint(4)              DEFAULT NULL COMMENT '附加产品类型（1：延误保 2：专车 3：保险 4：套餐 5：邮寄）',
    `product_code`     varchar(128)            DEFAULT NULL COMMENT '附加产品代码',
    `order_id`         varchar(50)             DEFAULT NULL COMMENT '订单号',
    `detail_id`        bigint(15)              DEFAULT NULL COMMENT '邮寄任务详情ID',
    `task_status`      tinyint(4)              DEFAULT NULL COMMENT '邮寄任务状态（0:已创建，1:已取消）',
    `record_sid`       bigint(15)     NOT NULL COMMENT '国际机票报销凭证记录ID',
    `remark`           varchar(1024)           DEFAULT NULL COMMENT '备注',
    `created_by`       varchar(20)    NOT NULL COMMENT '创建人',
    `created_time`     datetime       NOT NULL COMMENT '创建时间',
    `updated_by`       varchar(20)             DEFAULT NULL COMMENT '更改人',
    `updated_time`     datetime                DEFAULT NULL COMMENT '更新时间',
    `version_num`      int(9)         NOT NULL COMMENT '版本号',
    PRIMARY KEY (`sid`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='报销凭证处理-国际机票订单维度报销凭证详情记录表';

CREATE TABLE `rrh_int_reimbursement_receipt_ticket_detail_record`
(
    `sid`               bigint(15)     NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `ticket_no`         varchar(30)             DEFAULT NULL COMMENT '电子客票号',
    `passenger_id_type` varchar(50)             DEFAULT NULL COMMENT '乘机人证件类型',
    `passenger_id_no`   varchar(60)             DEFAULT NULL COMMENT '乘机人证件号',
    `passenger_name`    varchar(40)             DEFAULT NULL COMMENT '乘机人姓名',
    `dep_time`          varchar(20)              DEFAULT NULL COMMENT '乘机时间(yyyy-MM-dd HH:mm:ss)',
    `voucher_type`      varchar(20)    NOT NULL COMMENT '凭证类型(发票: invoice, 行程单: itinerary)',
    `voucher_sub_type`  varchar(30)    NOT NULL DEFAULT 'none' COMMENT '凭证子类型(当凭证类型为发票时, 此类型来用指定发票的类型(代订机票款发票(如有遗漏请补充)))',
    `voucher_amount`    decimal(10, 2) NOT NULL COMMENT '报销凭证金额',
    `addon_id` varchar(20) DEFAULT NULL COMMENT '附加产品ID',
    `product_type` tinyint(4) DEFAULT NULL COMMENT '附加产品类型（1：延误保 2：专车 3：保险 4：套餐 5：邮寄）',
    `product_code` varchar(128) DEFAULT NULL COMMENT '附加产品代码',
    `order_id`          varchar(50)             DEFAULT NULL COMMENT '订单号',
    `detail_id`         bigint(15)              DEFAULT NULL COMMENT '邮寄任务详情ID',
    `task_status`       tinyint(4)              DEFAULT NULL COMMENT '邮寄任务状态（0:已创建，1:已取消）',
    `record_sid`        bigint(15)     NOT NULL COMMENT '国际机票报销凭证记录ID',
    `remark`            varchar(1024)           DEFAULT NULL COMMENT '备注',
    `created_by`        varchar(20)    NOT NULL COMMENT '创建人',
    `created_time`      datetime       NOT NULL COMMENT '创建时间',
    `updated_by`        varchar(20)             DEFAULT NULL COMMENT '更改人',
    `updated_time`      datetime                DEFAULT NULL COMMENT '更新时间',
    `version_num`       int(9)         NOT NULL COMMENT '版本号',
    PRIMARY KEY (`sid`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='报销凭证处理-国际机票客票维度报销凭证详情记录表';

CREATE TABLE `rrh_int_reimbursement_receipt_order_detail_record`
(
    `sid`              bigint(15)     NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `ticket_no`         varchar(30)            DEFAULT NULL COMMENT '电子客票号',
    `addon_id`         varchar(40)             DEFAULT NULL COMMENT '附加产品ID',
    `voucher_type`     varchar(20)    NOT NULL COMMENT '凭证类型(发票: invoice, 行程单: ticket-receipt)',
    `voucher_sub_type` varchar(30)    NOT NULL DEFAULT 'none' COMMENT '凭证子类型(当凭证类型为发票时, 此类型来用指定发票的类型(代订机票款发票(如有遗漏请补充)))',
    `voucher_amount`   decimal(10, 2) NOT NULL COMMENT '报销凭证金额',
    `product_type`     tinyint(4)              DEFAULT NULL COMMENT '附加产品类型（1：延误保 2：专车 3：保险 4：套餐 5：邮寄）',
    `product_code`     varchar(128)            DEFAULT NULL COMMENT '附加产品代码',
    `order_id`         varchar(50)             DEFAULT NULL COMMENT '订单号',
    `detail_id`        bigint(15)              DEFAULT NULL COMMENT '邮寄任务详情ID',
    `task_status`      tinyint(4)              DEFAULT NULL COMMENT '邮寄任务状态（0:已创建，1:已取消）',
    `record_sid`       bigint(15)     NOT NULL COMMENT '国际机票报销凭证记录ID',
    `remark`           varchar(1024)           DEFAULT NULL COMMENT '备注',
    `created_by`       varchar(20)    NOT NULL COMMENT '创建人',
    `created_time`     datetime       NOT NULL COMMENT '创建时间',
    `updated_by`       varchar(20)             DEFAULT NULL COMMENT '更改人',
    `updated_time`     datetime                DEFAULT NULL COMMENT '更新时间',
    `version_num`      int(9)         NOT NULL COMMENT '版本号',
    PRIMARY KEY (`sid`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='报销凭证处理-国际机票机票附加产品关联信息记录表';
