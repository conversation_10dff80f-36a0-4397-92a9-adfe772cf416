CREATE TABLE `osb_car_order_sub_record` (
  `sid` bigint(15) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `booked_by` varchar(20) NOT NULL COMMENT '预订人phoneId',
  `trans_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '交易时间',
  `order_id` varchar(50) NOT NULL COMMENT '业务订单号',
  `order_status` varchar(10) NOT NULL COMMENT '订单状态',
  `rid` varchar(50) DEFAULT NULL COMMENT '退款请求号',
  `consume_type` varchar(20) NOT NULL COMMENT '消费类型',
  `consume_amount` decimal(10,2) DEFAULT NULL COMMENT '消费金额',
  `corp_id` varchar(30) NOT NULL COMMENT '企业ID',
  `raw_data_of_order` mediumblob NOT NULL COMMENT '订单kafka消息',
  `maycur_data_of_order` mediumblob COMMENT '每刻报销订单数据',
  `status` varchar(20) NOT NULL COMMENT '状态(received: 已消费, pushed: 已推送)',
  `created_by` varchar(20) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(20) DEFAULT NULL COMMENT '更改人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version_num` int(9) NOT NULL COMMENT '版本号',
  PRIMARY KEY (`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单订阅-专车订单订阅记录表';

CREATE TABLE `osb_domestic_flight_ticket_order_sub_record` (
  `sid` bigint(15) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `booked_by` varchar(20) NOT NULL COMMENT '预订人phoneId',
  `trans_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '交易时间',
  `order_id` varchar(50) NOT NULL COMMENT '业务订单号',
  `order_status` varchar(10) NOT NULL COMMENT '订单状态',
  `origin_order_id` varchar(50) DEFAULT NULL COMMENT '改期单原单ID',
  `rid` varchar(50) DEFAULT NULL COMMENT '退款请求号',
  `consume_type` varchar(20) DEFAULT NULL COMMENT '消费类型',
  `consume_amount` decimal(10,2) DEFAULT NULL COMMENT '消费金额',
  `corp_id` varchar(30) NOT NULL COMMENT '企业ID',
  `raw_data_of_order` mediumblob NOT NULL COMMENT '订单kafka消息',
  `maycur_data_of_order` mediumblob COMMENT '每刻报销订单数据',
  `status` varchar(20) NOT NULL COMMENT '状态(received: 已消费, pushed: 已推送)',
  `created_by` varchar(20) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(20) DEFAULT NULL COMMENT '更改人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version_num` int(9) NOT NULL COMMENT '版本号',
  PRIMARY KEY (`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单订阅-国内机票订单订阅记录表';

CREATE TABLE `osb_international_flight_ticket_order_sub_record` (
  `sid` bigint(15) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `booked_by` varchar(20) NOT NULL COMMENT '预订人phoneId',
  `trans_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '交易时间',
  `order_id` varchar(50) NOT NULL COMMENT '业务订单号',
  `order_status` varchar(10) NOT NULL COMMENT '订单状态',
  `origin_order_id` varchar(50) DEFAULT NULL COMMENT '改期单原单ID',
  `rid` varchar(50) DEFAULT NULL COMMENT '退款请求号',
  `consume_type` varchar(20) DEFAULT NULL COMMENT '消费类型',
  `consume_amount` decimal(10,2) DEFAULT NULL COMMENT '消费金额',
  `corp_id` varchar(30) NOT NULL COMMENT '企业ID',
  `raw_data_of_order` mediumblob NOT NULL COMMENT '订单kafka消息',
  `maycur_data_of_order` mediumblob COMMENT '每刻报销订单数据',
  `status` varchar(20) NOT NULL COMMENT '状态(received: 已消费, pushed: 已推送)',
  `created_by` varchar(20) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(20) DEFAULT NULL COMMENT '更改人',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version_num` int(9) NOT NULL COMMENT '版本号',
  PRIMARY KEY (`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单订阅-国际机票订单订阅记录表';
