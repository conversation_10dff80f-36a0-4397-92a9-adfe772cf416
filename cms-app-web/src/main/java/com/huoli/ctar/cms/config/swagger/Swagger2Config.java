package com.huoli.ctar.cms.config.swagger;

import static com.google.common.base.Predicates.or;
import static springfox.documentation.builders.PathSelectors.regex;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableSwagger2
public class Swagger2Config {

    @Bean
    public Docket createCtmRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("企业客户端差旅管理模块")
                //.genericModelSubstitutes(ResultBean.Success.class)
                .useDefaultResponseMessages(false)
                .forCodeGeneration(true)
                .pathMapping("/")
                .select()
                .paths(or(regex("/api/app/ctm/.*")))
                .build()
                .apiInfo(ctmApiInfo());
    }

    private ApiInfo ctmApiInfo() {
        return new ApiInfoBuilder()
                .title("企业客户端差旅管理模块")
                .description("提供差旅申请查询, 查表查询, 差旅审批等接口")
                .contact(new Contact("ShanCheng", null, "<EMAIL>"))
                .version("1.0")
                .build();
    }

}
