package com.huoli.ctar.cms.web.app.controller;

import com.huoli.ctar.core.infra.model.BaseResult;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class CmsAppController {
    @GetMapping(path = "/cms-app/info", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<String> info() {
        return new BaseResult.Builder<String>()
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .data("2.0.2")
                .build();
    }
}
