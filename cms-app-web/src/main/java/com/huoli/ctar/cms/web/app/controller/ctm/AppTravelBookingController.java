package com.huoli.ctar.cms.web.app.controller.ctm;


import com.huoli.ctar.cms.ctm.bm.mobile.service.TravelBookingMobileService;
import com.huoli.ctar.cms.ctm.tcm.mobile.service.CorpTravelEntranceInfoService;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.tmccbs.biz.domain.vo.GatewayCommonParam;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.TravelBookingMobileVO;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.TravelBookingQueryParam;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tbc.vo.tmc.mobile.CorpTravelEntranceInfoMobileVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商旅App差旅预订相关接口
 */
@RestController
@RequestMapping("/api/app/ctm")
public class AppTravelBookingController {

    @Autowired
    @Qualifier("travelBookingMobileService")
    private TravelBookingMobileService travelBookingMobileService;

    @Autowired
    @Qualifier("corpTravelEntranceInfoService")
    private CorpTravelEntranceInfoService corpTravelEntranceInfoService;

    @ApiOperation("获取差旅预订信息")
    @PostMapping(value = "/travelBookings/retrieve", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<TravelBookingMobileVO> retrieveTravelBookingDetail(TravelBookingQueryParam queryParam) {
        return new BaseResult.Builder<TravelBookingMobileVO>()
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .data(travelBookingMobileService.retrieveTravelBookingDetail(queryParam))
                .build();
    }


    @ApiOperation("获取企业预订入口信息")
    @PostMapping(value = "/corpTravelEntranceInfo/retrieve", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<CorpTravelEntranceInfoMobileVO> retrieveCorpTravelEntranceInfo(GatewayCommonParam gatewayCommonParam) {
        return new BaseResult.Builder<CorpTravelEntranceInfoMobileVO>()
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .data(corpTravelEntranceInfoService.retrieveCorpTravelEntranceInfo(gatewayCommonParam.getPhoneid()))
                .build();
    }
}
