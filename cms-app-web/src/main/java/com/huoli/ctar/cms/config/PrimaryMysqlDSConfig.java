package com.huoli.ctar.cms.config;


import com.alibaba.druid.pool.DruidDataSource;
import com.huoli.ctar.common.config.properties.CmsProperties;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = { "com.huoli.ctar.tmccbs.cms.**.mapper" }, sqlSessionTemplateRef = "primaryMysqlSqlSessionTemplate")
public class PrimaryMysqlDSConfig {

    @Autowired
    private CmsProperties cmsProperties;

    @Bean(name = "primaryMysqlDataSource")
    @Primary
    public DataSource primaryMysqlDataSource() {
        DruidDataSource primaryMysqlDataSource = new DruidDataSource();
        primaryMysqlDataSource.setValidationQuery("select 1");
        primaryMysqlDataSource.setDriverClassName(cmsProperties.getMysql().getJdbc().getDriverClassName());
        primaryMysqlDataSource.setUrl(cmsProperties.getMysql().getJdbc().getUrl());
        primaryMysqlDataSource.setUsername(cmsProperties.getMysql().getJdbc().getUsername());
        primaryMysqlDataSource.setPassword(cmsProperties.getMysql().getJdbc().getPassword());
        return primaryMysqlDataSource;
    }

    @Bean(name = "primaryMysqlSqlSessionFactory")
    @Primary
    public SqlSessionFactory primaryMysqlSqlSessionFactory(@Qualifier("primaryMysqlDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean primaryMysqlSqlSessionFactory = new SqlSessionFactoryBean();
        primaryMysqlSqlSessionFactory.setDataSource(dataSource);
        primaryMysqlSqlSessionFactory.setConfigLocation(new PathMatchingResourcePatternResolver().getResource("classpath:mybatis/config/configuration.xml"));
        primaryMysqlSqlSessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mybatis/mapper/cms/**/*.xml"));
        return primaryMysqlSqlSessionFactory.getObject();
    }

    @Bean(name = "primaryMysqlTransactionManager")
    @Primary
    public DataSourceTransactionManager primaryMysqlTransactionManager(@Qualifier("primaryMysqlDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "primaryMysqlSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate primaryMysqlSqlSessionTemplate(@Qualifier("primaryMysqlSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}

