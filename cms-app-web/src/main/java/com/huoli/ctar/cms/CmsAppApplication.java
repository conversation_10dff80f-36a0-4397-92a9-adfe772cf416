package com.huoli.ctar.cms;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@EnableTransactionManagement
@SpringBootApplication(exclude = {SecurityAutoConfiguration.class}, scanBasePackages = { "com.huoli.ctar.cms", "com.huoli.ctar.cpcms", "com.huoli.ctar.tmccbs" })
public class CmsAppApplication {

    public static void main(String[] args) {
        SpringApplication.run(CmsAppApplication.class, args);
    }
}
