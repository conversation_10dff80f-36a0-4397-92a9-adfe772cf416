package com.huoli.ctar.cms.web.app.controller.ctm;


import com.huoli.ctar.cms.ctm.tam.mobile.service.InFlightTicketTravelRequestHandleMobileService;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tam.vo.tmc.mobile.InFlightTicketTravelReqDetailMobileVO;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tam.vo.tmc.mobile.InFlightTicketTravelRequestQueryParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商旅APP差旅国际机票申请接口
 */
@Slf4j
@RestController
@RequestMapping("/api/app/ctm")
public class AppInFlightTicketTravelRequestController {

    @Autowired
    @Qualifier("inFlightTicketTravelRequestHandleMobileService")
    private InFlightTicketTravelRequestHandleMobileService inFlightTicketTravelRequestHandleMobileService;


    @ApiOperation("获取国际机票事前审批单详情")
    @PostMapping(value = "/inFlightTicket/travelRequests/detail/retrieve", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<InFlightTicketTravelReqDetailMobileVO> retrieveInFlightTicketTravelRequestDetail(InFlightTicketTravelRequestQueryParam travelRequestQueryParam) {
        return new BaseResult.Builder<InFlightTicketTravelReqDetailMobileVO>()
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .data(inFlightTicketTravelRequestHandleMobileService.retrieveTravelRequestDetail(travelRequestQueryParam.getPhoneid(), travelRequestQueryParam.getTravelRequestId()))
                .build();
    }
}
