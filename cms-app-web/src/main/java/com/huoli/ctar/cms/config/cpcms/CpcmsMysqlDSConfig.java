package com.huoli.ctar.cms.config.cpcms;


import com.alibaba.druid.pool.DruidDataSource;
import com.huoli.ctar.common.config.properties.*;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = { "com.huoli.ctar.tmccbs.cpcms.**.mapper" }, sqlSessionTemplateRef = "cpcmsMysqlSqlSessionTemplate")
public class CpcmsMysqlDSConfig {
    @Autowired
    private CpcmsProperties cpcmsProperties;

    @Bean(name = "cpcmsMysqlDataSource")
    public DataSource cpcmsMysqlDataSource() {
        DruidDataSource cpcmsMysqlDataSource = new DruidDataSource();
        cpcmsMysqlDataSource.setDriverClassName(cpcmsProperties.getMysql().getJdbc().getDriverClassName());
        cpcmsMysqlDataSource.setUrl(cpcmsProperties.getMysql().getJdbc().getUrl());
        cpcmsMysqlDataSource.setUsername(cpcmsProperties.getMysql().getJdbc().getUsername());
        cpcmsMysqlDataSource.setPassword(cpcmsProperties.getMysql().getJdbc().getPassword());
        cpcmsMysqlDataSource.setValidationQuery("select 1");
        return cpcmsMysqlDataSource;
    }

    @Bean(name = "cpcmsMysqlSqlSessionFactory")
    public SqlSessionFactory cpcmsMysqlSqlSessionFactory(@Qualifier("cpcmsMysqlDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean cpcmsMysqlSqlSessionFactory = new SqlSessionFactoryBean();
        cpcmsMysqlSqlSessionFactory.setDataSource(dataSource);
        cpcmsMysqlSqlSessionFactory.setConfigLocation(new PathMatchingResourcePatternResolver().getResource("classpath:mybatis/config/configuration.xml"));
        cpcmsMysqlSqlSessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mybatis/mapper/cpcms/**/*.xml"));
        return cpcmsMysqlSqlSessionFactory.getObject();
    }

    @Bean(name = "cpcmsMysqlTransactionManager")
    public DataSourceTransactionManager cpcmsMysqlTransactionManager(@Qualifier("cpcmsMysqlDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "cpcmsMysqlSqlSessionTemplate")
    public SqlSessionTemplate cpcmsMysqlSqlSessionTemplate(@Qualifier("cpcmsMysqlSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}

