package com.huoli.ctar.cms.web.app.controller.ctm;


import com.huoli.ctar.cms.ctm.bm.mobile.service.IftTravelBookingMobileService;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.InFlightTicketTravelBookingMobileVO;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.TravelBookingQueryParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商旅App差旅国际机票预订相关接口
 */
@RestController
@RequestMapping("/api/app/ctm")
public class AppInFlightTicketTravelBookingController {
    @Autowired
    @Qualifier("iftTravelBookingMobileService")
    private IftTravelBookingMobileService iftTravelBookingMobileService;

    @ApiOperation("获取国际机票差旅预订信息")
    @PostMapping(value = "/travelBookings/internationalFlightTicket/retrieve", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<InFlightTicketTravelBookingMobileVO> retrieveIftTravelBookingDetail(TravelBookingQueryParam queryParam) {
        return new BaseResult.Builder<InFlightTicketTravelBookingMobileVO>()
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .data(iftTravelBookingMobileService.retrieveTravelBookingDetail(queryParam))
                .build();
    }
}
