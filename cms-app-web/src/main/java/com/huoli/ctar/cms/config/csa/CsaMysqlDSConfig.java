package com.huoli.ctar.cms.config.csa;


import com.alibaba.druid.pool.DruidDataSource;
import com.huoli.ctar.common.config.properties.TmccsaProperties;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = {"com.huoli.ctar.tmccbs.csa.**.mapper"}, sqlSessionTemplateRef = "csaMysqlSqlSessionTemplate")
public class CsaMysqlDSConfig {
    @Autowired
    private TmccsaProperties tmccsaProperties;

    @Bean(name = "csaMysqlDataSource")
    public DataSource csaMysqlDataSource() {
        DruidDataSource csaMysqlDataSource = new DruidDataSource();
        csaMysqlDataSource.setDriverClassName(tmccsaProperties.getMysql().getJdbc().getDriverClassName());
        csaMysqlDataSource.setUrl(tmccsaProperties.getMysql().getJdbc().getUrl());
        csaMysqlDataSource.setUsername(tmccsaProperties.getMysql().getJdbc().getUsername());
        csaMysqlDataSource.setPassword(tmccsaProperties.getMysql().getJdbc().getPassword());
        csaMysqlDataSource.setValidationQuery("select 1");
        return csaMysqlDataSource;
    }

    @Bean(name = "csaMysqlSqlSessionFactory")
    public SqlSessionFactory csaMysqlSqlSessionFactory(@Qualifier("csaMysqlDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean csaMysqlSqlSessionFactory = new SqlSessionFactoryBean();
        csaMysqlSqlSessionFactory.setDataSource(dataSource);
        csaMysqlSqlSessionFactory.setConfigLocation(new PathMatchingResourcePatternResolver().getResource("classpath:mybatis/config/configuration.xml"));
        csaMysqlSqlSessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mybatis/mapper/tmccsa/**/*.xml"));
        return csaMysqlSqlSessionFactory.getObject();
    }

    @Bean(name = "csaMysqlTransactionManager")
    public DataSourceTransactionManager csaMysqlTransactionManager(@Qualifier("csaMysqlDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "csaMysqlSqlSessionTemplate")
    public SqlSessionTemplate csaMysqlSqlSessionTemplate(@Qualifier("csaMysqlSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}

