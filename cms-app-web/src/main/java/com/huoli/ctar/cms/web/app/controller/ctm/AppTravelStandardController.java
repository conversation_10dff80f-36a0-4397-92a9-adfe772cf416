package com.huoli.ctar.cms.web.app.controller.ctm;


import com.huoli.ctar.cms.ctm.bm.mobile.service.TravelStandardViolationMsgMobileService;
import com.huoli.ctar.cms.ctm.tsm.mobile.service.TravelTempMobileService;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.tmccbs.biz.domain.cms.ctm.tsm.model.TravelStandardHolder;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.TravelStandardViolationMsgSubmitParam;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tsm.vo.tmc.mobile.TravelStandardQueryParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;


/**
 * 商旅APP差旅标准接口
 */
@RestController
@RequestMapping("/api/app/ctm")
public class AppTravelStandardController {

    @Autowired
    @Qualifier("travelTempMobileService")
    private TravelTempMobileService travelTempMobileService;

    @Autowired
    @Qualifier("travelStandardViolationMsgMobileService")
    private TravelStandardViolationMsgMobileService travelStandardViolationMsgMobileService;

    @PostMapping(path = "/travelStandard/retrieve", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<TravelStandardHolder> retrieveTravelStandard(TravelStandardQueryParam param) {
        return new BaseResult.Builder<TravelStandardHolder>()
                .data(travelTempMobileService.retrieveTravelStandard(param.getPhoneid(), param.getServiceId(), param.getCityCodeOfBooked()))
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .msg("success")
                .build();
    }

    @ApiOperation("差标违规信息提交")
    @PostMapping(value = "/travelStandarViolationMsgs/submit", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<String> submitTravelStandardViolationMsgs(TravelStandardViolationMsgSubmitParam travelStandarViolationMsgSubmitParam) {
        travelStandardViolationMsgMobileService.submitTravelStandardViolationMsgs(travelStandarViolationMsgSubmitParam);
        return new BaseResult.Builder<String>()
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .data("差标违规信息提交成功!")
                .build();
    }

}
