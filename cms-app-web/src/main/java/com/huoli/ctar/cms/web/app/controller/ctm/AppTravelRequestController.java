package com.huoli.ctar.cms.web.app.controller.ctm;


import com.huoli.ctar.cms.ctm.processing.mobile.service.TravelRequestProcessingService;
import com.huoli.ctar.cms.ctm.tam.mobile.service.DomesticFlightTicketTravelRequestHandleMobileService;
import com.huoli.ctar.cms.ctm.tam.mobile.service.TravelRequestHandleMobileService;
import com.huoli.ctar.core.infra.exception.ServiceException;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.util.EnumUtil;
import com.huoli.ctar.tmccbs.biz.domain.cms.ctm.constant.TravelRequestStatus;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.BookingRedirectUrlParam;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.TravelProcessingRequestParam;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.bm.vo.tmc.mobile.TravelProcessingResultVO;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tam.vo.tmc.common.TravelApplyResult;
import com.huoli.ctar.tmccbs.client.domain.cms.ctm.tam.vo.tmc.mobile.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商旅APP差旅申请接口
 */
@RestController
@RequestMapping("/api/app/ctm")
public class AppTravelRequestController {

    private static final Logger log = LoggerFactory.getLogger(AppTravelRequestController.class);

    @Autowired
    @Qualifier("travelRequestHandleMobileService")
    private TravelRequestHandleMobileService travelRequestHandleMobileService;

    @Autowired
    @Qualifier("travelRequestProcessingService")
    private TravelRequestProcessingService travelRequestProcessingService;

    @Autowired
    @Qualifier("domesticFlightTicketTravelRequestHandleMobileService")
    private DomesticFlightTicketTravelRequestHandleMobileService domesticFlightTicketTravelRequestHandleMobileService;

    @ApiOperation("事前差旅申请接口")
    @PostMapping(value = "/travelRequests/applyInAdvance", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<TravelApplyResult> requestForTravel(RequestForTravelParam requestForTravelParam) {
        return new BaseResult.Builder<TravelApplyResult>()
                .data(travelRequestHandleMobileService.applyForPostBookingTravelRequest(requestForTravelParam))
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .build();
    }

    @ApiOperation("企业差旅审批")
    @PostMapping(value = "/travelRequests/approve", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<String> approveForTravel(TravelRequestOperateParam travelRequestOperateParam) {
        try {
            travelRequestHandleMobileService.handleTravelRequest(travelRequestOperateParam);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error(String.format("企业差旅审批未知错误, travelRequestId: %d, phoneId: %s, 错误消息: %s",
                    travelRequestOperateParam.getTravelRequestId(), travelRequestOperateParam.getPhoneid(), e.getMessage()), e);
            throw new ServiceException(e.getMessage(), e);
        }
        return new BaseResult.Builder<String>()
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .data("差旅申请单已" + EnumUtil.codeOf(TravelRequestStatus.class, travelRequestOperateParam.getStatus()).getName())
                .build();
    }

    @ApiOperation("企业催促差旅审批")
    @PostMapping(value = "/travelRequests/pushUrgeApprove", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<String> pushUrgeApprove(TravelRequestOperateParam travelRequestOperateParam) {
        travelRequestHandleMobileService.remindApprovePushMessage(travelRequestOperateParam);
        return new BaseResult.Builder<String>()
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .data("催审批申请已提交")
                .build();
    }

    @ApiOperation("撤销事前差旅申请")
    @PostMapping(value = "/travelRequests/cancel", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<String> cancelTravelRequest(TravelRequestCancelParam cancelParam) {
        travelRequestHandleMobileService.cancelTravelRequest(cancelParam);
        return new BaseResult.Builder<String>()
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .data("撤销事前差旅申请已提交")
                .build();
    }

    @ApiOperation("获取事前审批单详情")
    @PostMapping(value = "/travelRequests/inAdv/detail/retrieve", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<TravelRequestDetailMobileVO> retrieveTravelRequestInAdvDetail(TravelRequestQueryParam travelRequestQueryParam) {
        return new BaseResult.Builder<TravelRequestDetailMobileVO>()
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .data(domesticFlightTicketTravelRequestHandleMobileService.retrieveTravelRequestInAdvDetail(travelRequestQueryParam.getPhoneid(), travelRequestQueryParam.getTravelRequestId()))
                .build();
    }

    @ApiOperation("差旅处理")
    @PostMapping(value = "/travelProcessing", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<TravelProcessingResultVO> travelProcessing(TravelProcessingRequestParam travelProcessingRequestParam) {
        return new BaseResult.Builder<TravelProcessingResultVO>()
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .data(travelRequestProcessingService.travelProcessing(travelProcessingRequestParam))
                .build();
    }

    @ApiOperation("获取去预定跳转链接接口")
    @PostMapping(value = "/bookingRedirectUrl/retrieve", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public BaseResult<String> retrieveBookingRedirectUrl(BookingRedirectUrlParam bookingRedirectUrlParam) {
        return new BaseResult.Builder<String>()
                .code(BaseResult.SUCCESS)
                .success(BaseResult.TRUE)
                .data(travelRequestHandleMobileService.retrieveBookingRedirectUrl(bookingRedirectUrlParam))
                .build();
    }
}
