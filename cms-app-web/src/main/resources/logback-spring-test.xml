<!-- 从高到地低 OFF 、 FATAL 、 ERROR 、 WARN 、 INFO 、 DEBUG 、 TRACE 、 ALL -->
<!-- 日志输出规则 根据当前ROOT 级别，日志输出时，级别高于root默认的级别时 会输出 -->
<!-- 以下 每个配置的 filter 是过滤掉输出文件里面，会出现高级别文件，依然出现低级别的日志信息，通过filter 过滤只记录本级别的日志 -->

<!-- 属性描述 scan：性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。
	debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration scan="true" scanPeriod="60 seconds" debug="false">
  <contextName>cms</contextName>

  <!-- 定义日志文件 输入位置 -->
  <property name="log_dir" value="/root/app/logs" />
  <!-- 日志最大的历史 30天 -->
  <property name="maxHistory" value="30" />
  <property name="maxFileSize" value="50MB" />


  <!-- ERROR级别日志 -->
  <!-- 滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件 RollingFileAppender -->
  <appender name="ACCESS-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 过滤器，只记录ERROR级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <!-- 最常用的滚动策略，它根据时间来制定滚动策略.既负责滚动也负责出发滚动 -->
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!--日志输出位置 可相对、和绝对路径 -->
      <fileNamePattern>${log_dir}/%d{yyyy-MM-dd}/access/access_error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 可选节点，控制保留的归档文件的最大数量，超出数量就删除旧文件假设设置每个月滚动，且<maxHistory>是6， 则只保存最近6个月的文件，删除之前的旧文件。注意，删除旧文件是，那些为了归档而创建的目录也会被删除 -->
      <maxHistory>${maxHistory}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>${maxFileSize}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>

    <encoder>
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)</pattern>
    </encoder>
  </appender>

  <!-- INFO级别日志 appender -->
  <appender name="ACCESS-INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 过滤器，只记录INFO级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>INFO</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!-- 按天回滚 daily -->
      <fileNamePattern>${log_dir}/%d{yyyy-MM-dd}/access/access_info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 日志最大的历史 60天 -->
      <maxHistory>${maxHistory}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>${maxFileSize}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
    <encoder>
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)</pattern>
    </encoder>
  </appender>

  <appender name="TAM-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 过滤器，只记录ERROR级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <!-- 最常用的滚动策略，它根据时间来制定滚动策略.既负责滚动也负责出发滚动 -->
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!--日志输出位置 可相对、和绝对路径 -->
      <fileNamePattern>${log_dir}/%d{yyyy-MM-dd}/ctm_tam_error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 可选节点，控制保留的归档文件的最大数量，超出数量就删除旧文件假设设置每个月滚动，且<maxHistory>是6， 则只保存最近6个月的文件，删除之前的旧文件。注意，删除旧文件是，那些为了归档而创建的目录也会被删除 -->
      <maxHistory>${maxHistory}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>${maxFileSize}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>

    <encoder>
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)</pattern>
    </encoder>
  </appender>

  <appender name="TAM-INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 过滤器，只记录INFO级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>INFO</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!-- 按天回滚 daily -->
      <fileNamePattern>${log_dir}/%d{yyyy-MM-dd}/ctm_tam_info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 日志最大的历史 60天 -->
      <maxHistory>${maxHistory}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>${maxFileSize}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
    <encoder>
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)</pattern>
    </encoder>
  </appender>

  <!-- ERROR级别日志 -->
  <!-- 滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件 RollingFileAppender -->
  <appender name="CFESAG-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 过滤器，只记录ERROR级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <!-- 最常用的滚动策略，它根据时间来制定滚动策略.既负责滚动也负责出发滚动 -->
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!--日志输出位置 可相对、和绝对路径 -->
      <fileNamePattern>${log_dir}/%d{yyyy-MM-dd}/cfesag/other_error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 可选节点，控制保留的归档文件的最大数量，超出数量就删除旧文件假设设置每个月滚动，且<maxHistory>是6， 则只保存最近6个月的文件，删除之前的旧文件。注意，删除旧文件是，那些为了归档而创建的目录也会被删除 -->
      <maxHistory>${maxHistory}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>${maxFileSize}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>

    <encoder>
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)</pattern>
    </encoder>
  </appender>

  <!-- INFO级别日志 appender -->
  <appender name="CFESAG-INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 过滤器，只记录INFO级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>INFO</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!-- 按天回滚 daily -->
      <fileNamePattern>${log_dir}/%d{yyyy-MM-dd}/cfesag/other_info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 日志最大的历史 60天 -->
      <maxHistory>${maxHistory}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>${maxFileSize}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
    <encoder>
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)</pattern>
    </encoder>
  </appender>

  <!-- ERROR级别日志 -->
  <!-- 滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件 RollingFileAppender -->
  <appender name="CFESAG-PUSH-AND-PAYMENT-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 过滤器，只记录ERROR级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <!-- 最常用的滚动策略，它根据时间来制定滚动策略.既负责滚动也负责出发滚动 -->
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!--日志输出位置 可相对、和绝对路径 -->
      <fileNamePattern>${log_dir}/%d{yyyy-MM-dd}/cfesag/msg_push_and_payment_error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 可选节点，控制保留的归档文件的最大数量，超出数量就删除旧文件假设设置每个月滚动，且<maxHistory>是6， 则只保存最近6个月的文件，删除之前的旧文件。注意，删除旧文件是，那些为了归档而创建的目录也会被删除 -->
      <maxHistory>${maxHistory}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>${maxFileSize}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>

    <encoder>
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)</pattern>
    </encoder>
  </appender>

  <!-- INFO级别日志 appender -->
  <appender name="CFESAG-PUSH-AND-PAYMENT-INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 过滤器，只记录INFO级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>INFO</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!-- 按天回滚 daily -->
      <fileNamePattern>${log_dir}/%d{yyyy-MM-dd}/cfesag/msg_push_and_payment_info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 日志最大的历史 60天 -->
      <maxHistory>${maxHistory}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>${maxFileSize}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
    <encoder>
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)</pattern>
    </encoder>
  </appender>


  <!-- ERROR级别日志 -->
  <!-- 滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件 RollingFileAppender -->
  <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 过滤器，只记录ERROR级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <!-- 最常用的滚动策略，它根据时间来制定滚动策略.既负责滚动也负责出发滚动 -->
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!--日志输出位置 可相对、和绝对路径 -->
      <fileNamePattern>${log_dir}/app_error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 可选节点，控制保留的归档文件的最大数量，超出数量就删除旧文件假设设置每个月滚动，且<maxHistory>是6， 则只保存最近6个月的文件，删除之前的旧文件。注意，删除旧文件是，那些为了归档而创建的目录也会被删除 -->
      <maxHistory>${maxHistory}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>${maxFileSize}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>

    <!-- 按照固定窗口模式生成日志文件，当文件大于20MB时，生成新的日志文件。窗口大小是1到3，当保存了3个归档文件后，将覆盖最早的日志。
      <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
      <fileNamePattern>${log_dir}/%d{yyyy-MM-dd}/.log.zip</fileNamePattern> <minIndex>1</minIndex>
      <maxIndex>3</maxIndex> </rollingPolicy> -->
    <!-- 查看当前活动文件的大小，如果超过指定大小会告知RollingFileAppender 触发当前活动文件滚动 <triggeringPolicy
      class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy"> <maxFileSize>5MB</maxFileSize>
      </triggeringPolicy> -->

    <encoder>
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)</pattern>
    </encoder>
  </appender>

  <!-- WARN级别日志 appender -->
  <appender name="WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 过滤器，只记录WARN级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>WARN</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!-- 按天回滚 daily -->
      <fileNamePattern>${log_dir}/app_warn.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 日志最大的历史 60天 -->
      <maxHistory>${maxHistory}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>${maxFileSize}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
    <encoder>
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)</pattern>
    </encoder>
  </appender>

  <!-- INFO级别日志 appender -->
  <appender name="INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 过滤器，只记录INFO级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>INFO</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!-- 按天回滚 daily -->
      <fileNamePattern>${log_dir}/app_info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 日志最大的历史 60天 -->
      <maxHistory>${maxHistory}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>${maxFileSize}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
    <encoder>
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)</pattern>
    </encoder>
  </appender>

  <!-- DEBUG级别日志 appender -->
  <appender name="DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 过滤器，只记录DEBUG级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>DEBUG</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!-- 按天回滚 daily -->
      <fileNamePattern>${log_dir}/app_debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 日志最大的历史 60天 -->
      <maxHistory>${maxHistory}</maxHistory>
      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>${maxFileSize}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
    <encoder>
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)</pattern>
    </encoder>
  </appender>

  <!-- TRACE级别日志 appender -->
  <appender name="TRACE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 过滤器，只记录ERROR级别的日志 -->
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>TRACE</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <!-- 按天回滚 daily -->
      <fileNamePattern>${log_dir}/app_trace.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <!-- 日志最大的历史 60天 -->
      <maxHistory>${maxHistory}</maxHistory>

      <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
        <maxFileSize>${maxFileSize}</maxFileSize>
      </timeBasedFileNamingAndTriggeringPolicy>
    </rollingPolicy>
    <encoder>
      <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)</pattern>
    </encoder>
  </appender>

  <logger name="java.sql.PreparedStatement" value="DEBUG" />
  <logger name="java.sql.Connection" value="DEBUG" />
  <logger name="java.sql.Statement" value="DEBUG" />
  <logger name="com.ibatis" value="DEBUG" />
  <logger name="com.ibatis.common.jdbc.SimpleDataSource" value="DEBUG" />
  <logger name="com.ibatis.common.jdbc.ScriptRunner" level="DEBUG" />
  <logger name="com.ibatis.sqlmap.engine.impl.SqlMapClientDelegate" value="DEBUG" />
  <logger name="com.apache.ibatis" level="TRACE" />

  <logger name="com.huoli.ctar.cms.access" level="INFO" additivity="false">
    <appender-ref ref="ACCESS-ERROR"/>
    <appender-ref ref="ACCESS-INFO"/>
  </logger>

  <logger name="com.huoli.ctar.cms.ctm.tam" level="INFO" additivity="false">
    <appender-ref ref="TAM-ERROR"/>
    <appender-ref ref="TAM-INFO"/>
  </logger>

  <logger name="com.huoli.ctar.cfesag.client.deligator" level="INFO" additivity="false">
    <appender-ref ref="CFESAG-ERROR"/>
    <appender-ref ref="CFESAG-INFO"/>
  </logger>

  <logger name="com.huoli.ctar.cfesag.client.deligator.pushcenter" level="INFO" additivity="false">
    <appender-ref ref="CFESAG-PUSH-AND-PAYMENT-ERROR"/>
    <appender-ref ref="CFESAG-PUSH-AND-PAYMENT-INFO"/>
  </logger>

  <logger name="com.huoli.ctar.cfesag.client.deligator.payment" level="INFO" additivity="false">
    <appender-ref ref="CFESAG-PUSH-AND-PAYMENT-ERROR"/>
    <appender-ref ref="CFESAG-PUSH-AND-PAYMENT-INFO"/>
  </logger>

  <!-- root级别 DEBUG -->
  <root level="debug">
    <!-- 文件输出 -->
    <appender-ref ref="ERROR" />
    <appender-ref ref="INFO" />
    <appender-ref ref="WARN" />
    <appender-ref ref="DEBUG" />
    <appender-ref ref="TRACE" />
  </root>
</configuration>