<configuration scan="true" scanPeriod="60 seconds" debug="false">
  <contextName>cms</contextName>

  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>
        [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%X{traceId}] %highlight([%-5level] %logger{50} - %msg%n)
      </pattern>
    </encoder>
  </appender>

  <logger name="java.sql.PreparedStatement" value="DEBUG" />
  <logger name="java.sql.Connection" value="DEBUG" />
  <logger name="java.sql.Statement" value="DEBUG" />
  <logger name="com.ibatis" value="DEBUG" />
  <logger name="com.ibatis.common.jdbc.SimpleDataSource" value="DEBUG" />
  <logger name="com.ibatis.common.jdbc.ScriptRunner" level="DEBUG" />
  <logger name="com.ibatis.sqlmap.engine.impl.SqlMapClientDelegate" value="DEBUG" />
  <logger name="com.apache.ibatis" level="TRACE" />

  <!-- root级别 DEBUG -->
  <root level="debug">
    <!-- 控制台输出 -->
    <appender-ref ref="STDOUT" />
  </root>
</configuration>