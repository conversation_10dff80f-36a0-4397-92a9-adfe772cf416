server.port=9091
spring.profiles.active=dev

logging.config=${ctar.common.logging-config}

## 邮件服务器配置
spring.mail.host=${ctar.common.mail.host}
spring.mail.username=${ctar.common.mail.username}
spring.mail.password=${ctar.common.mail.password}
spring.mail.default-encoding=${ctar.common.mail.default-encoding}
## SSL证书Socket工厂
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
## 使用SMTPS协议465端口
spring.mail.properties.mail.smtp.socketFactory.port=465
## 避免530的错误
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
## 避免530的错误


## thymeleaf 模板引擎配置
spring.thymeleaf.prefix=classpath:/email/templates/
spring.thymeleaf.encoding=UTF-8

spring.redis.host=${ctar.common.redis.host}
spring.redis.password=${ctar.common.redis.password}
spring.redis.port=${ctar.common.redis.port}

com.huoli.ctar.common.web.enable-param-validation=false
com.huoli.ctar.cfesag.host=${ctar.cfesag.host}