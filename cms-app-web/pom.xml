<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>cms</artifactId>
    <groupId>com.huoli.ctar</groupId>
    <version>2.0.2</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>cms-app-web</artifactId>
  <version>2.0.2</version>
  <description>企业管理系统cms 对企业差旅app提供接口</description>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.huoli.ctar.common</groupId>
      <artifactId>common-web-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid</artifactId>
    </dependency>

    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
    </dependency>

    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger2</artifactId>
    </dependency>

    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger-ui</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-cms-basic-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-cpcms-basic-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-csa-basic-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-csa-basic-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-travel-common-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-cam-tmc-common-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-tam-common-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-pam-tmc-common-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-pbm-tmc-common-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-tbm-tmc-common-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-cam-cache-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-tam-cache-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>cms-pc-pbm-mobile-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>cms-travel-tam-mobile-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>cms-travel-tcm-mobile-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>cms-travel-processing-mobile-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>cms-travel-bm-mobile-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>cms-travel-tsm-mobile-server</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar.common</groupId>
      <artifactId>common-config</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-common-web-config</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-budget-tmc-common-server</artifactId>
    </dependency>

    <dependency>
      <groupId>org.mybatis.spring.boot</groupId>
      <artifactId>mybatis-spring-boot-starter</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework</groupId>
          <artifactId>spring-beans</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-tx</artifactId>
    </dependency>
  </dependencies>

  <build>
    <finalName>cms-app</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.1.3.RELEASE</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.22.1</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>2.8.2</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-install-plugin</artifactId>
        <version>2.5.2</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>

      <!-- mybatis逆向工程 -->
      <plugin>
        <groupId>org.mybatis.generator</groupId>
        <artifactId>mybatis-generator-maven-plugin</artifactId>
        <version>1.3.2</version>
        <configuration>
          <!--配置文件的位置-->
          <configurationFile>src/main/resources/generatorConfig.xml</configurationFile>
          <verbose>true</verbose>
          <overwrite>true</overwrite>
        </configuration>
      </plugin>
    </plugins>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
        <excludes>
          <exclude>application-dev.properties</exclude>
          <exclude>application-preprod.properties</exclude>
          <exclude>application-prod.properties</exclude>
          <exclude>application-test.properties</exclude>
          <exclude>logback-spring-dev.xml</exclude>
          <exclude>logback-spring-preprod.xml</exclude>
          <exclude>logback-spring-prod.xml</exclude>
          <exclude>logback-spring-test.xml</exclude>
        </excludes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
        <includes>
          <include>application-${profileActive}.properties</include>
          <include>logback-spring-${profileActive}.xml</include>
        </includes>
      </resource>
    </resources>
  </build>
</project>
