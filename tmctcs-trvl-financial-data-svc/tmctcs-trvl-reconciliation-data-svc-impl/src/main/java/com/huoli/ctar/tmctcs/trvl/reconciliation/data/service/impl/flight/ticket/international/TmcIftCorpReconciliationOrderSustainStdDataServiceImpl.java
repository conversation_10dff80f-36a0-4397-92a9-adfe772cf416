package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.flight.ticket.international;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.db.IftCorpReconciliationOrderConsRecDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.db.IftCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.db.IftCorpReconciliationOrderDetailConsRecDbService;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.FlightTicketCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.international.IftCorpReconciliationOrderStdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("tmcIftCorpReconciliationOrderSustainStdDataService")
public class TmcIftCorpReconciliationOrderSustainStdDataServiceImpl
        extends IftCorpReconciliationOrderSustainStdDataService {

    @Autowired
    @Qualifier("iftCorpReconciliationOrderDbService")
    private IftCorpReconciliationOrderDbService iftCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("iftCorpReconciliationOrderConsRecDbService")
    private IftCorpReconciliationOrderConsRecDbService iftCorpReconciliationOrderConsRecDbService;

    @Autowired
    @Qualifier("iftCorpReconciliationOrderDetailConsRecDbService")
    private IftCorpReconciliationOrderDetailConsRecDbService iftCorpReconciliationOrderDetailConsRecDbService;

    @Autowired
    @Qualifier("tmcIftCorpReconciliationOrderStdDataService")
    private CorpReconciliationOrderStdDataService<IftCorpReconciliationOrderStdVO, FlightTicketCorpReconciliationOrderQueryParam> tmcIftCorpReconciliationOrderStdDataService;

    @Override
    protected void persistReconciliationOrder(IftCorpReconciliationOrder newEntity) {
        iftCorpReconciliationOrderDbService.dbInsert(newEntity);
    }

    @Override
    protected int doUpdateReconciliationOrder(IftCorpReconciliationOrder example) {
        return iftCorpReconciliationOrderDbService.dbUpdate(example);
    }

    @Override
    protected int deleteCorpReconciliationOrders(Map<String, Object> deleteParams) {
        return iftCorpReconciliationOrderDbService.deleteByParams(deleteParams);
    }

    @Override
    protected List<IftCorpReconciliationOrderStdVO> getExistingReconciliationOrders(final String orderId) {
        return tmcIftCorpReconciliationOrderStdDataService.getReconciliationOrders(orderId, buildQueryCfg());
    }

    @Override
    protected IftCorpReconciliationOrderStdVO getExistingReconciliationOrder(Long reconciliationOrderId) {
        return tmcIftCorpReconciliationOrderStdDataService.getReconciliationOrder(reconciliationOrderId, buildQueryCfg());
    }

    @Override
    protected void persistCorpReconciliationOrderConsRecs(List<IftCorpReconciliationOrderConsRec> reconciliationOrderConsRecs) {
        iftCorpReconciliationOrderConsRecDbService.batchInsert(reconciliationOrderConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderConsRecs(Map<String, Object> deleteParams) {
        return iftCorpReconciliationOrderConsRecDbService.deleteByParams(deleteParams);
    }

    @Override
    protected void persistCorpReconciliationOrderDetailConsRecs(List<IftCorpReconciliationOrderDetailConsRec> reconciliationOrderDetailConsRecs) {
        iftCorpReconciliationOrderDetailConsRecDbService.batchInsert(reconciliationOrderDetailConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderDetailConsRecs(Map<String, Object> deleteParams) {
        return iftCorpReconciliationOrderDetailConsRecDbService.deleteByParams(deleteParams);
    }
}
