package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.core.infra.constant.BaseShowColumn;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.constant.CorpCategory;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.BaseCorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.view.CorpReconciliationOrderViewStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.entity.ConsOrderCorpInfoShowColumn;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.*;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.BaseCorpReconciliationOrderStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.CorpReconciliationOrderQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.CorpReconciliationOrderQueryParam;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public abstract class AbstractCorpReconciliationOrderViewStdDataService<
        E extends BaseCorpReconciliationOrder,
        CR extends ComparableCorpOrderConsRecStdVO,
        DCR extends ComparableCorpOrderDetailConsRecStdVO<? extends OrderConsRecBizInfoStdVO, ? extends CorpOrderDetailConsRecUniqueKey>,
        R extends BaseCorpReconciliationOrderStdVO<CR, DCR>,
        Q extends CorpReconciliationOrderQueryParam,
        CRQ extends CorpOrderConsRecQueryParam<? extends CorpOrderConsInfoQueryParam>,
        DCRQ extends CorpOrderDetailConsRecQueryParam<? extends CorpOrderConsInfoQueryParam, ? extends ConsRecBizInfoQueryParam>>
        extends BaseCorpReconciliationOrderStdDataService<E, CR, DCR, R>
        implements CorpReconciliationOrderViewStdDataService<CR, DCR, R, Q> {

    @Override
    public int countReconciliationOrders(final Q queryParam) {
        if (Objects.isNull(queryParam)) {
            return GlobalConstant.ZERO;
        }
        final Map<String, Object> queryParams = queryParam.toQueryParams();
        if (ListUtil.isEmpty(queryParams)) {
            return GlobalConstant.ZERO;
        }
        return countReconciliationOrders(queryParams);
    }

    @Override
    public List<R> queryReconciliationOrders(final Q queryParam, final CorpReconciliationOrderQueryCfg queryCfg) {
        if (Objects.isNull(queryParam)) {
            return Collections.emptyList();
        }
        final Map<String, Object> queryParams = queryParam.toQueryParams();
        if (ListUtil.isEmpty(queryParams)) {
            return Collections.emptyList();
        }
        final List<String> showColumns = Objects.isNull(queryCfg) ? null : queryCfg.getShowColumns();
        addDynamicShowColumnsIfNecessary(showColumns, BaseShowColumn.ID.getName(), ConsOrderCorpInfoShowColumn.CORP_BIZ_TYPE.getName());
        final List<E> entities = searchReconciliationOrders(queryParams, showColumns);
        if (ListUtil.isEmpty(entities)) {
            return Collections.emptyList();
        }
        final Map<String, List<R>> reconciliationOrdersGroupByCorpBizType = entities.stream()
                .map(this::transferToStdVO)
                .collect(Collectors.groupingBy(R::getCorpBizType));
        final List<R> tmcReconciliationOrders = reconciliationOrdersGroupByCorpBizType.get(CorpCategory.BIZ_TRVL_CORP.getCode());
        final List<R> resaleReconciliationOrders = reconciliationOrdersGroupByCorpBizType.get(CorpCategory.RESALE_CORP.getCode());

        final List<Long> tmcReconciliationOrderIds = ListUtil.isEmpty(tmcReconciliationOrders)
                ? Collections.emptyList() : tmcReconciliationOrders.stream().map(R::getId).collect(Collectors.toList());
        final List<Long> resaleReconciliationOrdersIds = ListUtil.isEmpty(resaleReconciliationOrders)
                ? Collections.emptyList() : resaleReconciliationOrders.stream().map(R::getId).collect(Collectors.toList());

        if (Objects.nonNull(queryCfg) && StringUtils.equals(GlobalConstant.FLAG_YES_VALUE, queryCfg.getNeedConsRecsFlag())) {
            if (ListUtil.isNotEmpty(tmcReconciliationOrderIds)) {
                final Map<Long, List<CR>> tmcConsRecsMap =
                        retrieveConsRecs(queryParam, CorpCategory.BIZ_TRVL_CORP.getCode(), tmcReconciliationOrderIds, queryCfg.getConsRecQueryCfg());
                assembleConsRecs(tmcReconciliationOrders, tmcConsRecsMap);
            }
            if (ListUtil.isNotEmpty(resaleReconciliationOrdersIds)) {
                final Map<Long, List<CR>> resaleConsRecsMap =
                        retrieveConsRecs(queryParam, CorpCategory.RESALE_CORP.getCode(), resaleReconciliationOrdersIds, queryCfg.getConsRecQueryCfg());
                assembleConsRecs(resaleReconciliationOrders, resaleConsRecsMap);
            }
        }
        if (Objects.nonNull(queryCfg) && StringUtils.equals(GlobalConstant.FLAG_YES_VALUE, queryCfg.getNeedDetailConsRecsFlag())) {
            if (ListUtil.isNotEmpty(tmcReconciliationOrderIds)) {
                final Map<Long, List<DCR>> tmcDetailConsRecsMap =
                        retrieveDetailConsRecs(queryParam, CorpCategory.BIZ_TRVL_CORP.getCode(), tmcReconciliationOrderIds, queryCfg.getDetailConsRecQueryCfg());
                assembleDetailConsRecs(tmcReconciliationOrders, tmcDetailConsRecsMap);
            }
            if (ListUtil.isNotEmpty(resaleReconciliationOrdersIds)) {
                final Map<Long, List<DCR>> resaleDetailConsRecsMap =
                        retrieveDetailConsRecs(queryParam, CorpCategory.RESALE_CORP.getCode(), resaleReconciliationOrdersIds, queryCfg.getDetailConsRecQueryCfg());
                assembleDetailConsRecs(resaleReconciliationOrders, resaleDetailConsRecsMap);
            }
        }

        final List<R> reconciliationOrders = new ArrayList<>(tmcReconciliationOrderIds.size() + resaleReconciliationOrdersIds.size());
        if (ListUtil.isNotEmpty(tmcReconciliationOrders)) {
            reconciliationOrders.addAll(tmcReconciliationOrders);
        }
        if (ListUtil.isNotEmpty(resaleReconciliationOrders)) {
            reconciliationOrders.addAll(resaleReconciliationOrders);
        }
        return reconciliationOrders;
    }

    protected CRQ buildConsRecQueryParam(final Q queryParam,
                                         final List<Long> consRecIds,
                                         final String corpBizType) {
        final CRQ consRecQueryParam = buildConsRecQueryParam(queryParam);
        consRecQueryParam.setConsRecIds(consRecIds);
        if (Objects.isNull(consRecQueryParam.getCorpInfo())) {
            final ConsOrderCorpInfoQueryParam corpInfo = new ConsOrderCorpInfoQueryParam();
            consRecQueryParam.setCorpInfo(corpInfo);
        }
        consRecQueryParam.getCorpInfo().setCorpBizType(corpBizType);
        return consRecQueryParam;
    }

    protected DCRQ buildDetailConsRecQueryParam(final Q queryParam,
                                                final List<Long> detailConsRecIds,
                                                final String corpBizType) {
        final DCRQ detailConsRecQueryParam = buildDetailConsRecQueryParam(queryParam);
        detailConsRecQueryParam.setDetailConsRecIds(detailConsRecIds);
        if (Objects.isNull(detailConsRecQueryParam.getCorpInfo())) {
            final ConsOrderCorpInfoQueryParam corpInfo = new ConsOrderCorpInfoQueryParam();
            detailConsRecQueryParam.setCorpInfo(corpInfo);
        }
        detailConsRecQueryParam.getCorpInfo().setCorpBizType(corpBizType);
        return detailConsRecQueryParam;
    }

    protected abstract CRQ buildConsRecQueryParam(final Q queryParam);

    protected abstract DCRQ buildDetailConsRecQueryParam(final Q queryParam);

    protected Map<Long, List<CR>> retrieveConsRecs(final Q queryParam,
                                                   final String corpBizType,
                                                   final List<Long> reconciliationOrderIds,
                                                   final CorpOrderConsRecQueryCfg consRecQueryCfg) {

        final Map<Long, List<Long>> conRecIdsGrpByReconciliationOrderId = getConsRecIds(reconciliationOrderIds, corpBizType);
        final Set<Long> consRecIds = new HashSet<>();
        conRecIdsGrpByReconciliationOrderId.values().forEach(consRecIds::addAll);
        final CRQ consRecQueryParam = buildConsRecQueryParam(queryParam, new ArrayList<>(consRecIds), corpBizType);
        addShowColumnIfNecessary(consRecQueryCfg.getShowColumns(), BaseShowColumn.ID.getName());
        final List<CR> consRecs = queryConsRecs(consRecQueryParam, consRecQueryCfg);
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }

        final Map<Long, CR> consRecMap = consRecs.stream().collect(Collectors.toMap(CR::getId, item -> item));
        final Map<Long, List<CR>> consRecsGrpByReconciliationOrderId = new HashMap<>(reconciliationOrderIds.size());
        for (final Long reconciliationOrderId : conRecIdsGrpByReconciliationOrderId.keySet()) {
            final List<Long> groupedConsRecIds = conRecIdsGrpByReconciliationOrderId.get(reconciliationOrderId);
            final List<CR> groupedConsRecs = new ArrayList<>(groupedConsRecIds.size());
            for (final Long consRecId : groupedConsRecIds) {
                if (consRecMap.containsKey(consRecId)) {
                    groupedConsRecs.add(consRecMap.get(consRecId));
                }
            }
            consRecsGrpByReconciliationOrderId.put(reconciliationOrderId, groupedConsRecs);
        }
        return consRecsGrpByReconciliationOrderId;
    }

    protected Map<Long, List<DCR>> retrieveDetailConsRecs(final Q queryParam,
                                                          final String corpBizType,
                                                          final List<Long> reconciliationOrderIds,
                                                          final CorpOrderDetailConsRecQueryCfg detailConsRecQueryCfg) {
        final Map<Long, List<Long>> detailConRecIdsGrpByReconciliationOrderId = getDetailConsRecIs(reconciliationOrderIds, corpBizType);
        final Set<Long> detailConsRecIds = new HashSet<>();
        detailConRecIdsGrpByReconciliationOrderId.values().forEach(detailConsRecIds::addAll);
        final DCRQ detailConsRecQueryParam = buildDetailConsRecQueryParam(queryParam, new ArrayList<>(detailConsRecIds), corpBizType);
        addShowColumnIfNecessary(detailConsRecQueryCfg.getShowColumns(), BaseShowColumn.ID.getName());
        final List<DCR> detailConsRecs = queryDetailConsRecs(detailConsRecQueryParam, detailConsRecQueryCfg);
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        final Map<Long, DCR> detailConsRecMap = detailConsRecs.stream().collect(Collectors.toMap(DCR::getId, item -> item));
        final Map<Long, List<DCR>> detailConsRecsGrpByReconciliationOrderId = new HashMap<>(reconciliationOrderIds.size());
        for (final Long reconciliationOrderId : detailConRecIdsGrpByReconciliationOrderId.keySet()) {
            final List<Long> groupedDetailConsRecIds = detailConRecIdsGrpByReconciliationOrderId.get(reconciliationOrderId);
            final List<DCR> groupedDetailConsRecs = new ArrayList<>(groupedDetailConsRecIds.size());
            for (final Long detailConsRecId : groupedDetailConsRecIds) {
                if (detailConsRecMap.containsKey(detailConsRecId)) {
                    groupedDetailConsRecs.add(detailConsRecMap.get(detailConsRecId));
                }
            }
            detailConsRecsGrpByReconciliationOrderId.put(reconciliationOrderId, groupedDetailConsRecs);
        }
        return detailConsRecsGrpByReconciliationOrderId;
    }

    protected abstract Map<Long, List<Long>> getConsRecIds(final List<Long> reconciliationOrderIds, final String corpBizType);

    protected abstract List<CR> queryConsRecs(final CRQ queryParam, final CorpOrderConsRecQueryCfg queryCfg);

    protected abstract List<DCR> queryDetailConsRecs(final DCRQ queryParam, final CorpOrderDetailConsRecQueryCfg queryCfg);

    protected void assembleConsRecs(final List<R> reconciliationOrders,
                                    final Map<Long, List<CR>> consRecsMap) {
        if (ListUtil.isEmpty(consRecsMap)) {
            return;
        }
        for (final R reconciliationOrder : reconciliationOrders) {
            if (consRecsMap.containsKey(reconciliationOrder.getId())) {
                reconciliationOrder.setConsRecs(consRecsMap.get(reconciliationOrder.getId()));
            }
        }
    }

    protected void assembleDetailConsRecs(final List<R> reconciliationOrders,
                                          final Map<Long, List<DCR>> detailConsRecsMap) {
        if (ListUtil.isEmpty(detailConsRecsMap)) {
            return;
        }
        for (final R reconciliationOrder : reconciliationOrders) {
            if (detailConsRecsMap.containsKey(reconciliationOrder.getId())) {
                reconciliationOrder.setDetailConsRecs(detailConsRecsMap.get(reconciliationOrder.getId()));
            }
        }
    }

    protected abstract Map<Long, List<Long>> getDetailConsRecIs(final List<Long> reconciliationOrderIds, final String corpBizType);

    protected abstract int countReconciliationOrders(final Map<String, Object> queryParams);

    protected abstract List<E> searchReconciliationOrders(final Map<String, Object> queryParams,
                                                          final List<String> showColumns);
}
