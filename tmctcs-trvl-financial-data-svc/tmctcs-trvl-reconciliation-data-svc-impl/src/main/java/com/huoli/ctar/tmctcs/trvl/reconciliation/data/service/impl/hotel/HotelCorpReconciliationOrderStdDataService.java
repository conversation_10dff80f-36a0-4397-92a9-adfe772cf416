package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.hotel;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.hotel.HotelCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.hotel.HotelCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.hotel.HotelCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.hotel.HotelCorpReconciliationOrderStdVO;

import java.util.Objects;

public abstract class HotelCorpReconciliationOrderStdDataService
        extends AbstractCorpReconciliationOrderStdDataService<
        HotelCorpReconciliationOrder, HotelCorpOrderConsRecStdVO, HotelCorpOrderDetailConsRecStdVO,
        HotelCorpReconciliationOrderStdVO, HotelCorpReconciliationOrderQueryParam> {

    @Override
    protected HotelCorpReconciliationOrderQueryParam buildQueryParam(final String orderId) {
        return new HotelCorpReconciliationOrderQueryParam.Builder()
                .orderId(orderId)
                .build();
    }

    @Override
    protected HotelCorpReconciliationOrderQueryParam buildQueryParam(Long reconciliationOrderId) {
        return new HotelCorpReconciliationOrderQueryParam.Builder()
                .reconciliationOrderId(reconciliationOrderId)
                .build();
    }

    @Override
    public HotelCorpReconciliationOrderStdVO transferToStdVO(HotelCorpReconciliationOrder entity) {
        final HotelCorpReconciliationOrderStdVO stdVO = new HotelCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        stdVO.setNumOfRoomNights(entity.getNumOfRoomNights());
        stdVO.setHotelType(entity.getHotelType());
        stdVO.setHotelName(entity.getHotelName());
        stdVO.setResourceChannel(entity.getResourceChannel());
        if (Objects.nonNull(entity.getCheckInDate())) {
            stdVO.setCheckInDate(entity.getCheckInDate().getTime());
        }
        if (Objects.nonNull(entity.getCheckOutDate())) {
            stdVO.setCheckOutDate(entity.getCheckOutDate().getTime());
        }
        return stdVO;
    }
}
