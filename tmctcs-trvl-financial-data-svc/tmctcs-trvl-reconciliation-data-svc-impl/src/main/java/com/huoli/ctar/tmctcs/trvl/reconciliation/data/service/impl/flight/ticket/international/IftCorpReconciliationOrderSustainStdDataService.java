package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.flight.ticket.international;

import com.huoli.ctar.core.infra.constant.BaseShowColumn;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderSustainStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.entity.CorpOrderConsRecShowColumn;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.CorpReconciliationOrderQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.FlightTicketCorpReconciliationOrderUpdateSummary;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.international.IftCorpReconciliationOrderStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.international.IftCorpReconciliationOrderSustainStdVO;

import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

public abstract class IftCorpReconciliationOrderSustainStdDataService
        extends AbstractCorpReconciliationOrderSustainStdDataService<
        IftCorpReconciliationOrder, IftCorpReconciliationOrderSustainStdVO, FlightTicketCorpReconciliationOrderUpdateSummary,
        IftCorpReconciliationOrderConsRec, IftCorpReconciliationOrderDetailConsRec,
        IftCorpReconciliationOrderStdVO> {

    protected CorpReconciliationOrderQueryCfg buildQueryCfg() {
        return new CorpReconciliationOrderQueryCfg.QueryCfgBuilder()
                .needConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .consRecQueryCfg(new CorpOrderConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(),
                                CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .needDetailConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .detailConsRecQueryCfg(new CorpOrderDetailConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(),
                                CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .addShowColumns(IftCorpReconciliationOrder.allColumns())
                .build();
    }

    @Override
    protected IftCorpReconciliationOrderConsRec buildCorpReconciliationOrderConsRec(Long consRecId, Long reconciliationOrderId) {
        return new IftCorpReconciliationOrderConsRec(reconciliationOrderId, consRecId);
    }

    @Override
    protected IftCorpReconciliationOrderDetailConsRec buildCorpReconciliationOrderDetailConsRec(Long detailConsRecId, Long reconciliationOrderId) {
        return new IftCorpReconciliationOrderDetailConsRec(reconciliationOrderId, detailConsRecId);
    }

    @Override
    protected IftCorpReconciliationOrder toNewEntity(IftCorpReconciliationOrderSustainStdVO reconciliationOrder) {
        final IftCorpReconciliationOrder newEntity = new IftCorpReconciliationOrder();
        assembleCommonAttributes(newEntity, reconciliationOrder);
        assembleCommonAttributesOnCreation(newEntity, reconciliationOrder);

        newEntity.setTicketId(reconciliationOrder.getTicketId());
        newEntity.setTmcTicketNo(reconciliationOrder.getTmcTicketNo());
        newEntity.setTicketStatus(reconciliationOrder.getTicketStatus());
        if (Objects.nonNull(reconciliationOrder.getDepTime())) {
            newEntity.setDepTime(new Date(reconciliationOrder.getDepTime()));
        }
        if (Objects.nonNull(reconciliationOrder.getArrTime())) {
            newEntity.setArrTime(new Date(reconciliationOrder.getArrTime()));
        }
        return newEntity;
    }

    @Override
    protected IftCorpReconciliationOrder transferToEntity(FlightTicketCorpReconciliationOrderUpdateSummary updateSummary) {
        final IftCorpReconciliationOrder example = new IftCorpReconciliationOrder();
        assembleCommonAttributes(example, updateSummary);

        example.setTicketId(updateSummary.getUpdateInfo().getTicketId());
        example.setTmcTicketNo(updateSummary.getUpdateInfo().getTmcTicketNo());
        example.setTicketStatus(updateSummary.getUpdateInfo().getTicketStatus());
        if (Objects.nonNull(updateSummary.getUpdateInfo().getDepTime())) {
            example.setDepTime(new Date(updateSummary.getUpdateInfo().getDepTime()));
        }
        if (Objects.nonNull(updateSummary.getUpdateInfo().getArrTime())) {
            example.setArrTime(new Date(updateSummary.getUpdateInfo().getArrTime()));
        }
        return example;
    }

    @Override
    protected FlightTicketCorpReconciliationOrderUpdateSummary getUpdateSummary(IftCorpReconciliationOrderSustainStdVO reconciliationOrder,
                                                                                IftCorpReconciliationOrderStdVO existingReconciliationOrder) {
        return reconciliationOrder.getUpdateSummary(existingReconciliationOrder);
    }
}
