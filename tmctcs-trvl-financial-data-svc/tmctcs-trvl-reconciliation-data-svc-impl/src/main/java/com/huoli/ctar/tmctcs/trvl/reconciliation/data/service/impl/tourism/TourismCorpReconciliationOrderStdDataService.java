package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.tourism;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.tourism.TourismCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.tourism.TourismCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.tourism.TourismCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.tourism.TourismCorpReconciliationOrderStdVO;

public abstract class TourismCorpReconciliationOrderStdDataService
        extends AbstractCorpReconciliationOrderStdDataService<
        TourismCorpReconciliationOrder, TourismCorpOrderConsRecStdVO, TourismCorpOrderDetailConsRecStdVO,
        TourismCorpReconciliationOrderStdVO, TourismCorpReconciliationOrderQueryParam> {

    @Override
    protected TourismCorpReconciliationOrderQueryParam buildQueryParam(final String orderId) {
        return new TourismCorpReconciliationOrderQueryParam.Builder()
                .orderId(orderId)
                .build();
    }

    @Override
    protected TourismCorpReconciliationOrderQueryParam buildQueryParam(Long reconciliationOrderId) {
        return new TourismCorpReconciliationOrderQueryParam.Builder()
                .reconciliationOrderId(reconciliationOrderId)
                .build();
    }

    @Override
    public TourismCorpReconciliationOrderStdVO transferToStdVO(TourismCorpReconciliationOrder entity) {
        final TourismCorpReconciliationOrderStdVO stdVO = new TourismCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        return stdVO;
    }
}
