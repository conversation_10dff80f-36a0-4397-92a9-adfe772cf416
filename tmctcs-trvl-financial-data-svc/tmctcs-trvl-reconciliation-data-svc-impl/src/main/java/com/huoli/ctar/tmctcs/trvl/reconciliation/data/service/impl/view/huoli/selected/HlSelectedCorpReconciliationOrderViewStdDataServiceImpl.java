package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.huoli.selected;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.constant.CorpCategory;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.AbstractCorpReconciliationOrderViewStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.huoli.selected.internal.HlSelectedCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.huoli.selected.internal.HlSelectedCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.view.huoli.selected.HlSelectedCorpReconciliationOrderViewService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.ConsOrderCorpInfoQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.huoli.selected.*;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.huoli.selected.HlSelectedCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.huoli.selected.HlSelectedCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.huoli.selected.HlSelectedCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.huoli.selected.HlSelectedCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.huoli.selected.HlSelectedCorpReconciliationOrderStdVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("hlSelectedCorpReconciliationOrderViewStdDataService")
public class HlSelectedCorpReconciliationOrderViewStdDataServiceImpl
        extends AbstractCorpReconciliationOrderViewStdDataService<HlSelectedCorpReconciliationOrder,
        HlSelectedCorpOrderConsRecStdVO, HlSelectedCorpOrderDetailConsRecStdVO, HlSelectedCorpReconciliationOrderStdVO,
        HlSelectedCorpReconciliationOrderQueryParam, HlSelectedCorpOrderConsRecQueryParam, HlSelectedCorpOrderDetailConsRecQueryParam> {

    @Autowired
    @Qualifier("hlSelectedCorpReconciliationOrderViewService")
    private HlSelectedCorpReconciliationOrderViewService hlSelectedCorpReconciliationOrderViewService;

    @Autowired
    @Qualifier("hlSelectedCorpReconciliationOrderConsRecInternalService")
    private HlSelectedCorpReconciliationOrderConsRecInternalService hlSelectedCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("hlSelectedCorpReconciliationOrderDetailConsRecInternalService")
    private HlSelectedCorpReconciliationOrderDetailConsRecInternalService hlSelectedCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("hlSelectedCorpOrderConsRecViewStdDataService")
    private CorpOrderConsRecStdDataService<HlSelectedCorpOrderConsRecStdVO, HlSelectedCorpOrderConsRecQueryParam> hlSelectedCorpOrderConsRecViewStdDataService;

    @Autowired
    @Qualifier("hlSelectedCorpOrderDetailConsRecViewStdDataService")
    private CorpOrderDetailConsRecStdDataService<HlSelectedCorpOrderDetailConsRecStdVO, HlSelectedCorpOrderDetailConsRecQueryParam> hlSelectedCorpOrderDetailConsRecViewStdDataService;

    @Override
    public HlSelectedCorpReconciliationOrderStdVO transferToStdVO(HlSelectedCorpReconciliationOrder entity) {
        final HlSelectedCorpReconciliationOrderStdVO stdVO = new HlSelectedCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        return stdVO;
    }

    @Override
    protected HlSelectedCorpOrderConsRecQueryParam buildConsRecQueryParam(HlSelectedCorpReconciliationOrderQueryParam queryParam) {
        final HlSelectedCorpOrderConsRecQueryParam.QueryParamBuilder builder = new HlSelectedCorpOrderConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new HlSelectedCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build());
        return builder.build();
    }

    @Override
    protected HlSelectedCorpOrderDetailConsRecQueryParam buildDetailConsRecQueryParam(HlSelectedCorpReconciliationOrderQueryParam queryParam) {
        final HlSelectedCorpOrderDetailConsRecQueryParam.QueryParamBuilder builder = new HlSelectedCorpOrderDetailConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new HlSelectedCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build());
        return builder.build();
    }

    @Override
    protected Map<Long, List<Long>> getConsRecIds(List<Long> reconciliationOrderIds, String corpBizType) {
        final Map<Long, List<Long>> consRecIdsMap;
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            consRecIdsMap = Collections.emptyMap();
        } else {
            final Map<String, Object> queryParams = QueryParamBuilder.get()
                    .param("reconciliationOrderIds", reconciliationOrderIds)
                    .build();
            final List<HlSelectedCorpReconciliationOrderConsRec> consRecs =
                    hlSelectedCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
            if (ListUtil.isEmpty(consRecs)) {
                consRecIdsMap = Collections.emptyMap();
            } else {
                consRecIdsMap = consRecs.stream()
                        .collect(Collectors.groupingBy(HlSelectedCorpReconciliationOrderConsRec::getReconciliationOrderId,
                                Collectors.mapping(HlSelectedCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
            }
        }
        return consRecIdsMap;
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(List<Long> reconciliationOrderIds, String corpBizType) {
        final Map<Long, List<Long>> detailConsRecIsMap;
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            detailConsRecIsMap = Collections.emptyMap();
        } else {
            final Map<String, Object> queryParams = QueryParamBuilder.get()
                    .param("reconciliationOrderIds", reconciliationOrderIds)
                    .build();
            final List<HlSelectedCorpReconciliationOrderDetailConsRec> detailConsRecs =
                    hlSelectedCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
            if (ListUtil.isEmpty(detailConsRecs)) {
                detailConsRecIsMap = Collections.emptyMap();
            } else {
                detailConsRecIsMap = detailConsRecs.stream()
                        .collect(Collectors.groupingBy(HlSelectedCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                                Collectors.mapping(HlSelectedCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
            }
        }
        return detailConsRecIsMap;
    }

    @Override
    protected List<HlSelectedCorpOrderConsRecStdVO> queryConsRecs(final HlSelectedCorpOrderConsRecQueryParam queryParam,
                                                                  final CorpOrderConsRecQueryCfg queryCfg) {
        return hlSelectedCorpOrderConsRecViewStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<HlSelectedCorpOrderDetailConsRecStdVO> queryDetailConsRecs(final HlSelectedCorpOrderDetailConsRecQueryParam queryParam,
                                                                              final CorpOrderDetailConsRecQueryCfg queryCfg) {
        return hlSelectedCorpOrderDetailConsRecViewStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(final Map<String, Object> queryParams) {
        return hlSelectedCorpReconciliationOrderViewService.countByParams(queryParams);
    }

    @Override
    protected List<HlSelectedCorpReconciliationOrder> searchReconciliationOrders(final Map<String, Object> queryParams, final List<String> showColumns) {
        return hlSelectedCorpReconciliationOrderViewService.findByParams(queryParams, showColumns);
    }
}
