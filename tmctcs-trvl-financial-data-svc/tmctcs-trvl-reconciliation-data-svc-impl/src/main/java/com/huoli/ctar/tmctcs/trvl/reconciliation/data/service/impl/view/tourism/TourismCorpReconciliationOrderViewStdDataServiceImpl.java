package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.tourism;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.constant.CorpCategory;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.AbstractCorpReconciliationOrderViewStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.internal.TourismCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.internal.TourismCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.internal.TourismCorpResaleReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.internal.TourismCorpResaleReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.view.tourism.TourismCorpReconciliationOrderViewService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.ConsOrderCorpInfoQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.tourism.*;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.tourism.TourismCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.tourism.TourismCorpReconciliationOrderStdVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("tourismCorpReconciliationOrderViewStdDataService")
public class TourismCorpReconciliationOrderViewStdDataServiceImpl
        extends AbstractCorpReconciliationOrderViewStdDataService<TourismCorpReconciliationOrder,
        TourismCorpOrderConsRecStdVO, TourismCorpOrderDetailConsRecStdVO, TourismCorpReconciliationOrderStdVO,
        TourismCorpReconciliationOrderQueryParam, TourismCorpOrderConsRecQueryParam, TourismCorpOrderDetailConsRecQueryParam> {

    @Autowired
    @Qualifier("tourismCorpReconciliationOrderViewService")
    private TourismCorpReconciliationOrderViewService tourismCorpReconciliationOrderViewService;

    @Autowired
    @Qualifier("tourismCorpResaleReconciliationOrderConsRecInternalService")
    private TourismCorpResaleReconciliationOrderConsRecInternalService tourismCorpResaleReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("tourismCorpResaleReconciliationOrderDetailConsRecInternalService")
    private TourismCorpResaleReconciliationOrderDetailConsRecInternalService tourismCorpResaleReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("tourismCorpReconciliationOrderConsRecInternalService")
    private TourismCorpReconciliationOrderConsRecInternalService tourismCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("tourismCorpReconciliationOrderDetailConsRecInternalService")
    private TourismCorpReconciliationOrderDetailConsRecInternalService tourismCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("tourismCorpOrderConsRecViewStdDataService")
    private CorpOrderConsRecStdDataService<TourismCorpOrderConsRecStdVO, TourismCorpOrderConsRecQueryParam> tourismCorpOrderConsRecViewStdDataService;

    @Autowired
    @Qualifier("tourismCorpOrderDetailConsRecViewStdDataService")
    private CorpOrderDetailConsRecStdDataService<TourismCorpOrderDetailConsRecStdVO, TourismCorpOrderDetailConsRecQueryParam> tourismCorpOrderDetailConsRecViewStdDataService;

    @Override
    public TourismCorpReconciliationOrderStdVO transferToStdVO(final TourismCorpReconciliationOrder entity) {
        final TourismCorpReconciliationOrderStdVO stdVO = new TourismCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        stdVO.setPrdUseDate(entity.getPrdUseDate());
        return stdVO;
    }

    @Override
    protected TourismCorpOrderConsRecQueryParam buildConsRecQueryParam(TourismCorpReconciliationOrderQueryParam queryParam) {
        final TourismCorpOrderConsRecQueryParam.QueryParamBuilder builder = new TourismCorpOrderConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new TourismCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build());
        return builder.build();
    }

    @Override
    protected TourismCorpOrderDetailConsRecQueryParam buildDetailConsRecQueryParam(TourismCorpReconciliationOrderQueryParam queryParam) {
        final TourismCorpOrderDetailConsRecQueryParam.QueryParamBuilder builder = new TourismCorpOrderDetailConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new TourismCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build());
        return builder.build();
    }

    @Override
    protected Map<Long, List<Long>> getConsRecIds(final List<Long> reconciliationOrderIds, final String corpBizType) {
        final List<TourismCorpReconciliationOrderConsRec> consRecs;
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            consRecs = tourismCorpResaleReconciliationOrderConsRecInternalService.findByParams(queryParams,
                    Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                            BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        } else {
            consRecs = tourismCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                    Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                            BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));

        }
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(TourismCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(TourismCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(final List<Long> reconciliationOrderIds, final String corpBizType) {
        final List<TourismCorpReconciliationOrderDetailConsRec> detailConsRecs;
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            detailConsRecs =
                    tourismCorpResaleReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        } else {
            detailConsRecs =
                    tourismCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        }
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(TourismCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(TourismCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<TourismCorpOrderConsRecStdVO> queryConsRecs(final TourismCorpOrderConsRecQueryParam queryParam,
                                                               final CorpOrderConsRecQueryCfg queryCfg) {
        return tourismCorpOrderConsRecViewStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<TourismCorpOrderDetailConsRecStdVO> queryDetailConsRecs(final TourismCorpOrderDetailConsRecQueryParam queryParam,
                                                                           final CorpOrderDetailConsRecQueryCfg queryCfg) {
        return tourismCorpOrderDetailConsRecViewStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(final Map<String, Object> queryParams) {
        return tourismCorpReconciliationOrderViewService.countByParams(queryParams);
    }

    @Override
    protected List<TourismCorpReconciliationOrder> searchReconciliationOrders(final Map<String, Object> queryParams, final List<String> showColumns) {
        return tourismCorpReconciliationOrderViewService.findByParams(queryParams, showColumns);
    }
}
