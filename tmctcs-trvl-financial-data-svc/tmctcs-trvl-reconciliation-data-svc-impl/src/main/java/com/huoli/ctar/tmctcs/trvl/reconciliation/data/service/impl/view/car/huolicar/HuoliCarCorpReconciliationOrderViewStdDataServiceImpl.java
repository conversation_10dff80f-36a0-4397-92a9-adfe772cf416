package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.car.huolicar;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.constant.CorpCategory;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.AbstractCorpReconciliationOrderViewStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.huolicar.internal.HuoliCarCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.huolicar.internal.HuoliCarCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.view.car.huolicar.HuoliCarCorpReconciliationOrderViewService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.ConsOrderCorpInfoQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.huolicar.*;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.huolicar.HuoliCarCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.huolicar.HuoliCarCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.huolicar.HuoliCarCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.CarCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.huolicar.HuoliCarCorpReconciliationOrderStdVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("huoliCarCorpReconciliationOrderViewStdDataService")
public class HuoliCarCorpReconciliationOrderViewStdDataServiceImpl
        extends AbstractCorpReconciliationOrderViewStdDataService<HuoliCarCorpReconciliationOrder,
        HuoliCarCorpOrderConsRecStdVO, HuoliCarCorpOrderDetailConsRecStdVO, HuoliCarCorpReconciliationOrderStdVO,
        CarCorpReconciliationOrderQueryParam, HuoliCarCorpOrderConsRecQueryParam, HuoliCarCorpOrderDetailConsRecQueryParam> {

    @Autowired
    @Qualifier("huoliCarCorpReconciliationOrderViewService")
    private HuoliCarCorpReconciliationOrderViewService huoliCarCorpReconciliationOrderViewService;

    @Autowired
    @Qualifier("huoliCarCorpReconciliationOrderConsRecInternalService")
    private HuoliCarCorpReconciliationOrderConsRecInternalService huoliCarCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("huoliCarCorpReconciliationOrderDetailConsRecInternalService")
    private HuoliCarCorpReconciliationOrderDetailConsRecInternalService huoliCarCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("huoliCarCorpOrderConsRecViewStdDataService")
    private CorpOrderConsRecStdDataService<HuoliCarCorpOrderConsRecStdVO, HuoliCarCorpOrderConsRecQueryParam> huoliCarCorpOrderConsRecViewStdDataService;

    @Autowired
    @Qualifier("huoliCarCorpOrderDetailConsRecViewStdDataService")
    private CorpOrderDetailConsRecStdDataService<HuoliCarCorpOrderDetailConsRecStdVO, HuoliCarCorpOrderDetailConsRecQueryParam> huoliCarCorpOrderDetailConsRecViewStdDataService;

    @Override
    public HuoliCarCorpReconciliationOrderStdVO transferToStdVO(final HuoliCarCorpReconciliationOrder entity) {
        final HuoliCarCorpReconciliationOrderStdVO stdVO = new HuoliCarCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        return stdVO;
    }

    @Override
    protected HuoliCarCorpOrderConsRecQueryParam buildConsRecQueryParam(final CarCorpReconciliationOrderQueryParam queryParam) {
        final HuoliCarCorpOrderConsRecQueryParam.QueryParamBuilder builder = new HuoliCarCorpOrderConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new HuoliCarCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build());
        return builder.build();
    }

    @Override
    protected HuoliCarCorpOrderDetailConsRecQueryParam buildDetailConsRecQueryParam(final CarCorpReconciliationOrderQueryParam queryParam) {
        final HuoliCarCorpOrderDetailConsRecQueryParam.QueryParamBuilder builder = new HuoliCarCorpOrderDetailConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new HuoliCarCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build());
        return builder.build();
    }

    @Override
    protected Map<Long, List<Long>> getConsRecIds(List<Long> reconciliationOrderIds, String corpBizType) {
        final Map<Long, List<Long>> consRecIdsMap;
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            consRecIdsMap = Collections.emptyMap();
        } else {
            final Map<String, Object> queryParams = QueryParamBuilder.get()
                    .param("reconciliationOrderIds", reconciliationOrderIds)
                    .build();
            final List<HuoliCarCorpReconciliationOrderConsRec> consRecs =
                    huoliCarCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
            if (ListUtil.isEmpty(consRecs)) {
                consRecIdsMap = Collections.emptyMap();
            } else {
                consRecIdsMap = consRecs.stream()
                        .collect(Collectors.groupingBy(HuoliCarCorpReconciliationOrderConsRec::getReconciliationOrderId,
                                Collectors.mapping(HuoliCarCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
            }
        }
        return consRecIdsMap;
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(List<Long> reconciliationOrderIds, String corpBizType) {
        final Map<Long, List<Long>> detailConsRecIsMap;
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            detailConsRecIsMap = Collections.emptyMap();
        } else {
            final Map<String, Object> queryParams = QueryParamBuilder.get()
                    .param("reconciliationOrderIds", reconciliationOrderIds)
                    .build();
            final List<HuoliCarCorpReconciliationOrderDetailConsRec> detailConsRecs =
                    huoliCarCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
            if (ListUtil.isEmpty(detailConsRecs)) {
                detailConsRecIsMap = Collections.emptyMap();
            } else {
                detailConsRecIsMap = detailConsRecs.stream()
                        .collect(Collectors.groupingBy(HuoliCarCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                                Collectors.mapping(HuoliCarCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
            }
        }
        return detailConsRecIsMap;
    }

    @Override
    protected List<HuoliCarCorpOrderConsRecStdVO> queryConsRecs(final HuoliCarCorpOrderConsRecQueryParam queryParam,
                                                                final CorpOrderConsRecQueryCfg queryCfg) {
        return huoliCarCorpOrderConsRecViewStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<HuoliCarCorpOrderDetailConsRecStdVO> queryDetailConsRecs(final HuoliCarCorpOrderDetailConsRecQueryParam queryParam,
                                                                            final CorpOrderDetailConsRecQueryCfg queryCfg) {
        return huoliCarCorpOrderDetailConsRecViewStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(final Map<String, Object> queryParams) {
        return huoliCarCorpReconciliationOrderViewService.countByParams(queryParams);
    }

    @Override
    protected List<HuoliCarCorpReconciliationOrder> searchReconciliationOrders(final Map<String, Object> queryParams, final List<String> showColumns) {
        return huoliCarCorpReconciliationOrderViewService.findByParams(queryParams, showColumns);
    }
}
