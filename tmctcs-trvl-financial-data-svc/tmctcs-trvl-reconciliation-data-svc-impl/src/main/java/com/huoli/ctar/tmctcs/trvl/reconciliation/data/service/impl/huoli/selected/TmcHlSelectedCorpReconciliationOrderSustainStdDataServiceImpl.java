package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.huoli.selected;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.huoli.selected.db.HlSelectedCorpReconciliationOrderConsRecDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.huoli.selected.db.HlSelectedCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.huoli.selected.db.HlSelectedCorpReconciliationOrderDetailConsRecDbService;
import com.huoli.ctar.tmctfs.beans.srd.entity.huoli.selected.HlSelectedCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.huoli.selected.HlSelectedCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.huoli.selected.HlSelectedCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.huoli.selected.HlSelectedCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.huoli.selected.HlSelectedCorpReconciliationOrderStdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("tmcHlSelectedCorpReconciliationOrderSustainStdDataService")
public class TmcHlSelectedCorpReconciliationOrderSustainStdDataServiceImpl
        extends HlSelectedCorpReconciliationOrderSustainStdDataService {

    @Autowired
    @Qualifier("hlSelectedCorpReconciliationOrderDbService")
    private HlSelectedCorpReconciliationOrderDbService hlSelectedCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("hlSelectedCorpReconciliationOrderConsRecDbService")
    private HlSelectedCorpReconciliationOrderConsRecDbService hlSelectedCorpReconciliationOrderConsRecDbService;

    @Autowired
    @Qualifier("hlSelectedCorpReconciliationOrderDetailConsRecDbService")
    private HlSelectedCorpReconciliationOrderDetailConsRecDbService hlSelectedCorpReconciliationOrderDetailConsRecDbService;

    @Autowired
    @Qualifier("tmcHlSelectedCorpReconciliationOrderStdDataService")
    private CorpReconciliationOrderStdDataService<HlSelectedCorpReconciliationOrderStdVO, HlSelectedCorpReconciliationOrderQueryParam> tmcHlSelectedCorpReconciliationOrderStdDataService;

    @Override
    protected void persistReconciliationOrder(HlSelectedCorpReconciliationOrder newEntity) {
        hlSelectedCorpReconciliationOrderDbService.dbInsert(newEntity);
    }

    @Override
    protected int doUpdateReconciliationOrder(HlSelectedCorpReconciliationOrder example) {
        return hlSelectedCorpReconciliationOrderDbService.dbUpdate(example);
    }

    @Override
    protected int deleteCorpReconciliationOrders(Map<String, Object> deleteParams) {
        return hlSelectedCorpReconciliationOrderDbService.deleteByParams(deleteParams);
    }

    @Override
    protected List<HlSelectedCorpReconciliationOrderStdVO> getExistingReconciliationOrders(final String orderId) {
        return tmcHlSelectedCorpReconciliationOrderStdDataService.getReconciliationOrders(orderId, buildQueryCfg());
    }

    @Override
    protected HlSelectedCorpReconciliationOrderStdVO getExistingReconciliationOrder(Long reconciliationOrderId) {
        return tmcHlSelectedCorpReconciliationOrderStdDataService.getReconciliationOrder(reconciliationOrderId, buildQueryCfg());
    }

    @Override
    protected void persistCorpReconciliationOrderConsRecs(List<HlSelectedCorpReconciliationOrderConsRec> reconciliationOrderConsRecs) {
        hlSelectedCorpReconciliationOrderConsRecDbService.batchInsert(reconciliationOrderConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderConsRecs(Map<String, Object> deleteParams) {
        return hlSelectedCorpReconciliationOrderConsRecDbService.deleteByParams(deleteParams);
    }

    @Override
    protected void persistCorpReconciliationOrderDetailConsRecs(List<HlSelectedCorpReconciliationOrderDetailConsRec> reconciliationOrderDetailConsRecs) {
        hlSelectedCorpReconciliationOrderDetailConsRecDbService.batchInsert(reconciliationOrderDetailConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderDetailConsRecs(Map<String, Object> deleteParams) {
        return hlSelectedCorpReconciliationOrderDetailConsRecDbService.deleteByParams(deleteParams);
    }
}
