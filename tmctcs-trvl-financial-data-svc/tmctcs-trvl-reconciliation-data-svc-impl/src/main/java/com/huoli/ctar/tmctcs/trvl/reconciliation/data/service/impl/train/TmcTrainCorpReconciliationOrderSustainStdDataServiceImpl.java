package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.train;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.db.TrainCorpReconciliationOrderConsRecDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.db.TrainCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.db.TrainCorpReconciliationOrderDetailConsRecDbService;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.train.TrainCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.train.TrainCorpReconciliationOrderStdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("tmcTrainCorpReconciliationOrderSustainStdDataService")
public class TmcTrainCorpReconciliationOrderSustainStdDataServiceImpl
        extends TrainCorpReconciliationOrderSustainStdDataService {

    @Autowired
    @Qualifier("trainCorpReconciliationOrderDbService")
    private TrainCorpReconciliationOrderDbService trainCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("trainCorpReconciliationOrderConsRecDbService")
    private TrainCorpReconciliationOrderConsRecDbService trainCorpReconciliationOrderConsRecDbService;

    @Autowired
    @Qualifier("trainCorpReconciliationOrderDetailConsRecDbService")
    private TrainCorpReconciliationOrderDetailConsRecDbService trainCorpReconciliationOrderDetailConsRecDbService;

    @Autowired
    @Qualifier("tmcTrainCorpReconciliationOrderStdDataService")
    private CorpReconciliationOrderStdDataService<TrainCorpReconciliationOrderStdVO, TrainCorpReconciliationOrderQueryParam> tmcTrainCorpReconciliationOrderStdDataService;

    @Override
    protected void persistReconciliationOrder(TrainCorpReconciliationOrder newEntity) {
        trainCorpReconciliationOrderDbService.dbInsert(newEntity);
    }

    @Override
    protected int doUpdateReconciliationOrder(TrainCorpReconciliationOrder example) {
        return trainCorpReconciliationOrderDbService.dbUpdate(example);
    }

    @Override
    protected int deleteCorpReconciliationOrders(Map<String, Object> deleteParams) {
        return trainCorpReconciliationOrderDbService.deleteByParams(deleteParams);
    }

    @Override
    protected List<TrainCorpReconciliationOrderStdVO> getExistingReconciliationOrders(final String orderId) {
        return tmcTrainCorpReconciliationOrderStdDataService.getReconciliationOrders(orderId, buildQueryCfg());
    }

    @Override
    protected TrainCorpReconciliationOrderStdVO getExistingReconciliationOrder(Long reconciliationOrderId) {
        return tmcTrainCorpReconciliationOrderStdDataService.getReconciliationOrder(reconciliationOrderId, buildQueryCfg());
    }

    @Override
    protected void persistCorpReconciliationOrderConsRecs(List<TrainCorpReconciliationOrderConsRec> reconciliationOrderConsRecs) {
        trainCorpReconciliationOrderConsRecDbService.batchInsert(reconciliationOrderConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderConsRecs(Map<String, Object> deleteParams) {
        return trainCorpReconciliationOrderConsRecDbService.deleteByParams(deleteParams);
    }

    @Override
    protected void persistCorpReconciliationOrderDetailConsRecs(List<TrainCorpReconciliationOrderDetailConsRec> reconciliationOrderDetailConsRecs) {
        trainCorpReconciliationOrderDetailConsRecDbService.batchInsert(reconciliationOrderDetailConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderDetailConsRecs(Map<String, Object> deleteParams) {
        return trainCorpReconciliationOrderDetailConsRecDbService.deleteByParams(deleteParams);
    }
}
