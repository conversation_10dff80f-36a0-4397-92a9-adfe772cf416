package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.tourism;

import com.huoli.ctar.core.infra.constant.BaseShowColumn;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderSustainStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.entity.CorpOrderConsRecShowColumn;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.CorpReconciliationOrderQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.tourism.TourismCorpReconciliationOrderStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.tourism.TourismCorpReconciliationOrderSustainStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.tourism.TourismCorpReconciliationOrderUpdateSummary;

import java.util.Arrays;

public abstract class TourismCorpReconciliationOrderSustainStdDataService
        extends AbstractCorpReconciliationOrderSustainStdDataService<
        TourismCorpReconciliationOrder, TourismCorpReconciliationOrderSustainStdVO, TourismCorpReconciliationOrderUpdateSummary,
        TourismCorpReconciliationOrderConsRec, TourismCorpReconciliationOrderDetailConsRec,
        TourismCorpReconciliationOrderStdVO> {

    protected CorpReconciliationOrderQueryCfg buildQueryCfg() {
        return new CorpReconciliationOrderQueryCfg.QueryCfgBuilder()
                .needConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .consRecQueryCfg(new CorpOrderConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(), CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .needDetailConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .detailConsRecQueryCfg(new CorpOrderDetailConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(), CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .addShowColumns(TourismCorpReconciliationOrder.allColumns())
                .build();
    }

    @Override
    protected TourismCorpReconciliationOrderConsRec buildCorpReconciliationOrderConsRec(Long consRecId, Long reconciliationOrderId) {
        return new TourismCorpReconciliationOrderConsRec(reconciliationOrderId, consRecId);
    }

    @Override
    protected TourismCorpReconciliationOrderDetailConsRec buildCorpReconciliationOrderDetailConsRec(Long detailConsRecId, Long reconciliationOrderId) {
        return new TourismCorpReconciliationOrderDetailConsRec(reconciliationOrderId, detailConsRecId);
    }

    @Override
    protected TourismCorpReconciliationOrder toNewEntity(TourismCorpReconciliationOrderSustainStdVO reconciliationOrder) {
        final TourismCorpReconciliationOrder newEntity = new TourismCorpReconciliationOrder();
        assembleCommonAttributes(newEntity, reconciliationOrder);
        assembleCommonAttributesOnCreation(newEntity, reconciliationOrder);
        return newEntity;
    }

    @Override
    protected TourismCorpReconciliationOrder transferToEntity(TourismCorpReconciliationOrderUpdateSummary updateSummary) {
        final TourismCorpReconciliationOrder example = new TourismCorpReconciliationOrder();
        assembleCommonAttributes(example, updateSummary);
        return example;
    }

    @Override
    protected TourismCorpReconciliationOrderUpdateSummary getUpdateSummary(TourismCorpReconciliationOrderSustainStdVO reconciliationOrder,
                                                                           TourismCorpReconciliationOrderStdVO existingReconciliationOrder) {
        return reconciliationOrder.getUpdateSummary(existingReconciliationOrder);
    }
}
