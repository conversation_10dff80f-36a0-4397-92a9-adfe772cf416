package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.huoli.selected;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.huoli.selected.HlSelectedCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.huoli.selected.HlSelectedCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.huoli.selected.HlSelectedCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.huoli.selected.HlSelectedCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.huoli.selected.HlSelectedCorpReconciliationOrderStdVO;

public abstract class HlSelectedCorpReconciliationOrderStdDataService
        extends AbstractCorpReconciliationOrderStdDataService<
        HlSelectedCorpReconciliationOrder, HlSelectedCorpOrderConsRecStdVO, HlSelectedCorpOrderDetailConsRecStdVO,
        HlSelectedCorpReconciliationOrderStdVO, HlSelectedCorpReconciliationOrderQueryParam> {

    @Override
    protected HlSelectedCorpReconciliationOrderQueryParam buildQueryParam(final String orderId) {
        return new HlSelectedCorpReconciliationOrderQueryParam.Builder()
                .orderId(orderId)
                .build();
    }

    @Override
    protected HlSelectedCorpReconciliationOrderQueryParam buildQueryParam(Long reconciliationOrderId) {
        return new HlSelectedCorpReconciliationOrderQueryParam.Builder()
                .reconciliationOrderId(reconciliationOrderId)
                .build();
    }

    @Override
    public HlSelectedCorpReconciliationOrderStdVO transferToStdVO(HlSelectedCorpReconciliationOrder entity) {
        final HlSelectedCorpReconciliationOrderStdVO stdVO = new HlSelectedCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        return stdVO;
    }
}
