package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.train;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.db.TrainCorpResaleReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.internal.TrainCorpResaleReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.internal.TrainCorpResaleReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.internal.TrainCorpResaleReconciliationOrderInternalService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.train.TrainCorpOrderConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.train.TrainCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.train.TrainCorpOrderDetailConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.train.TrainCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrderDetailConsRec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("trainCorpResaleReconciliationOrderStdDataService")
public class TrainCorpResaleReconciliationOrderStdDataServiceImpl
        extends TrainCorpReconciliationOrderStdDataService {

    @Autowired
    @Qualifier("trainCorpResaleReconciliationOrderInternalService")
    private TrainCorpResaleReconciliationOrderInternalService trainCorpResaleReconciliationOrderInternalService;

    @Autowired
    @Qualifier("trainCorpResaleReconciliationOrderDbService")
    private TrainCorpResaleReconciliationOrderDbService trainCorpResaleReconciliationOrderDbService;

    @Autowired
    @Qualifier("trainCorpResaleReconciliationOrderConsRecInternalService")
    private TrainCorpResaleReconciliationOrderConsRecInternalService trainCorpResaleReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("trainCorpResaleReconciliationOrderDetailConsRecInternalService")
    private TrainCorpResaleReconciliationOrderDetailConsRecInternalService trainCorpResaleReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("tmcTrainCorpResaleOrderConsRecStdDataService")
    private CorpOrderConsRecStdDataService<TrainCorpOrderConsRecStdVO, TrainCorpOrderConsRecQueryParam> corpResaleOrderConsRecStdDataService;

    @Autowired
    @Qualifier("tmcTrainCorpResaleOrderDetailConsRecStdDataService")
    private CorpOrderDetailConsRecStdDataService<TrainCorpOrderDetailConsRecStdVO, TrainCorpOrderDetailConsRecQueryParam> corpResaleOrderDetailConsRecStdDataService;

    @Override
    protected Map<Long, List<Long>> getConsRecIds(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<TrainCorpReconciliationOrderConsRec> consRecs =
                trainCorpResaleReconciliationOrderConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(TrainCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(TrainCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<TrainCorpReconciliationOrderDetailConsRec> detailConsRecs =
                trainCorpResaleReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(TrainCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(TrainCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<TrainCorpOrderConsRecStdVO> getConsRecs(List<Long> consRecIds, CorpOrderConsRecQueryCfg queryCfg) {
        final TrainCorpOrderConsRecQueryParam queryParam = new TrainCorpOrderConsRecQueryParam.QueryParamBuilder()
                .consRecIds(consRecIds)
                .build();
        return corpResaleOrderConsRecStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<TrainCorpOrderDetailConsRecStdVO> getDetailConsRecs(List<Long> detailConsRecIds, CorpOrderDetailConsRecQueryCfg queryCfg) {
        final TrainCorpOrderDetailConsRecQueryParam queryParam = new TrainCorpOrderDetailConsRecQueryParam.QueryParamBuilder()
                .detailConsRecIds(detailConsRecIds)
                .build();
        return corpResaleOrderDetailConsRecStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(Map<String, Object> queryParams) {
        return trainCorpResaleReconciliationOrderDbService.countByParams(queryParams);
    }

    @Override
    protected List<TrainCorpReconciliationOrder> searchReconciliationOrders(Map<String, Object> queryParams, List<String> showColumns) {
        return trainCorpResaleReconciliationOrderInternalService.findByParams(queryParams, showColumns);
    }
}
