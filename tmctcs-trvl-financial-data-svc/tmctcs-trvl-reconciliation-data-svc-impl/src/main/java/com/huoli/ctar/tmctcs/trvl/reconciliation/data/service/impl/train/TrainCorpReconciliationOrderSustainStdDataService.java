package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.train;

import com.huoli.ctar.core.infra.constant.BaseShowColumn;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderSustainStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.entity.CorpOrderConsRecShowColumn;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.CorpReconciliationOrderQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.train.TrainCorpReconciliationOrderStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.train.TrainCorpReconciliationOrderSustainStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.train.TrainCorpReconciliationOrderUpdateSummary;

import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

public abstract class TrainCorpReconciliationOrderSustainStdDataService
        extends AbstractCorpReconciliationOrderSustainStdDataService<
        TrainCorpReconciliationOrder, TrainCorpReconciliationOrderSustainStdVO, TrainCorpReconciliationOrderUpdateSummary,
        TrainCorpReconciliationOrderConsRec, TrainCorpReconciliationOrderDetailConsRec,
        TrainCorpReconciliationOrderStdVO> {

    protected CorpReconciliationOrderQueryCfg buildQueryCfg() {
        return new CorpReconciliationOrderQueryCfg.QueryCfgBuilder()
                .needConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .consRecQueryCfg(new CorpOrderConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(), CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .needDetailConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .detailConsRecQueryCfg(new CorpOrderDetailConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(), CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .addShowColumns(TrainCorpReconciliationOrder.allColumns())
                .build();
    }

    @Override
    protected TrainCorpReconciliationOrderConsRec buildCorpReconciliationOrderConsRec(Long consRecId, Long reconciliationOrderId) {
        return new TrainCorpReconciliationOrderConsRec(reconciliationOrderId, consRecId);
    }

    @Override
    protected TrainCorpReconciliationOrderDetailConsRec buildCorpReconciliationOrderDetailConsRec(Long detailConsRecId, Long reconciliationOrderId) {
        return new TrainCorpReconciliationOrderDetailConsRec(reconciliationOrderId, detailConsRecId);
    }

    @Override
    protected TrainCorpReconciliationOrder toNewEntity(TrainCorpReconciliationOrderSustainStdVO reconciliationOrder) {
        final TrainCorpReconciliationOrder newEntity = new TrainCorpReconciliationOrder();
        newEntity.setSubOrderId(reconciliationOrder.getSubOrderId());
        newEntity.setTicketStatus(reconciliationOrder.getTicketStatus());
        assembleCommonAttributes(newEntity, reconciliationOrder);
        assembleOtherAttributes(newEntity, reconciliationOrder);
        assembleCommonAttributesOnCreation(newEntity, reconciliationOrder);
        return newEntity;
    }

    @Override
    protected TrainCorpReconciliationOrder transferToEntity(TrainCorpReconciliationOrderUpdateSummary updateSummary) {
        final TrainCorpReconciliationOrder example = new TrainCorpReconciliationOrder();
        example.setSubOrderId(updateSummary.getUpdateInfo().getSubOrderId());
        example.setTicketStatus(updateSummary.getUpdateInfo().getTicketStatus());
        assembleOtherAttributes(example, updateSummary);
        assembleCommonAttributes(example, updateSummary);
        return example;
    }

    @Override
    protected TrainCorpReconciliationOrderUpdateSummary getUpdateSummary(TrainCorpReconciliationOrderSustainStdVO reconciliationOrder,
                                                                         TrainCorpReconciliationOrderStdVO existingReconciliationOrder) {
        return reconciliationOrder.getUpdateSummary(existingReconciliationOrder);
    }

    private void assembleOtherAttributes(TrainCorpReconciliationOrder newEntity,
                                         TrainCorpReconciliationOrderSustainStdVO reconciliationOrder) {
        newEntity.setSpOrderId(reconciliationOrder.getSpOrderId());
        if (Objects.nonNull(reconciliationOrder.getDepTime())) {
            newEntity.setDepTime(new Date(reconciliationOrder.getDepTime()));
        }
        if (Objects.nonNull(reconciliationOrder.getArrTime())) {
            newEntity.setArrTime(new Date(reconciliationOrder.getArrTime()));
        }
    }

    private void assembleOtherAttributes(TrainCorpReconciliationOrder newEntity,
                                         TrainCorpReconciliationOrderUpdateSummary updateSummary) {
        newEntity.setSpOrderId(updateSummary.getUpdateInfo().getSpOrderId());
        if (Objects.nonNull(updateSummary.getUpdateInfo().getDepTime())) {
            newEntity.setDepTime(new Date(updateSummary.getUpdateInfo().getDepTime()));
        }
        if (Objects.nonNull(updateSummary.getUpdateInfo().getArrTime())) {
            newEntity.setArrTime(new Date(updateSummary.getUpdateInfo().getArrTime()));
        }
    }
}
