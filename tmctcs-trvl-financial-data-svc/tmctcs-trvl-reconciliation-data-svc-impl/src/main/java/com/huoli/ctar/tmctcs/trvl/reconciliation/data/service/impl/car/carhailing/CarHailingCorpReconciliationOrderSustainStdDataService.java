package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.car.carhailing;

import com.huoli.ctar.core.infra.constant.BaseShowColumn;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderSustainStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.entity.CorpOrderConsRecShowColumn;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.carhailing.CarHailingCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.carhailing.CarHailingCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.carhailing.CarHailingCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.CorpReconciliationOrderQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.CarCorpReconciliationOrderUpdateSummary;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.carhailing.CarHailingCorpReconciliationOrderStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.carhailing.CarHailingCorpReconciliationOrderSustainStdVO;

import java.util.Arrays;

public abstract class CarHailingCorpReconciliationOrderSustainStdDataService
        extends AbstractCorpReconciliationOrderSustainStdDataService<
        CarHailingCorpReconciliationOrder, CarHailingCorpReconciliationOrderSustainStdVO, CarCorpReconciliationOrderUpdateSummary,
        CarHailingCorpReconciliationOrderConsRec, CarHailingCorpReconciliationOrderDetailConsRec,
        CarHailingCorpReconciliationOrderStdVO> {

    protected CorpReconciliationOrderQueryCfg buildQueryCfg() {
        return new CorpReconciliationOrderQueryCfg.QueryCfgBuilder()
                .needConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .consRecQueryCfg(new CorpOrderConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(),
                                CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .needDetailConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .detailConsRecQueryCfg(new CorpOrderDetailConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(),
                                CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .addShowColumns(CarHailingCorpReconciliationOrder.allColumns())
                .build();
    }

    @Override
    protected CarHailingCorpReconciliationOrderConsRec buildCorpReconciliationOrderConsRec(Long consRecId, Long reconciliationOrderId) {
        return new CarHailingCorpReconciliationOrderConsRec(reconciliationOrderId, consRecId);
    }

    @Override
    protected CarHailingCorpReconciliationOrderDetailConsRec buildCorpReconciliationOrderDetailConsRec(Long detailConsRecId, Long reconciliationOrderId) {
        return new CarHailingCorpReconciliationOrderDetailConsRec(reconciliationOrderId, detailConsRecId);
    }

    @Override
    protected CarHailingCorpReconciliationOrder toNewEntity(final CarHailingCorpReconciliationOrderSustainStdVO reconciliationOrder) {
        final CarHailingCorpReconciliationOrder newEntity = new CarHailingCorpReconciliationOrder();
        assembleCommonAttributes(newEntity, reconciliationOrder);
        assembleCommonAttributesOnCreation(newEntity, reconciliationOrder);
        return newEntity;
    }

    @Override
    protected CarHailingCorpReconciliationOrder transferToEntity(CarCorpReconciliationOrderUpdateSummary updateSummary) {
        final CarHailingCorpReconciliationOrder example = new CarHailingCorpReconciliationOrder();
        assembleCommonAttributes(example, updateSummary);
        return example;
    }

    @Override
    protected CarCorpReconciliationOrderUpdateSummary getUpdateSummary(CarHailingCorpReconciliationOrderSustainStdVO reconciliationOrder, CarHailingCorpReconciliationOrderStdVO existingReconciliationOrder) {
        return reconciliationOrder.getUpdateSummary(existingReconciliationOrder);
    }
}
