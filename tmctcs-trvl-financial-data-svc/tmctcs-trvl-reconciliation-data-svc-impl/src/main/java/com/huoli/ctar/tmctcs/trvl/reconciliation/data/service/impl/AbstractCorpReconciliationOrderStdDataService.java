package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.core.infra.constant.BaseShowColumn;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.*;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.BaseCorpReconciliationOrderStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.CorpReconciliationOrderQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.CorpReconciliationOrderQueryParam;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public abstract class AbstractCorpReconciliationOrderStdDataService<
        E extends BaseCorpReconciliationOrder,
        CR extends ComparableCorpOrderConsRecStdVO,
        DCR extends ComparableCorpOrderDetailConsRecStdVO<? extends OrderConsRecBizInfoStdVO, ? extends CorpOrderDetailConsRecUniqueKey>,
        R extends BaseCorpReconciliationOrderStdVO<CR, DCR>,
        Q extends CorpReconciliationOrderQueryParam>
        extends BaseCorpReconciliationOrderStdDataService<E, CR, DCR, R>
        implements CorpReconciliationOrderStdDataService<R, Q> {

    @Override
    public int countReconciliationOrders(final Q queryParam) {
        if (Objects.isNull(queryParam)) {
            return GlobalConstant.ZERO;
        }
        return countReconciliationOrders(queryParam.toQueryParams());
    }

    @Override
    public List<R> getReconciliationOrders(final String orderId, final CorpReconciliationOrderQueryCfg queryCfg) {
        final Q queryParam = buildQueryParam(orderId);
        return queryReconciliationOrders(queryParam, queryCfg);
    }

    @Override
    public R getReconciliationOrder(final Long reconciliationOrderId, final CorpReconciliationOrderQueryCfg queryCfg) {
        final Q queryParam = buildQueryParam(reconciliationOrderId);
        final List<R> reconciliationOrders = queryReconciliationOrders(queryParam, queryCfg);
        if (ListUtil.isEmpty(reconciliationOrders)) {
            return null;
        }
        return reconciliationOrders.get(0);
    }

    protected abstract Q buildQueryParam(final Long reconciliationOrderId);

    protected abstract Q buildQueryParam(final String orderId);

    @Override
    public List<R> queryReconciliationOrders(final Q queryParam, final CorpReconciliationOrderQueryCfg queryCfg) {
        if (Objects.isNull(queryParam)) {
            return Collections.emptyList();
        }
        final Map<String, Object> queryParams = queryParam.toQueryParams();
        final List<String> showColumns = Objects.isNull(queryCfg) ? null : queryCfg.getShowColumns();
        addShowColumnIfNecessary(showColumns, BaseShowColumn.ID.getName());
        final List<E> reconciliationOrders = searchReconciliationOrders(queryParams, showColumns);
        if (ListUtil.isEmpty(reconciliationOrders)) {
            return Collections.emptyList();
        }
        final List<R> stdVOList = new ArrayList<>(reconciliationOrders.size());
        final List<Long> reconciliationOrderIds = new ArrayList<>(reconciliationOrders.size());
        for (final E reconciliationOrder : reconciliationOrders) {
            stdVOList.add(transferToStdVO(reconciliationOrder));
            reconciliationOrderIds.add(reconciliationOrder.getSid());
        }

        if (Objects.nonNull(queryCfg) && StringUtils.equals(GlobalConstant.FLAG_YES_VALUE, queryCfg.getNeedConsRecsFlag())) {
            if (Objects.nonNull(queryCfg.getConsRecQueryCfg())) {
                addShowColumnIfNecessary(queryCfg.getConsRecQueryCfg().getShowColumns(), BaseShowColumn.ID.getName());
            }

            final Map<Long, List<Long>> groupedConsRecIds = getConsRecIds(reconciliationOrderIds);
            final Set<Long> consRecIds = new HashSet<>();
            groupedConsRecIds.values().forEach(consRecIds::addAll);
            final List<CR> consRecs = getConsRecs(new ArrayList<>(consRecIds), queryCfg.getConsRecQueryCfg());
            final Map<Long, CR> consRecMap = ListUtil.isEmpty(consRecs)
                    ? Collections.emptyMap() : consRecs.stream().collect(Collectors.toMap(CR::getId, item -> item));
            if (ListUtil.isNotEmpty(consRecs)) {
                final Map<Long, List<CR>> groupedConsRecsMap = new HashMap<>(reconciliationOrderIds.size());
                for (final Long reconciliationOrderId : groupedConsRecIds.keySet()) {
                    final List<Long> consRecIdsGrouped = groupedConsRecIds.get(reconciliationOrderId);
                    final List<CR> consRecsGrouped = new ArrayList<>(consRecIdsGrouped.size());
                    for (final Long consRecId : consRecIdsGrouped) {
                        if (consRecMap.containsKey(consRecId)) {
                            consRecsGrouped.add(consRecMap.get(consRecId));
                        }
                    }
                    groupedConsRecsMap.put(reconciliationOrderId, consRecsGrouped);
                }
                stdVOList.forEach(stdVO -> {
                    if (groupedConsRecsMap.containsKey(stdVO.getId())) {
                        stdVO.setConsRecs(groupedConsRecsMap.get(stdVO.getId()));
                    }
                });
            }
        }
        if (Objects.nonNull(queryCfg) && StringUtils.equals(GlobalConstant.FLAG_YES_VALUE, queryCfg.getNeedDetailConsRecsFlag())) {
            if (Objects.nonNull(queryCfg.getDetailConsRecQueryCfg())) {
                addShowColumnIfNecessary(queryCfg.getDetailConsRecQueryCfg().getShowColumns(), BaseShowColumn.ID.getName());
            }

            final Map<Long, List<Long>> groupedDetailConsRecIds = getDetailConsRecIs(reconciliationOrderIds);
            final Set<Long> detailConsRecIds = new HashSet<>();
            groupedDetailConsRecIds.values().forEach(detailConsRecIds::addAll);
            final List<DCR> detailConsRecs = getDetailConsRecs(new ArrayList<>(detailConsRecIds), queryCfg.getDetailConsRecQueryCfg());
            final Map<Long, DCR> detailConsRecMap = ListUtil.isEmpty(detailConsRecs)
                    ? Collections.emptyMap() : detailConsRecs.stream().collect(Collectors.toMap(DCR::getId, item -> item));
            if (ListUtil.isNotEmpty(detailConsRecs)) {
                final Map<Long, List<DCR>> groupedDetailConsRecsMap = new HashMap<>(reconciliationOrderIds.size());
                for (final Long reconciliationOrderId : groupedDetailConsRecIds.keySet()) {
                    final List<Long> detailConsRecIdsGrouped = groupedDetailConsRecIds.get(reconciliationOrderId);
                    final List<DCR> detailConsRecsGrouped = new ArrayList<>(detailConsRecIdsGrouped.size());
                    for (final Long detailConsRecId : detailConsRecIdsGrouped) {
                        if (detailConsRecMap.containsKey(detailConsRecId)) {
                            detailConsRecsGrouped.add(detailConsRecMap.get(detailConsRecId));
                        }
                    }
                    groupedDetailConsRecsMap.put(reconciliationOrderId, detailConsRecsGrouped);
                }
                stdVOList.forEach(stdVO -> {
                    if (groupedDetailConsRecsMap.containsKey(stdVO.getId())) {
                        stdVO.setDetailConsRecs(groupedDetailConsRecsMap.get(stdVO.getId()));
                    }
                });
            }
        }
        return stdVOList;
    }

    protected abstract Map<Long, List<Long>> getConsRecIds(final List<Long> reconciliationOrderIds);
    protected abstract Map<Long, List<Long>> getDetailConsRecIs(final List<Long> reconciliationOrderIds);

    protected abstract List<CR> getConsRecs(final List<Long> consRecIds, final CorpOrderConsRecQueryCfg queryCfg);
    protected abstract List<DCR> getDetailConsRecs(final List<Long> detailConsRecIds, final CorpOrderDetailConsRecQueryCfg queryCfg);

    protected abstract int countReconciliationOrders(final Map<String, Object> queryParams);

    protected abstract List<E> searchReconciliationOrders(final Map<String, Object> queryParams,
                                                          final List<String> showColumns);
}
