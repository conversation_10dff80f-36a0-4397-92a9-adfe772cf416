package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.car.carhailing;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.carhailing.CarHailingCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.carhailing.CarHailingCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.carhailing.CarHailingCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.CarCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.carhailing.CarHailingCorpReconciliationOrderStdVO;

public abstract class CarHailingCorpReconciliationOrderStdDataService
        extends AbstractCorpReconciliationOrderStdDataService<
        CarHailingCorpReconciliationOrder, CarHailingCorpOrderConsRecStdVO, CarHailingCorpOrderDetailConsRecStdVO,
        CarHailingCorpReconciliationOrderStdVO, CarCorpReconciliationOrderQueryParam> {

    @Override
    protected CarCorpReconciliationOrderQueryParam buildQueryParam(final String orderId) {
        return new CarCorpReconciliationOrderQueryParam.Builder()
                .orderId(orderId)
                .build();
    }

    @Override
    protected CarCorpReconciliationOrderQueryParam buildQueryParam(final Long reconciliationOrderId) {
        return new CarCorpReconciliationOrderQueryParam.Builder()
                .reconciliationOrderId(reconciliationOrderId)
                .build();
    }

    @Override
    public CarHailingCorpReconciliationOrderStdVO transferToStdVO(CarHailingCorpReconciliationOrder entity) {
        final CarHailingCorpReconciliationOrderStdVO stdVO = new CarHailingCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        return stdVO;
    }
}
