package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.car.huolicar;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.huolicar.db.HuoliCarCorpReconciliationOrderConsRecDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.huolicar.db.HuoliCarCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.huolicar.db.HuoliCarCorpReconciliationOrderDetailConsRecDbService;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.huolicar.HuoliCarCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.huolicar.HuoliCarCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.huolicar.HuoliCarCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.CarCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.huolicar.HuoliCarCorpReconciliationOrderStdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("tmcHuoliCarCorpReconciliationOrderSustainStdDataService")
public class TmHuoliCarCorpReconciliationOrderSustainStdDataServiceImpl
        extends HuoliCarCorpReconciliationOrderSustainStdDataService {

    @Autowired
    @Qualifier("huoliCarCorpReconciliationOrderDbService")
    private HuoliCarCorpReconciliationOrderDbService huoliCarCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("huoliCarCorpReconciliationOrderConsRecDbService")
    private HuoliCarCorpReconciliationOrderConsRecDbService huoliCarCorpReconciliationOrderConsRecDbService;

    @Autowired
    @Qualifier("huoliCarCorpReconciliationOrderDetailConsRecDbService")
    private HuoliCarCorpReconciliationOrderDetailConsRecDbService huoliCarCorpReconciliationOrderDetailConsRecDbService;

    @Autowired
    @Qualifier("tmcHuoliCarCorpReconciliationOrderStdDataService")
    private CorpReconciliationOrderStdDataService<HuoliCarCorpReconciliationOrderStdVO, CarCorpReconciliationOrderQueryParam> tmcHuoliCarCorpReconciliationOrderStdDataService;

    @Override
    protected void persistReconciliationOrder(HuoliCarCorpReconciliationOrder newEntity) {
        huoliCarCorpReconciliationOrderDbService.dbInsert(newEntity);
    }

    @Override
    protected int doUpdateReconciliationOrder(HuoliCarCorpReconciliationOrder example) {
        return huoliCarCorpReconciliationOrderDbService.dbUpdate(example);
    }

    @Override
    protected int deleteCorpReconciliationOrders(Map<String, Object> deleteParams) {
        return huoliCarCorpReconciliationOrderDbService.deleteByParams(deleteParams);
    }

    @Override
    protected List<HuoliCarCorpReconciliationOrderStdVO> getExistingReconciliationOrders(final String orderId) {
        return tmcHuoliCarCorpReconciliationOrderStdDataService.getReconciliationOrders(orderId, buildQueryCfg());
    }

    @Override
    protected HuoliCarCorpReconciliationOrderStdVO getExistingReconciliationOrder(Long reconciliationOrderId) {
        return tmcHuoliCarCorpReconciliationOrderStdDataService.getReconciliationOrder(reconciliationOrderId, buildQueryCfg());
    }

    @Override
    protected void persistCorpReconciliationOrderConsRecs(List<HuoliCarCorpReconciliationOrderConsRec> reconciliationOrderConsRecs) {
        huoliCarCorpReconciliationOrderConsRecDbService.batchInsert(reconciliationOrderConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderConsRecs(Map<String, Object> deleteParams) {
        return huoliCarCorpReconciliationOrderConsRecDbService.deleteByParams(deleteParams);
    }

    @Override
    protected void persistCorpReconciliationOrderDetailConsRecs(List<HuoliCarCorpReconciliationOrderDetailConsRec> reconciliationOrderDetailConsRecs) {
        huoliCarCorpReconciliationOrderDetailConsRecDbService.batchInsert(reconciliationOrderDetailConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderDetailConsRecs(Map<String, Object> deleteParams) {
        return huoliCarCorpReconciliationOrderDetailConsRecDbService.deleteByParams(deleteParams);
    }
}
