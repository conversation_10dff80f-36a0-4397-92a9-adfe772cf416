package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.car.huolicar;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.huolicar.db.HuoliCarCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.huolicar.internal.HuoliCarCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.huolicar.internal.HuoliCarCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.huolicar.internal.HuoliCarCorpReconciliationOrderInternalService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.huolicar.HuoliCarCorpOrderConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.huolicar.HuoliCarCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.huolicar.HuoliCarCorpOrderDetailConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.huolicar.HuoliCarCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.huolicar.HuoliCarCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.huolicar.HuoliCarCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.huolicar.HuoliCarCorpReconciliationOrderDetailConsRec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("tmcHuoliCarCorpReconciliationOrderStdDataService")
public class TmcHuoliCarCorpReconciliationOrderStdDataServiceImpl
        extends HuoliCarCorpReconciliationOrderStdDataService {

    @Autowired
    @Qualifier("huoliCarCorpReconciliationOrderInternalService")
    private HuoliCarCorpReconciliationOrderInternalService huoliCarCorpReconciliationOrderInternalService;

    @Autowired
    @Qualifier("huoliCarCorpReconciliationOrderDbService")
    private HuoliCarCorpReconciliationOrderDbService huoliCarCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("huoliCarCorpReconciliationOrderConsRecInternalService")
    private HuoliCarCorpReconciliationOrderConsRecInternalService huoliCarCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("huoliCarCorpReconciliationOrderDetailConsRecInternalService")
    private HuoliCarCorpReconciliationOrderDetailConsRecInternalService huoliCarCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("tmcHuoliCarCorpOrderConsRecStdDataService")
    private CorpOrderConsRecStdDataService<HuoliCarCorpOrderConsRecStdVO, HuoliCarCorpOrderConsRecQueryParam> corpOrderConsRecStdDataService;

    @Autowired
    @Qualifier("tmcHuoliCarCorpOrderDetailConsRecStdDataService")
    private CorpOrderDetailConsRecStdDataService<HuoliCarCorpOrderDetailConsRecStdVO, HuoliCarCorpOrderDetailConsRecQueryParam> corpOrderDetailConsRecStdDataService;

    @Override
    protected Map<Long, List<Long>> getConsRecIds(final List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<HuoliCarCorpReconciliationOrderConsRec> consRecs =
                huoliCarCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(HuoliCarCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(HuoliCarCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(final List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<HuoliCarCorpReconciliationOrderDetailConsRec> detailConsRecs =
                huoliCarCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(HuoliCarCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(HuoliCarCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<HuoliCarCorpOrderConsRecStdVO> getConsRecs(List<Long> consRecIds, CorpOrderConsRecQueryCfg queryCfg) {
        final HuoliCarCorpOrderConsRecQueryParam queryParam = new HuoliCarCorpOrderConsRecQueryParam.QueryParamBuilder()
                .consRecIds(consRecIds)
                .build();
        return corpOrderConsRecStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<HuoliCarCorpOrderDetailConsRecStdVO> getDetailConsRecs(List<Long> detailConsRecIds, CorpOrderDetailConsRecQueryCfg queryCfg) {
        final HuoliCarCorpOrderDetailConsRecQueryParam queryParam = new HuoliCarCorpOrderDetailConsRecQueryParam.QueryParamBuilder()
                .detailConsRecIds(detailConsRecIds)
                .build();
        return corpOrderDetailConsRecStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(Map<String, Object> queryParams) {
        return huoliCarCorpReconciliationOrderDbService.countByParams(queryParams);
    }

    @Override
    protected List<HuoliCarCorpReconciliationOrder> searchReconciliationOrders(Map<String, Object> queryParams, List<String> showColumns) {
        return huoliCarCorpReconciliationOrderInternalService.findByParams(queryParams, showColumns);
    }
}
