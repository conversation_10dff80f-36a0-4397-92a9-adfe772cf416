package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.flight.ticket.international;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.db.IftCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.internal.IftCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.internal.IftCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.internal.IftCorpReconciliationOrderInternalService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.international.IftCorpOrderConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.international.IftCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.international.IftCorpOrderDetailConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.international.IftCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrderDetailConsRec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("tmcIftCorpReconciliationOrderStdDataService")
public class TmcIftCorpReconciliationOrderStdDataServiceImpl
        extends IftCorpReconciliationOrderStdDataService {

    @Autowired
    @Qualifier("iftCorpReconciliationOrderInternalService")
    private IftCorpReconciliationOrderInternalService iftCorpReconciliationOrderInternalService;

    @Autowired
    @Qualifier("iftCorpReconciliationOrderDbService")
    private IftCorpReconciliationOrderDbService iftCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("iftCorpReconciliationOrderConsRecInternalService")
    private IftCorpReconciliationOrderConsRecInternalService iftCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("iftCorpReconciliationOrderDetailConsRecInternalService")
    private IftCorpReconciliationOrderDetailConsRecInternalService iftCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("tmcIftCorpOrderConsRecStdDataService")
    private CorpOrderConsRecStdDataService<IftCorpOrderConsRecStdVO, IftCorpOrderConsRecQueryParam> corpOrderConsRecStdDataService;

    @Autowired
    @Qualifier("tmcIftCorpOrderDetailConsRecStdDataService")
    private CorpOrderDetailConsRecStdDataService<IftCorpOrderDetailConsRecStdVO, IftCorpOrderDetailConsRecQueryParam> corpOrderDetailConsRecStdDataService;

    @Override
    protected Map<Long, List<Long>> getConsRecIds(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<IftCorpReconciliationOrderConsRec> consRecs =
                iftCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(IftCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(IftCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<IftCorpReconciliationOrderDetailConsRec> detailConsRecs =
                iftCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(IftCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(IftCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<IftCorpOrderConsRecStdVO> getConsRecs(List<Long> consRecIds, CorpOrderConsRecQueryCfg queryCfg) {
        final IftCorpOrderConsRecQueryParam queryParam = new IftCorpOrderConsRecQueryParam.QueryParamBuilder()
                .consRecIds(consRecIds)
                .build();
        return corpOrderConsRecStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<IftCorpOrderDetailConsRecStdVO> getDetailConsRecs(List<Long> detailConsRecIds, CorpOrderDetailConsRecQueryCfg queryCfg) {
        final IftCorpOrderDetailConsRecQueryParam queryParam = new IftCorpOrderDetailConsRecQueryParam.QueryParamBuilder()
                .detailConsRecIds(detailConsRecIds)
                .build();
        return corpOrderDetailConsRecStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(Map<String, Object> queryParams) {
        return iftCorpReconciliationOrderDbService.countByParams(queryParams);
    }

    @Override
    protected List<IftCorpReconciliationOrder> searchReconciliationOrders(Map<String, Object> queryParams, List<String> showColumns) {
        return iftCorpReconciliationOrderInternalService.findByParams(queryParams, showColumns);
    }
}
