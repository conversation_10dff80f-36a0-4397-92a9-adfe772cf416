package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.flight.ticket.international;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.international.IftCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.international.IftCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.FlightTicketCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.international.IftCorpReconciliationOrderStdVO;

import java.util.Objects;

public abstract class IftCorpReconciliationOrderStdDataService
        extends AbstractCorpReconciliationOrderStdDataService<
        IftCorpReconciliationOrder, IftCorpOrderConsRecStdVO, IftCorpOrderDetailConsRecStdVO,
        IftCorpReconciliationOrderStdVO, FlightTicketCorpReconciliationOrderQueryParam> {

    @Override
    protected FlightTicketCorpReconciliationOrderQueryParam buildQueryParam(final String orderId) {
        return new FlightTicketCorpReconciliationOrderQueryParam.Builder()
                .orderId(orderId)
                .build();
    }

    @Override
    protected FlightTicketCorpReconciliationOrderQueryParam buildQueryParam(Long reconciliationOrderId) {
        return new FlightTicketCorpReconciliationOrderQueryParam.Builder()
                .reconciliationOrderId(reconciliationOrderId)
                .build();
    }

    @Override
    public IftCorpReconciliationOrderStdVO transferToStdVO(IftCorpReconciliationOrder entity) {
        final IftCorpReconciliationOrderStdVO stdVO = new IftCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        stdVO.setTmcTicketNo(entity.getTmcTicketNo());
        stdVO.setTicketId(entity.getTicketId());
        stdVO.setTicketStatus(entity.getTicketStatus());
        if (Objects.nonNull(entity.getDepTime())) {
            stdVO.setDepTime(entity.getDepTime().getTime());
        }
        if (Objects.nonNull(entity.getArrTime())) {
            stdVO.setArrTime(entity.getArrTime().getTime());
        }
        return stdVO;
    }
}
