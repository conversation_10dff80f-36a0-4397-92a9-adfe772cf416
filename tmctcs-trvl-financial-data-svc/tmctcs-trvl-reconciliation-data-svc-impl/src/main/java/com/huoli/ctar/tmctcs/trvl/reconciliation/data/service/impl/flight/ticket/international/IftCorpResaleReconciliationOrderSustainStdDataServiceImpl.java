package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.flight.ticket.international;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.db.IftCorpResaleReconciliationOrderConsRecDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.db.IftCorpResaleReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.db.IftCorpResaleReconciliationOrderDetailConsRecDbService;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.FlightTicketCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.international.IftCorpReconciliationOrderStdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("iftCorpResaleReconciliationOrderSustainStdDataService")
public class IftCorpResaleReconciliationOrderSustainStdDataServiceImpl
        extends IftCorpReconciliationOrderSustainStdDataService {

    @Autowired
    @Qualifier("iftCorpResaleReconciliationOrderDbService")
    private IftCorpResaleReconciliationOrderDbService iftCorpResaleReconciliationOrderDbService;

    @Autowired
    @Qualifier("iftCorpResaleReconciliationOrderConsRecDbService")
    private IftCorpResaleReconciliationOrderConsRecDbService iftCorpResaleReconciliationOrderConsRecDbService;

    @Autowired
    @Qualifier("iftCorpResaleReconciliationOrderDetailConsRecDbService")
    private IftCorpResaleReconciliationOrderDetailConsRecDbService iftCorpResaleReconciliationOrderDetailConsRecDbService;

    @Autowired
    @Qualifier("iftCorpResaleReconciliationOrderStdDataService")
    private CorpReconciliationOrderStdDataService<IftCorpReconciliationOrderStdVO, FlightTicketCorpReconciliationOrderQueryParam> iftCorpResaleReconciliationOrderStdDataService;

    @Override
    protected void persistReconciliationOrder(IftCorpReconciliationOrder newEntity) {
        iftCorpResaleReconciliationOrderDbService.dbInsert(newEntity);
    }

    @Override
    protected int doUpdateReconciliationOrder(IftCorpReconciliationOrder example) {
        return iftCorpResaleReconciliationOrderDbService.dbUpdate(example);
    }

    @Override
    protected int deleteCorpReconciliationOrders(Map<String, Object> deleteParams) {
        return iftCorpResaleReconciliationOrderDbService.deleteByParams(deleteParams);
    }

    @Override
    protected List<IftCorpReconciliationOrderStdVO> getExistingReconciliationOrders(final String orderId) {
        return iftCorpResaleReconciliationOrderStdDataService.getReconciliationOrders(orderId, buildQueryCfg());
    }

    @Override
    protected IftCorpReconciliationOrderStdVO getExistingReconciliationOrder(Long reconciliationOrderId) {
        return iftCorpResaleReconciliationOrderStdDataService.getReconciliationOrder(reconciliationOrderId, buildQueryCfg());
    }

    @Override
    protected void persistCorpReconciliationOrderConsRecs(List<IftCorpReconciliationOrderConsRec> reconciliationOrderConsRecs) {
        iftCorpResaleReconciliationOrderConsRecDbService.batchInsert(reconciliationOrderConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderConsRecs(Map<String, Object> deleteParams) {
        return iftCorpResaleReconciliationOrderConsRecDbService.deleteByParams(deleteParams);
    }

    @Override
    protected void persistCorpReconciliationOrderDetailConsRecs(List<IftCorpReconciliationOrderDetailConsRec> reconciliationOrderDetailConsRecs) {
        iftCorpResaleReconciliationOrderDetailConsRecDbService.batchInsert(reconciliationOrderDetailConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderDetailConsRecs(Map<String, Object> deleteParams) {
        return iftCorpResaleReconciliationOrderDetailConsRecDbService.deleteByParams(deleteParams);
    }
}
