package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.train;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.db.TrainCorpResaleReconciliationOrderConsRecDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.db.TrainCorpResaleReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.db.TrainCorpResaleReconciliationOrderDetailConsRecDbService;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.train.TrainCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.train.TrainCorpReconciliationOrderStdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("trainCorpResaleReconciliationOrderSustainStdDataService")
public class TrainCorpResaleReconciliationOrderSustainStdDataServiceImpl
        extends TrainCorpReconciliationOrderSustainStdDataService {

    @Autowired
    @Qualifier("trainCorpResaleReconciliationOrderDbService")
    private TrainCorpResaleReconciliationOrderDbService trainCorpResaleReconciliationOrderDbService;

    @Autowired
    @Qualifier("trainCorpResaleReconciliationOrderConsRecDbService")
    private TrainCorpResaleReconciliationOrderConsRecDbService trainCorpResaleReconciliationOrderConsRecDbService;

    @Autowired
    @Qualifier("trainCorpResaleReconciliationOrderDetailConsRecDbService")
    private TrainCorpResaleReconciliationOrderDetailConsRecDbService trainCorpResaleReconciliationOrderDetailConsRecDbService;

    @Autowired
    @Qualifier("trainCorpResaleReconciliationOrderStdDataService")
    private CorpReconciliationOrderStdDataService<TrainCorpReconciliationOrderStdVO, TrainCorpReconciliationOrderQueryParam> trainCorpResaleReconciliationOrderStdDataService;

    @Override
    protected void persistReconciliationOrder(TrainCorpReconciliationOrder newEntity) {
        trainCorpResaleReconciliationOrderDbService.dbInsert(newEntity);
    }

    @Override
    protected int doUpdateReconciliationOrder(TrainCorpReconciliationOrder example) {
        return trainCorpResaleReconciliationOrderDbService.dbUpdate(example);
    }

    @Override
    protected int deleteCorpReconciliationOrders(Map<String, Object> deleteParams) {
        return trainCorpResaleReconciliationOrderDbService.deleteByParams(deleteParams);
    }

    @Override
    protected List<TrainCorpReconciliationOrderStdVO> getExistingReconciliationOrders(final String orderId) {
        return trainCorpResaleReconciliationOrderStdDataService.getReconciliationOrders(orderId, buildQueryCfg());
    }

    @Override
    protected TrainCorpReconciliationOrderStdVO getExistingReconciliationOrder(Long reconciliationOrderId) {
        return trainCorpResaleReconciliationOrderStdDataService.getReconciliationOrder(reconciliationOrderId, buildQueryCfg());
    }

    @Override
    protected void persistCorpReconciliationOrderConsRecs(List<TrainCorpReconciliationOrderConsRec> reconciliationOrderConsRecs) {
        trainCorpResaleReconciliationOrderConsRecDbService.batchInsert(reconciliationOrderConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderConsRecs(Map<String, Object> deleteParams) {
        return trainCorpResaleReconciliationOrderConsRecDbService.deleteByParams(deleteParams);
    }

    @Override
    protected void persistCorpReconciliationOrderDetailConsRecs(List<TrainCorpReconciliationOrderDetailConsRec> reconciliationOrderDetailConsRecs) {
        trainCorpResaleReconciliationOrderDetailConsRecDbService.batchInsert(reconciliationOrderDetailConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderDetailConsRecs(Map<String, Object> deleteParams) {
        return trainCorpResaleReconciliationOrderDetailConsRecDbService.deleteByParams(deleteParams);
    }
}
