package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.flight.ticket.domestic;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.constant.CorpCategory;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.AbstractCorpReconciliationOrderViewStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.internal.DftCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.internal.DftCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.internal.DftCorpResaleReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.internal.DftCorpResaleReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.view.flight.ticket.domestic.DftCorpReconciliationOrderViewService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.ConsOrderCorpInfoQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.domestic.*;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.FlightTicketCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.domestic.DftCorpReconciliationOrderStdVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("dftCorpReconciliationOrderViewStdDataService")
public class DftCorpReconciliationOrderViewStdDataServiceImpl
        extends AbstractCorpReconciliationOrderViewStdDataService<DftCorpReconciliationOrder,
        DftCorpOrderConsRecStdVO, DftCorpOrderDetailConsRecStdVO, DftCorpReconciliationOrderStdVO,
        FlightTicketCorpReconciliationOrderQueryParam, DftCorpOrderConsRecQueryParam, DftCorpOrderDetailConsRecQueryParam> {

    @Autowired
    @Qualifier("dftCorpReconciliationOrderViewService")
    private DftCorpReconciliationOrderViewService dftCorpReconciliationOrderViewService;

    @Autowired
    @Qualifier("dftCorpResaleReconciliationOrderConsRecInternalService")
    private DftCorpResaleReconciliationOrderConsRecInternalService dftCorpResaleReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("dftCorpResaleReconciliationOrderDetailConsRecInternalService")
    private DftCorpResaleReconciliationOrderDetailConsRecInternalService dftCorpResaleReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("dftCorpReconciliationOrderConsRecInternalService")
    private DftCorpReconciliationOrderConsRecInternalService dftCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("dftCorpReconciliationOrderDetailConsRecInternalService")
    private DftCorpReconciliationOrderDetailConsRecInternalService dftCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("dftCorpOrderConsRecViewStdDataService")
    private CorpOrderConsRecStdDataService<DftCorpOrderConsRecStdVO, DftCorpOrderConsRecQueryParam> dftCorpOrderConsRecViewStdDataService;

    @Autowired
    @Qualifier("dftCorpOrderDetailConsRecViewStdDataService")
    private CorpOrderDetailConsRecStdDataService<DftCorpOrderDetailConsRecStdVO, DftCorpOrderDetailConsRecQueryParam> dftCorpOrderDetailConsRecViewStdDataService;

    @Override
    public DftCorpReconciliationOrderStdVO transferToStdVO(final DftCorpReconciliationOrder entity) {
        final DftCorpReconciliationOrderStdVO stdVO = new DftCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        stdVO.setOriginOrderId(entity.getOriginOrderId());
        stdVO.setFirstOrderId(entity.getFirstOrderId());
        stdVO.setTicketId(entity.getTicketId());
        stdVO.setTmcTicketNo(entity.getTmcTicketNo());
        stdVO.setTicketStatus(entity.getTicketStatus());
        if (Objects.nonNull(entity.getDepTime())) {
            stdVO.setDepTime(entity.getDepTime().getTime());
        }
        if (Objects.nonNull(entity.getArrTime())) {
            stdVO.setArrTime(entity.getArrTime().getTime());
        }
        return stdVO;
    }

    @Override
    protected DftCorpOrderConsRecQueryParam buildConsRecQueryParam(FlightTicketCorpReconciliationOrderQueryParam queryParam) {
        final DftCorpOrderConsRecQueryParam.QueryParamBuilder builder = new DftCorpOrderConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new DftCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build());
        return builder.build();
    }

    @Override
    protected DftCorpOrderDetailConsRecQueryParam buildDetailConsRecQueryParam(FlightTicketCorpReconciliationOrderQueryParam queryParam) {
        final DftCorpOrderDetailConsRecQueryParam.QueryParamBuilder builder = new DftCorpOrderDetailConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new DftCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build())
                .bizInfo(new DftOrderConsRecBizInfoQueryParam.QueryParamBuilder()
                        .ticketNo(queryParam.getTmcTicketNo())
                        .depTimeFrom(queryParam.getDepTimeFrom())
                        .depTimeTo(queryParam.getDepTimeTo())
                        .build());
        return builder.build();
    }

    @Override
    protected Map<Long, List<Long>> getConsRecIds(final List<Long> reconciliationOrderIds, final String corpBizType) {
        final List<DftCorpReconciliationOrderConsRec> consRecs;
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            consRecs = dftCorpResaleReconciliationOrderConsRecInternalService.findByParams(queryParams,
                    Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                            BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        } else {
            consRecs = dftCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                    Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                            BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));

        }
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(DftCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(DftCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(final List<Long> reconciliationOrderIds, final String corpBizType) {
        final List<DftCorpReconciliationOrderDetailConsRec> detailConsRecs;
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            detailConsRecs =
                    dftCorpResaleReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        } else {
            detailConsRecs =
                    dftCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        }
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(DftCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(DftCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<DftCorpOrderConsRecStdVO> queryConsRecs(final DftCorpOrderConsRecQueryParam queryParam,
                                                           final CorpOrderConsRecQueryCfg queryCfg) {
        return dftCorpOrderConsRecViewStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<DftCorpOrderDetailConsRecStdVO> queryDetailConsRecs(final DftCorpOrderDetailConsRecQueryParam queryParam,
                                                                       final CorpOrderDetailConsRecQueryCfg queryCfg) {
        return dftCorpOrderDetailConsRecViewStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(final Map<String, Object> queryParams) {
        return dftCorpReconciliationOrderViewService.countByParams(queryParams);
    }

    @Override
    protected List<DftCorpReconciliationOrder> searchReconciliationOrders(final Map<String, Object> queryParams,
                                                                          final List<String> showColumns) {
        return dftCorpReconciliationOrderViewService.findByParams(queryParams, showColumns);
    }
}
