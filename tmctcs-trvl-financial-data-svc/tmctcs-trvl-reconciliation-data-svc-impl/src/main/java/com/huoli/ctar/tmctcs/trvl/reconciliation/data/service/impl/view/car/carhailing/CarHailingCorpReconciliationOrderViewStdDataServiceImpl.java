package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.car.carhailing;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.constant.CorpCategory;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.AbstractCorpReconciliationOrderViewStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.carhailing.internal.CarHailingCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.carhailing.internal.CarHailingCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.view.car.carhailing.CarHailingCorpReconciliationOrderViewService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.ConsOrderCorpInfoQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.carhailing.*;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.carhailing.CarHailingCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.carhailing.CarHailingCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.carhailing.CarHailingCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.CarCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.carhailing.CarHailingCorpReconciliationOrderStdVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("carHailingCorpReconciliationOrderViewStdDataService")
public class CarHailingCorpReconciliationOrderViewStdDataServiceImpl
        extends AbstractCorpReconciliationOrderViewStdDataService<CarHailingCorpReconciliationOrder,
        CarHailingCorpOrderConsRecStdVO, CarHailingCorpOrderDetailConsRecStdVO, CarHailingCorpReconciliationOrderStdVO,
        CarCorpReconciliationOrderQueryParam, CarHailingCorpOrderConsRecQueryParam, CarHailingCorpOrderDetailConsRecQueryParam> {

    @Autowired
    @Qualifier("carHailingCorpReconciliationOrderViewService")
    private CarHailingCorpReconciliationOrderViewService carHailingCorpReconciliationOrderViewService;

    @Autowired
    @Qualifier("carHailingCorpReconciliationOrderConsRecInternalService")
    private CarHailingCorpReconciliationOrderConsRecInternalService carHailingCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("carHailingCorpReconciliationOrderDetailConsRecInternalService")
    private CarHailingCorpReconciliationOrderDetailConsRecInternalService carHailingCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("carHailingCorpOrderConsRecViewStdDataService")
    private CorpOrderConsRecStdDataService<CarHailingCorpOrderConsRecStdVO, CarHailingCorpOrderConsRecQueryParam> carHailingCorpOrderConsRecViewStdDataService;

    @Autowired
    @Qualifier("carHailingCorpOrderDetailConsRecViewStdDataService")
    private CorpOrderDetailConsRecStdDataService<CarHailingCorpOrderDetailConsRecStdVO, CarHailingCorpOrderDetailConsRecQueryParam> carHailingCorpOrderDetailConsRecViewStdDataService;

    @Override
    public CarHailingCorpReconciliationOrderStdVO transferToStdVO(final CarHailingCorpReconciliationOrder entity) {
        final CarHailingCorpReconciliationOrderStdVO stdVO = new CarHailingCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        return stdVO;
    }

    @Override
    protected CarHailingCorpOrderConsRecQueryParam buildConsRecQueryParam(final CarCorpReconciliationOrderQueryParam queryParam) {
        final CarHailingCorpOrderConsRecQueryParam.QueryParamBuilder builder = new CarHailingCorpOrderConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new CarHailingCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build());
        return builder.build();
    }

    @Override
    protected CarHailingCorpOrderDetailConsRecQueryParam buildDetailConsRecQueryParam(final CarCorpReconciliationOrderQueryParam queryParam) {
        final CarHailingCorpOrderDetailConsRecQueryParam.QueryParamBuilder builder = new CarHailingCorpOrderDetailConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new CarHailingCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build());
        return builder.build();
    }

    @Override
    protected List<CarHailingCorpOrderConsRecStdVO> queryConsRecs(final CarHailingCorpOrderConsRecQueryParam queryParam,
                                                                  final CorpOrderConsRecQueryCfg queryCfg) {
        return carHailingCorpOrderConsRecViewStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<CarHailingCorpOrderDetailConsRecStdVO> queryDetailConsRecs(final CarHailingCorpOrderDetailConsRecQueryParam queryParam,
                                                                              final CorpOrderDetailConsRecQueryCfg queryCfg) {
        return carHailingCorpOrderDetailConsRecViewStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected Map<Long, List<Long>> getConsRecIds(final List<Long> reconciliationOrderIds, final String corpBizType) {
        final Map<Long, List<Long>> consRecIdsMap;
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            consRecIdsMap = Collections.emptyMap();
        } else {
            final Map<String, Object> queryParams = QueryParamBuilder.get()
                    .param("reconciliationOrderIds", reconciliationOrderIds)
                    .build();
            final List<CarHailingCorpReconciliationOrderConsRec> consRecs =
                    carHailingCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
            if (ListUtil.isEmpty(consRecs)) {
                consRecIdsMap = Collections.emptyMap();
            } else {
                consRecIdsMap = consRecs.stream()
                        .collect(Collectors.groupingBy(CarHailingCorpReconciliationOrderConsRec::getReconciliationOrderId,
                                Collectors.mapping(CarHailingCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
            }
        }
        return consRecIdsMap;
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(final List<Long> reconciliationOrderIds, final String corpBizType) {
        final Map<Long, List<Long>> detailConsRecIsMap;
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            detailConsRecIsMap = Collections.emptyMap();
        } else {
            final Map<String, Object> queryParams = QueryParamBuilder.get()
                    .param("reconciliationOrderIds", reconciliationOrderIds)
                    .build();
            final List<CarHailingCorpReconciliationOrderDetailConsRec> detailConsRecs =
                    carHailingCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
            if (ListUtil.isEmpty(detailConsRecs)) {
                detailConsRecIsMap = Collections.emptyMap();
            } else {
                detailConsRecIsMap = detailConsRecs.stream()
                        .collect(Collectors.groupingBy(CarHailingCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                                Collectors.mapping(CarHailingCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
            }
        }
        return detailConsRecIsMap;
    }

    @Override
    protected int countReconciliationOrders(final Map<String, Object> queryParams) {
        return carHailingCorpReconciliationOrderViewService.countByParams(queryParams);
    }

    @Override
    protected List<CarHailingCorpReconciliationOrder> searchReconciliationOrders(final Map<String, Object> queryParams,
                                                                                 final List<String> showColumns) {
        return carHailingCorpReconciliationOrderViewService.findByParams(queryParams, showColumns);
    }
}
