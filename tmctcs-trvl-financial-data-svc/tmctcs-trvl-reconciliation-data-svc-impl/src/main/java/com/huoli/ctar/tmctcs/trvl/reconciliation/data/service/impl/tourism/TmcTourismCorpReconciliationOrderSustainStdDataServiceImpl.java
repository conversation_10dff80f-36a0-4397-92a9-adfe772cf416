package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.tourism;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.db.TourismCorpReconciliationOrderConsRecDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.db.TourismCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.db.TourismCorpReconciliationOrderDetailConsRecDbService;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.tourism.TourismCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.tourism.TourismCorpReconciliationOrderStdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("tmcTourismCorpReconciliationOrderSustainStdDataService")
public class TmcTourismCorpReconciliationOrderSustainStdDataServiceImpl
        extends TourismCorpReconciliationOrderSustainStdDataService {

    @Autowired
    @Qualifier("tourismCorpReconciliationOrderDbService")
    private TourismCorpReconciliationOrderDbService tourismCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("tourismCorpReconciliationOrderConsRecDbService")
    private TourismCorpReconciliationOrderConsRecDbService tourismCorpReconciliationOrderConsRecDbService;

    @Autowired
    @Qualifier("tourismCorpReconciliationOrderDetailConsRecDbService")
    private TourismCorpReconciliationOrderDetailConsRecDbService tourismCorpReconciliationOrderDetailConsRecDbService;

    @Autowired
    @Qualifier("tmcTourismCorpReconciliationOrderStdDataService")
    private CorpReconciliationOrderStdDataService<TourismCorpReconciliationOrderStdVO, TourismCorpReconciliationOrderQueryParam> tmcTourismCorpReconciliationOrderStdDataService;

    @Override
    protected void persistReconciliationOrder(TourismCorpReconciliationOrder newEntity) {
        tourismCorpReconciliationOrderDbService.dbInsert(newEntity);
    }

    @Override
    protected int doUpdateReconciliationOrder(TourismCorpReconciliationOrder example) {
        return tourismCorpReconciliationOrderDbService.dbUpdate(example);
    }

    @Override
    protected int deleteCorpReconciliationOrders(Map<String, Object> deleteParams) {
        return tourismCorpReconciliationOrderDbService.deleteByParams(deleteParams);
    }

    @Override
    protected List<TourismCorpReconciliationOrderStdVO> getExistingReconciliationOrders(final String orderId) {
        return tmcTourismCorpReconciliationOrderStdDataService.getReconciliationOrders(orderId, buildQueryCfg());
    }

    @Override
    protected TourismCorpReconciliationOrderStdVO getExistingReconciliationOrder(Long reconciliationOrderId) {
        return tmcTourismCorpReconciliationOrderStdDataService.getReconciliationOrder(reconciliationOrderId, buildQueryCfg());
    }

    @Override
    protected void persistCorpReconciliationOrderConsRecs(List<TourismCorpReconciliationOrderConsRec> reconciliationOrderConsRecs) {
        tourismCorpReconciliationOrderConsRecDbService.batchInsert(reconciliationOrderConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderConsRecs(Map<String, Object> deleteParams) {
        return tourismCorpReconciliationOrderConsRecDbService.deleteByParams(deleteParams);
    }

    @Override
    protected void persistCorpReconciliationOrderDetailConsRecs(List<TourismCorpReconciliationOrderDetailConsRec> reconciliationOrderDetailConsRecs) {
        tourismCorpReconciliationOrderDetailConsRecDbService.batchInsert(reconciliationOrderDetailConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderDetailConsRecs(Map<String, Object> deleteParams) {
        return tourismCorpReconciliationOrderDetailConsRecDbService.deleteByParams(deleteParams);
    }
}
