package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.flight.ticket.domestic;

import com.huoli.ctar.core.infra.constant.BaseShowColumn;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderSustainStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.entity.CorpOrderConsRecShowColumn;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.CorpReconciliationOrderQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.FlightTicketCorpReconciliationOrderUpdateSummary;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.domestic.DftCorpReconciliationOrderStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.domestic.DftCorpReconciliationOrderSustainStdVO;

import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

public abstract class DftCorpReconciliationOrderSustainStdDataService
        extends AbstractCorpReconciliationOrderSustainStdDataService<
        DftCorpReconciliationOrder, DftCorpReconciliationOrderSustainStdVO, FlightTicketCorpReconciliationOrderUpdateSummary,
        DftCorpReconciliationOrderConsRec, DftCorpReconciliationOrderDetailConsRec,
        DftCorpReconciliationOrderStdVO> {

    protected CorpReconciliationOrderQueryCfg buildQueryCfg() {
        return new CorpReconciliationOrderQueryCfg.QueryCfgBuilder()
                .needConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .consRecQueryCfg(new CorpOrderConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(),
                                CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .needDetailConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .detailConsRecQueryCfg(new CorpOrderDetailConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(),
                                CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .addShowColumns(DftCorpReconciliationOrder.allColumns())
                .build();
    }

    @Override
    protected DftCorpReconciliationOrderConsRec buildCorpReconciliationOrderConsRec(Long consRecId, Long reconciliationOrderId) {
        return new DftCorpReconciliationOrderConsRec(reconciliationOrderId, consRecId);
    }

    @Override
    protected DftCorpReconciliationOrderDetailConsRec buildCorpReconciliationOrderDetailConsRec(Long detailConsRecId, Long reconciliationOrderId) {
        return new DftCorpReconciliationOrderDetailConsRec(reconciliationOrderId, detailConsRecId);
    }

    @Override
    protected DftCorpReconciliationOrder toNewEntity(final DftCorpReconciliationOrderSustainStdVO reconciliationOrder) {
        final DftCorpReconciliationOrder newEntity = new DftCorpReconciliationOrder();
        assembleCommonAttributes(newEntity, reconciliationOrder);
        assembleCommonAttributesOnCreation(newEntity, reconciliationOrder);

        newEntity.setOriginOrderId(reconciliationOrder.getOriginOrderId());
        newEntity.setFirstOrderId(reconciliationOrder.getFirstOrderId());
        newEntity.setTicketId(reconciliationOrder.getTicketId());
        newEntity.setTmcTicketNo(reconciliationOrder.getTmcTicketNo());
        newEntity.setTicketStatus(reconciliationOrder.getTicketStatus());
        if (Objects.nonNull(reconciliationOrder.getDepTime())) {
            newEntity.setDepTime(new Date(reconciliationOrder.getDepTime()));
        }
        if (Objects.nonNull(reconciliationOrder.getArrTime())) {
            newEntity.setArrTime(new Date(reconciliationOrder.getArrTime()));
        }
        return newEntity;
    }

    @Override
    protected DftCorpReconciliationOrder transferToEntity(final FlightTicketCorpReconciliationOrderUpdateSummary updateSummary) {
        final DftCorpReconciliationOrder example = new DftCorpReconciliationOrder();
        assembleCommonAttributes(example, updateSummary);

        example.setOriginOrderId(updateSummary.getUpdateInfo().getOriginOrderId());
        example.setFirstOrderId(updateSummary.getUpdateInfo().getFirstOrderId());
        example.setTicketId(updateSummary.getUpdateInfo().getTicketId());
        example.setTmcTicketNo(updateSummary.getUpdateInfo().getTmcTicketNo());
        example.setTicketStatus(updateSummary.getUpdateInfo().getTicketStatus());
        if (Objects.nonNull(updateSummary.getUpdateInfo().getDepTime())) {
            example.setDepTime(new Date(updateSummary.getUpdateInfo().getDepTime()));
        }
        if (Objects.nonNull(updateSummary.getUpdateInfo().getArrTime())) {
            example.setArrTime(new Date(updateSummary.getUpdateInfo().getArrTime()));
        }
        return example;
    }

    @Override
    protected FlightTicketCorpReconciliationOrderUpdateSummary getUpdateSummary(DftCorpReconciliationOrderSustainStdVO reconciliationOrder, DftCorpReconciliationOrderStdVO existingReconciliationOrder) {
        return reconciliationOrder.getUpdateSummary(existingReconciliationOrder);
    }
}
