package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.flight.ticket.domestic;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.db.DftCorpResaleReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.internal.DftCorpResaleReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.internal.DftCorpResaleReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.internal.DftCorpResaleReconciliationOrderInternalService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.domestic.DftCorpOrderConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.domestic.DftCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.domestic.DftCorpOrderDetailConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.domestic.DftCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrderDetailConsRec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("dftCorpResaleReconciliationOrderStdDataService")
public class DftCorpResaleReconciliationOrderStdDataServiceImpl
        extends DftCorpReconciliationOrderStdDataService {

    @Autowired
    @Qualifier("dftCorpResaleReconciliationOrderInternalService")
    private DftCorpResaleReconciliationOrderInternalService dftCorpResaleReconciliationOrderInternalService;

    @Autowired
    @Qualifier("dftCorpResaleReconciliationOrderDbService")
    private DftCorpResaleReconciliationOrderDbService dftCorpResaleReconciliationOrderDbService;

    @Autowired
    @Qualifier("dftCorpResaleReconciliationOrderConsRecInternalService")
    private DftCorpResaleReconciliationOrderConsRecInternalService dftCorpResaleReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("dftCorpResaleReconciliationOrderDetailConsRecInternalService")
    private DftCorpResaleReconciliationOrderDetailConsRecInternalService dftCorpResaleReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("tmcDftCorpResaleOrderConsRecStdDataService")
    private CorpOrderConsRecStdDataService<DftCorpOrderConsRecStdVO, DftCorpOrderConsRecQueryParam> corpResaleOrderConsRecStdDataService;

    @Autowired
    @Qualifier("tmcDftCorpResaleOrderDetailConsRecStdDataService")
    private CorpOrderDetailConsRecStdDataService<DftCorpOrderDetailConsRecStdVO, DftCorpOrderDetailConsRecQueryParam> corpResaleOrderDetailConsRecStdDataService;

    @Override
    protected Map<Long, List<Long>> getConsRecIds(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<DftCorpReconciliationOrderConsRec> consRecs =
                dftCorpResaleReconciliationOrderConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(DftCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(DftCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<DftCorpReconciliationOrderDetailConsRec> detailConsRecs =
                dftCorpResaleReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(DftCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(DftCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<DftCorpOrderConsRecStdVO> getConsRecs(List<Long> consRecIds, CorpOrderConsRecQueryCfg queryCfg) {
        final DftCorpOrderConsRecQueryParam queryParam = new DftCorpOrderConsRecQueryParam.QueryParamBuilder()
                .consRecIds(consRecIds)
                .build();
        return corpResaleOrderConsRecStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<DftCorpOrderDetailConsRecStdVO> getDetailConsRecs(List<Long> detailConsRecIds, CorpOrderDetailConsRecQueryCfg queryCfg) {
        final DftCorpOrderDetailConsRecQueryParam queryParam = new DftCorpOrderDetailConsRecQueryParam.QueryParamBuilder()
                .detailConsRecIds(detailConsRecIds)
                .build();
        return corpResaleOrderDetailConsRecStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(Map<String, Object> queryParams) {
        return dftCorpResaleReconciliationOrderDbService.countByParams(queryParams);
    }

    @Override
    protected List<DftCorpReconciliationOrder> searchReconciliationOrders(Map<String, Object> queryParams, List<String> showColumns) {
        return dftCorpResaleReconciliationOrderInternalService.findByParams(queryParams, showColumns);
    }
}
