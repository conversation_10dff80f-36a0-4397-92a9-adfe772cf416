package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.hotel;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.db.HotelCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.internal.HotelCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.internal.HotelCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.internal.HotelCorpReconciliationOrderInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.internal.HotelCorpResaleReconciliationOrderInternalService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.hotel.HotelCorpOrderConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.hotel.HotelCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.hotel.HotelCorpOrderDetailConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.hotel.HotelCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrderDetailConsRec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("tmcHotelCorpReconciliationOrderStdDataService")
public class TmcHotelCorpReconciliationOrderStdDataServiceImpl
        extends HotelCorpReconciliationOrderStdDataService {

    @Autowired
    @Qualifier("hotelCorpReconciliationOrderInternalService")
    private HotelCorpReconciliationOrderInternalService hotelCorpReconciliationOrderInternalService;

    @Autowired
    @Qualifier("hotelCorpReconciliationOrderDbService")
    private HotelCorpReconciliationOrderDbService hotelCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("hotelCorpReconciliationOrderConsRecInternalService")
    private HotelCorpReconciliationOrderConsRecInternalService hotelCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("hotelCorpReconciliationOrderDetailConsRecInternalService")
    private HotelCorpReconciliationOrderDetailConsRecInternalService hotelCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("tmcHotelCorpOrderConsRecStdDataService")
    private CorpOrderConsRecStdDataService<HotelCorpOrderConsRecStdVO, HotelCorpOrderConsRecQueryParam> corpOrderConsRecStdDataService;

    @Autowired
    @Qualifier("tmcHotelCorpOrderDetailConsRecStdDataService")
    private CorpOrderDetailConsRecStdDataService<HotelCorpOrderDetailConsRecStdVO, HotelCorpOrderDetailConsRecQueryParam> corpOrderDetailConsRecStdDataService;

    @Override
    protected Map<Long, List<Long>> getConsRecIds(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<HotelCorpReconciliationOrderConsRec> consRecs =
                hotelCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(HotelCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(HotelCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<HotelCorpReconciliationOrderDetailConsRec> detailConsRecs =
                hotelCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(HotelCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(HotelCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<HotelCorpOrderConsRecStdVO> getConsRecs(List<Long> consRecIds, CorpOrderConsRecQueryCfg queryCfg) {
        final HotelCorpOrderConsRecQueryParam queryParam = new HotelCorpOrderConsRecQueryParam.QueryParamBuilder()
                .consRecIds(consRecIds)
                .build();
        return corpOrderConsRecStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<HotelCorpOrderDetailConsRecStdVO> getDetailConsRecs(List<Long> detailConsRecIds, CorpOrderDetailConsRecQueryCfg queryCfg) {
        final HotelCorpOrderDetailConsRecQueryParam queryParam = new HotelCorpOrderDetailConsRecQueryParam.QueryParamBuilder()
                .detailConsRecIds(detailConsRecIds)
                .build();
        return corpOrderDetailConsRecStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(Map<String, Object> queryParams) {
        return hotelCorpReconciliationOrderDbService.countByParams(queryParams);
    }

    @Override
    protected List<HotelCorpReconciliationOrder> searchReconciliationOrders(Map<String, Object> queryParams, List<String> showColumns) {
        return hotelCorpReconciliationOrderInternalService.findByParams(queryParams, showColumns);
    }
}
