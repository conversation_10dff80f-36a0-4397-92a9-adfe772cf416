package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.huoli.selected;

import com.huoli.ctar.core.infra.constant.BaseShowColumn;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderSustainStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.entity.CorpOrderConsRecShowColumn;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.entity.huoli.selected.HlSelectedCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.huoli.selected.HlSelectedCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.huoli.selected.HlSelectedCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.CorpReconciliationOrderQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.huoli.selected.HlSelectedCorpReconciliationOrderStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.huoli.selected.HlSelectedCorpReconciliationOrderSustainStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.huoli.selected.HlSelectedCorpReconciliationOrderUpdateSummary;

import java.util.Arrays;

public abstract class HlSelectedCorpReconciliationOrderSustainStdDataService
        extends AbstractCorpReconciliationOrderSustainStdDataService<
        HlSelectedCorpReconciliationOrder, HlSelectedCorpReconciliationOrderSustainStdVO, HlSelectedCorpReconciliationOrderUpdateSummary,
        HlSelectedCorpReconciliationOrderConsRec, HlSelectedCorpReconciliationOrderDetailConsRec,
        HlSelectedCorpReconciliationOrderStdVO> {

    protected CorpReconciliationOrderQueryCfg buildQueryCfg() {
        return new CorpReconciliationOrderQueryCfg.QueryCfgBuilder()
                .needConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .consRecQueryCfg(new CorpOrderConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(), CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .needDetailConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .detailConsRecQueryCfg(new CorpOrderDetailConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(), CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .addShowColumns(HlSelectedCorpReconciliationOrder.allColumns())
                .build();
    }

    @Override
    protected HlSelectedCorpReconciliationOrderConsRec buildCorpReconciliationOrderConsRec(Long consRecId, Long reconciliationOrderId) {
        return new HlSelectedCorpReconciliationOrderConsRec(reconciliationOrderId, consRecId);
    }

    @Override
    protected HlSelectedCorpReconciliationOrderDetailConsRec buildCorpReconciliationOrderDetailConsRec(Long detailConsRecId, Long reconciliationOrderId) {
        return new HlSelectedCorpReconciliationOrderDetailConsRec(reconciliationOrderId, detailConsRecId);
    }

    @Override
    protected HlSelectedCorpReconciliationOrder toNewEntity(HlSelectedCorpReconciliationOrderSustainStdVO reconciliationOrder) {
        final HlSelectedCorpReconciliationOrder newEntity = new HlSelectedCorpReconciliationOrder();
        assembleCommonAttributes(newEntity, reconciliationOrder);
        assembleCommonAttributesOnCreation(newEntity, reconciliationOrder);
        return newEntity;
    }

    @Override
    protected HlSelectedCorpReconciliationOrder transferToEntity(HlSelectedCorpReconciliationOrderUpdateSummary updateSummary) {
        final HlSelectedCorpReconciliationOrder example = new HlSelectedCorpReconciliationOrder();
        assembleCommonAttributes(example, updateSummary);
        return example;
    }

    @Override
    protected HlSelectedCorpReconciliationOrderUpdateSummary getUpdateSummary(HlSelectedCorpReconciliationOrderSustainStdVO reconciliationOrder,
                                                                              HlSelectedCorpReconciliationOrderStdVO existingReconciliationOrder) {
        return reconciliationOrder.getUpdateSummary(existingReconciliationOrder);
    }
}
