package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.tourism;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.db.TourismCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.internal.TourismCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.internal.TourismCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.internal.TourismCorpReconciliationOrderInternalService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.tourism.TourismCorpOrderConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.tourism.TourismCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.tourism.TourismCorpOrderDetailConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.tourism.TourismCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrderDetailConsRec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("tmcTourismCorpReconciliationOrderStdDataService")
public class TmcTourismCorpReconciliationOrderStdDataServiceImpl
        extends TourismCorpReconciliationOrderStdDataService {

    @Autowired
    @Qualifier("tourismCorpReconciliationOrderInternalService")
    private TourismCorpReconciliationOrderInternalService tourismCorpReconciliationOrderInternalService;

    @Autowired
    @Qualifier("tourismCorpReconciliationOrderDbService")
    private TourismCorpReconciliationOrderDbService tourismCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("tourismCorpReconciliationOrderConsRecInternalService")
    private TourismCorpReconciliationOrderConsRecInternalService tourismCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("tourismCorpReconciliationOrderDetailConsRecInternalService")
    private TourismCorpReconciliationOrderDetailConsRecInternalService tourismCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("tmcTourismCorpOrderConsRecStdDataService")
    private CorpOrderConsRecStdDataService<TourismCorpOrderConsRecStdVO, TourismCorpOrderConsRecQueryParam> corpOrderConsRecStdDataService;

    @Autowired
    @Qualifier("tmcTourismCorpOrderDetailConsRecStdDataService")
    private CorpOrderDetailConsRecStdDataService<TourismCorpOrderDetailConsRecStdVO, TourismCorpOrderDetailConsRecQueryParam> corpOrderDetailConsRecStdDataService;

    @Override
    protected Map<Long, List<Long>> getConsRecIds(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<TourismCorpReconciliationOrderConsRec> consRecs =
                tourismCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(TourismCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(TourismCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<TourismCorpReconciliationOrderDetailConsRec> detailConsRecs =
                tourismCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(TourismCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(TourismCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<TourismCorpOrderConsRecStdVO> getConsRecs(List<Long> consRecIds, CorpOrderConsRecQueryCfg queryCfg) {
        final TourismCorpOrderConsRecQueryParam queryParam = new TourismCorpOrderConsRecQueryParam.QueryParamBuilder()
                .consRecIds(consRecIds)
                .build();
        return corpOrderConsRecStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<TourismCorpOrderDetailConsRecStdVO> getDetailConsRecs(List<Long> detailConsRecIds, CorpOrderDetailConsRecQueryCfg queryCfg) {
        final TourismCorpOrderDetailConsRecQueryParam queryParam = new TourismCorpOrderDetailConsRecQueryParam.QueryParamBuilder()
                .detailConsRecIds(detailConsRecIds)
                .build();
        return corpOrderDetailConsRecStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(Map<String, Object> queryParams) {
        return tourismCorpReconciliationOrderDbService.countByParams(queryParams);
    }

    @Override
    protected List<TourismCorpReconciliationOrder> searchReconciliationOrders(Map<String, Object> queryParams, List<String> showColumns) {
        return tourismCorpReconciliationOrderInternalService.findByParams(queryParams, showColumns);
    }
}
