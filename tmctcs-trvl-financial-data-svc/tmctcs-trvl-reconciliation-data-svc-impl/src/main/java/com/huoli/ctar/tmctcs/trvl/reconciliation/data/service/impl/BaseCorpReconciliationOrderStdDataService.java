package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl;

import com.huoli.ctar.core.infra.service.AbstractStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.ComparableCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.ComparableCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecUniqueKey;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.OrderConsRecBizInfoStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.BaseCorpReconciliationOrderStdVO;

import java.util.Objects;

public abstract class BaseCorpReconciliationOrderStdDataService<
        E extends BaseCorpReconciliationOrder,
        CR extends ComparableCorpOrderConsRecStdVO,
        DCR extends ComparableCorpOrderDetailConsRecStdVO<? extends OrderConsRecBizInfoStdVO, ? extends CorpOrderDetailConsRecUniqueKey>,
        R extends BaseCorpReconciliationOrderStdVO<CR, DCR>>
        extends AbstractStdDataService {

    public abstract R transferToStdVO(final E entity);

    protected void assembleCommonAttributes(final R stdVO, final E entity) {
        stdVO.setAccountingFinanceObj(entity.getAccountingFinanceObj());
        stdVO.setOrderId(entity.getOrderId());
        stdVO.setOrderType(entity.getOrderType());
        stdVO.setOrderStatus(entity.getOrderStatus());

        if (Objects.nonNull(entity.getTransTimeFrom())) {
            stdVO.setTransTimeFrom(entity.getTransTimeFrom().getTime());
        }
        if (Objects.nonNull(entity.getTransTimeTo())) {
            stdVO.setTransTimeTo(entity.getTransTimeTo().getTime());
        }
        if (Objects.nonNull(entity.getTransTime())) {
            stdVO.setTransTime(entity.getTransTime().getTime());
        }
        if (Objects.nonNull(entity.getSettleDate())) {
            stdVO.setSettleDate(entity.getSettleDate().getTime());
        }

        stdVO.setCorpId(entity.getCorpId());
        stdVO.setCorpCode(entity.getCorpCode());
        stdVO.setCorpCategory(entity.getCorpCategory());
        stdVO.setCorpBizType(entity.getCorpBizType());
        super.assembleCommonAttributes(stdVO, entity);
    }
}
