package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.flight.ticket.domestic;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.domestic.DftCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.domestic.DftCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.FlightTicketCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.domestic.DftCorpReconciliationOrderStdVO;

import java.util.Objects;

public abstract class DftCorpReconciliationOrderStdDataService
        extends AbstractCorpReconciliationOrderStdDataService<
        DftCorpReconciliationOrder, DftCorpOrderConsRecStdVO, DftCorpOrderDetailConsRecStdVO,
        DftCorpReconciliationOrderStdVO, FlightTicketCorpReconciliationOrderQueryParam> {

    @Override
    protected FlightTicketCorpReconciliationOrderQueryParam buildQueryParam(final String orderId) {
        return new FlightTicketCorpReconciliationOrderQueryParam.Builder()
                .orderId(orderId)
                .build();
    }

    @Override
    protected FlightTicketCorpReconciliationOrderQueryParam buildQueryParam(Long reconciliationOrderId) {
        return new FlightTicketCorpReconciliationOrderQueryParam.Builder()
                .reconciliationOrderId(reconciliationOrderId)
                .build();
    }

    @Override
    public DftCorpReconciliationOrderStdVO transferToStdVO(DftCorpReconciliationOrder entity) {
        final DftCorpReconciliationOrderStdVO stdVO = new DftCorpReconciliationOrderStdVO();
        stdVO.setOriginOrderId(entity.getOriginOrderId());
        stdVO.setFirstOrderId(entity.getFirstOrderId());
        stdVO.setTicketId(entity.getTicketId());
        stdVO.setTmcTicketNo(entity.getTmcTicketNo());
        stdVO.setTicketStatus(entity.getTicketStatus());
        if (Objects.nonNull(entity.getDepTime())) {
            stdVO.setDepTime(entity.getDepTime().getTime());
        }
        if (Objects.nonNull(entity.getArrTime())) {
            stdVO.setArrTime(entity.getArrTime().getTime());
        }
        assembleCommonAttributes(stdVO, entity);
        return stdVO;
    }
}
