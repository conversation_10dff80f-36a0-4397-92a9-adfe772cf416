package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.car.huolicar;

import com.huoli.ctar.core.infra.constant.BaseShowColumn;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderSustainStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.entity.CorpOrderConsRecShowColumn;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.huolicar.HuoliCarCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.huolicar.HuoliCarCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.huolicar.HuoliCarCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.CorpReconciliationOrderQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.CarCorpReconciliationOrderUpdateSummary;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.huolicar.HuoliCarCorpReconciliationOrderStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.huolicar.HuoliCarCorpReconciliationOrderSustainStdVO;

import java.util.Arrays;

public abstract class HuoliCarCorpReconciliationOrderSustainStdDataService
        extends AbstractCorpReconciliationOrderSustainStdDataService<
        HuoliCarCorpReconciliationOrder, HuoliCarCorpReconciliationOrderSustainStdVO, CarCorpReconciliationOrderUpdateSummary,
        HuoliCarCorpReconciliationOrderConsRec, HuoliCarCorpReconciliationOrderDetailConsRec,
        HuoliCarCorpReconciliationOrderStdVO> {

    protected CorpReconciliationOrderQueryCfg buildQueryCfg() {
        return new CorpReconciliationOrderQueryCfg.QueryCfgBuilder()
                .needConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .consRecQueryCfg(new CorpOrderConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(),
                                CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .needDetailConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .detailConsRecQueryCfg(new CorpOrderDetailConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(),
                                CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .addShowColumns(HuoliCarCorpReconciliationOrder.allColumns())
                .build();
    }

    @Override
    protected HuoliCarCorpReconciliationOrderConsRec buildCorpReconciliationOrderConsRec(Long consRecId, Long reconciliationOrderId) {
        return new HuoliCarCorpReconciliationOrderConsRec(reconciliationOrderId, consRecId);
    }

    @Override
    protected HuoliCarCorpReconciliationOrderDetailConsRec buildCorpReconciliationOrderDetailConsRec(Long detailConsRecId, Long reconciliationOrderId) {
        return new HuoliCarCorpReconciliationOrderDetailConsRec(reconciliationOrderId, detailConsRecId);
    }

    @Override
    protected HuoliCarCorpReconciliationOrder toNewEntity(HuoliCarCorpReconciliationOrderSustainStdVO reconciliationOrder) {
        final HuoliCarCorpReconciliationOrder newEntity = new HuoliCarCorpReconciliationOrder();
        assembleCommonAttributes(newEntity, reconciliationOrder);
        assembleCommonAttributesOnCreation(newEntity, reconciliationOrder);
        return newEntity;
    }

    @Override
    protected HuoliCarCorpReconciliationOrder transferToEntity(CarCorpReconciliationOrderUpdateSummary updateSummary) {
        final HuoliCarCorpReconciliationOrder example = new HuoliCarCorpReconciliationOrder();
        assembleCommonAttributes(example, updateSummary);
        return example;
    }

    @Override
    protected CarCorpReconciliationOrderUpdateSummary getUpdateSummary(HuoliCarCorpReconciliationOrderSustainStdVO reconciliationOrder,
                                                                       HuoliCarCorpReconciliationOrderStdVO existingReconciliationOrder) {
        return reconciliationOrder.getUpdateSummary(existingReconciliationOrder);
    }
}
