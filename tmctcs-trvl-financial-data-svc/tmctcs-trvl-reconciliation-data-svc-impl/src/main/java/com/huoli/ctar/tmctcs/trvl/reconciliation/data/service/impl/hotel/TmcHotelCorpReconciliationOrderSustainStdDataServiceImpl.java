package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.hotel;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.db.HotelCorpReconciliationOrderConsRecDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.db.HotelCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.db.HotelCorpReconciliationOrderDetailConsRecDbService;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.hotel.HotelCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.hotel.HotelCorpReconciliationOrderStdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("tmcHotelCorpReconciliationOrderSustainStdDataService")
public class TmcHotelCorpReconciliationOrderSustainStdDataServiceImpl
        extends HotelCorpReconciliationOrderSustainStdDataService {

    @Autowired
    @Qualifier("hotelCorpReconciliationOrderDbService")
    private HotelCorpReconciliationOrderDbService hotelCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("hotelCorpReconciliationOrderConsRecDbService")
    private HotelCorpReconciliationOrderConsRecDbService hotelCorpReconciliationOrderConsRecDbService;

    @Autowired
    @Qualifier("hotelCorpReconciliationOrderDetailConsRecDbService")
    private HotelCorpReconciliationOrderDetailConsRecDbService hotelCorpReconciliationOrderDetailConsRecDbService;

    @Autowired
    @Qualifier("tmcHotelCorpReconciliationOrderStdDataService")
    private CorpReconciliationOrderStdDataService<HotelCorpReconciliationOrderStdVO, HotelCorpReconciliationOrderQueryParam> tmcHotelCorpReconciliationOrderStdDataService;

    @Override
    protected void persistReconciliationOrder(HotelCorpReconciliationOrder newEntity) {
        hotelCorpReconciliationOrderDbService.dbInsert(newEntity);
    }

    @Override
    protected int doUpdateReconciliationOrder(HotelCorpReconciliationOrder example) {
        return hotelCorpReconciliationOrderDbService.dbUpdate(example);
    }

    @Override
    protected int deleteCorpReconciliationOrders(Map<String, Object> deleteParams) {
        return hotelCorpReconciliationOrderDbService.deleteByParams(deleteParams);
    }

    @Override
    protected List<HotelCorpReconciliationOrderStdVO> getExistingReconciliationOrders(final String orderId) {
        return tmcHotelCorpReconciliationOrderStdDataService.getReconciliationOrders(orderId, buildQueryCfg());
    }

    @Override
    protected HotelCorpReconciliationOrderStdVO getExistingReconciliationOrder(Long reconciliationOrderId) {
        return tmcHotelCorpReconciliationOrderStdDataService.getReconciliationOrder(reconciliationOrderId, buildQueryCfg());
    }

    @Override
    protected void persistCorpReconciliationOrderConsRecs(List<HotelCorpReconciliationOrderConsRec> reconciliationOrderConsRecs) {
        hotelCorpReconciliationOrderConsRecDbService.batchInsert(reconciliationOrderConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderConsRecs(Map<String, Object> deleteParams) {
        return hotelCorpReconciliationOrderConsRecDbService.deleteByParams(deleteParams);
    }

    @Override
    protected void persistCorpReconciliationOrderDetailConsRecs(List<HotelCorpReconciliationOrderDetailConsRec> reconciliationOrderDetailConsRecs) {
        hotelCorpReconciliationOrderDetailConsRecDbService.batchInsert(reconciliationOrderDetailConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderDetailConsRecs(Map<String, Object> deleteParams) {
        return hotelCorpReconciliationOrderDetailConsRecDbService.deleteByParams(deleteParams);
    }
}
