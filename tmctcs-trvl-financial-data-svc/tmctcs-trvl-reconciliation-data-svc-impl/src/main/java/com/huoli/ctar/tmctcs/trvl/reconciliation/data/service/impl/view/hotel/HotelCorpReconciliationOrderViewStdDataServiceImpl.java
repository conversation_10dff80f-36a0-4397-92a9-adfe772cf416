package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.hotel;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.constant.CorpCategory;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.AbstractCorpReconciliationOrderViewStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.internal.HotelCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.internal.HotelCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.internal.HotelCorpResaleReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.internal.HotelCorpResaleReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.view.hotel.HotelCorpReconciliationOrderViewService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.ConsOrderCorpInfoQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.hotel.*;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.hotel.HotelCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.hotel.HotelCorpReconciliationOrderStdVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("hotelCorpReconciliationOrderViewStdDataService")
public class HotelCorpReconciliationOrderViewStdDataServiceImpl
        extends AbstractCorpReconciliationOrderViewStdDataService<HotelCorpReconciliationOrder,
        HotelCorpOrderConsRecStdVO, HotelCorpOrderDetailConsRecStdVO, HotelCorpReconciliationOrderStdVO,
        HotelCorpReconciliationOrderQueryParam, HotelCorpOrderConsRecQueryParam, HotelCorpOrderDetailConsRecQueryParam> {

    @Autowired
    @Qualifier("hotelCorpReconciliationOrderViewService")
    private HotelCorpReconciliationOrderViewService hotelCorpReconciliationOrderViewService;

    @Autowired
    @Qualifier("hotelCorpResaleReconciliationOrderConsRecInternalService")
    private HotelCorpResaleReconciliationOrderConsRecInternalService hotelCorpResaleReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("hotelCorpResaleReconciliationOrderDetailConsRecInternalService")
    private HotelCorpResaleReconciliationOrderDetailConsRecInternalService hotelCorpResaleReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("hotelCorpReconciliationOrderConsRecInternalService")
    private HotelCorpReconciliationOrderConsRecInternalService hotelCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("hotelCorpReconciliationOrderDetailConsRecInternalService")
    private HotelCorpReconciliationOrderDetailConsRecInternalService hotelCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("hotelCorpOrderConsRecViewStdDataService")
    private CorpOrderConsRecStdDataService<HotelCorpOrderConsRecStdVO, HotelCorpOrderConsRecQueryParam> hotelCorpOrderConsRecViewStdDataService;

    @Autowired
    @Qualifier("hotelCorpOrderDetailConsRecViewStdDataService")
    private CorpOrderDetailConsRecStdDataService<HotelCorpOrderDetailConsRecStdVO, HotelCorpOrderDetailConsRecQueryParam> hotelCorpOrderDetailConsRecViewStdDataService;

    @Override
    public HotelCorpReconciliationOrderStdVO transferToStdVO(final HotelCorpReconciliationOrder entity) {
        final HotelCorpReconciliationOrderStdVO stdVO = new HotelCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        stdVO.setHotelType(entity.getHotelType());
        stdVO.setHotelName(entity.getHotelName());
        stdVO.setNumOfRoomNights(entity.getNumOfRoomNights());
        stdVO.setResourceChannel(entity.getResourceChannel());
        if (Objects.nonNull(entity.getCheckInDate())) {
            stdVO.setCheckInDate(entity.getCheckInDate().getTime());
        }
        if (Objects.nonNull(entity.getCheckOutDate())) {
            stdVO.setCheckOutDate(entity.getCheckOutDate().getTime());
        }
        return stdVO;
    }

    @Override
    protected HotelCorpOrderConsRecQueryParam buildConsRecQueryParam(final HotelCorpReconciliationOrderQueryParam queryParam) {
        final HotelCorpOrderConsRecQueryParam.QueryParamBuilder builder = new HotelCorpOrderConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new HotelCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build());
        return builder.build();
    }

    @Override
    protected HotelCorpOrderDetailConsRecQueryParam buildDetailConsRecQueryParam(final HotelCorpReconciliationOrderQueryParam queryParam) {
        final HotelCorpOrderDetailConsRecQueryParam.QueryParamBuilder builder = new HotelCorpOrderDetailConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new HotelCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build())
                .bizInfo(new HotelOrderConsRecBizInfoQueryParam.QueryParamBuilder()
                        .checkOutDateFrom(queryParam.getCheckOutDateFrom())
                        .checkOutDateTo(queryParam.getCheckOutDateTo())
                        .hotelType(queryParam.getHotelType())
                        .hotelName(queryParam.getHotelName())
                        .resourceChannel(queryParam.getResourceChannel())
                        .build());
        return builder.build();
    }

    @Override
    protected Map<Long, List<Long>> getConsRecIds(final List<Long> reconciliationOrderIds, final String corpBizType) {
        final List<HotelCorpReconciliationOrderConsRec> consRecs;
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            consRecs = hotelCorpResaleReconciliationOrderConsRecInternalService.findByParams(queryParams,
                    Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                            BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        } else {
            consRecs = hotelCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                    Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                            BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));

        }
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(HotelCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(HotelCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(final List<Long> reconciliationOrderIds, final String corpBizType) {
        final List<HotelCorpReconciliationOrderDetailConsRec> detailConsRecs;
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            detailConsRecs =
                    hotelCorpResaleReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        } else {
            detailConsRecs =
                    hotelCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        }
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(HotelCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(HotelCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<HotelCorpOrderConsRecStdVO> queryConsRecs(final HotelCorpOrderConsRecQueryParam queryParam,
                                                             final CorpOrderConsRecQueryCfg queryCfg) {
        return hotelCorpOrderConsRecViewStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<HotelCorpOrderDetailConsRecStdVO> queryDetailConsRecs(final HotelCorpOrderDetailConsRecQueryParam queryParam,
                                                                         final CorpOrderDetailConsRecQueryCfg queryCfg) {
        return hotelCorpOrderDetailConsRecViewStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(final Map<String, Object> queryParams) {
        return hotelCorpReconciliationOrderViewService.countByParams(queryParams);
    }

    @Override
    protected List<HotelCorpReconciliationOrder> searchReconciliationOrders(final Map<String, Object> queryParams, List<String> showColumns) {
        return hotelCorpReconciliationOrderViewService.findByParams(queryParams, showColumns);
    }
}
