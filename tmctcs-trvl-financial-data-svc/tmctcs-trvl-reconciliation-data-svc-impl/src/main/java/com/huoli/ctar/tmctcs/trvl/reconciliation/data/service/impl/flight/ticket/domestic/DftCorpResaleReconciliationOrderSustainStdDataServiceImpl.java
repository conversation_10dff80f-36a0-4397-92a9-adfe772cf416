package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.flight.ticket.domestic;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.db.DftCorpResaleReconciliationOrderConsRecDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.db.DftCorpResaleReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.db.DftCorpResaleReconciliationOrderDetailConsRecDbService;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.FlightTicketCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.domestic.DftCorpReconciliationOrderStdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("dftCorpResaleReconciliationOrderSustainStdDataService")
public class DftCorpResaleReconciliationOrderSustainStdDataServiceImpl
        extends DftCorpReconciliationOrderSustainStdDataService {

    @Autowired
    @Qualifier("dftCorpResaleReconciliationOrderDbService")
    private DftCorpResaleReconciliationOrderDbService dftCorpResaleReconciliationOrderDbService;

    @Autowired
    @Qualifier("dftCorpResaleReconciliationOrderConsRecDbService")
    private DftCorpResaleReconciliationOrderConsRecDbService dftCorpResaleReconciliationOrderConsRecDbService;

    @Autowired
    @Qualifier("dftCorpResaleReconciliationOrderDetailConsRecDbService")
    private DftCorpResaleReconciliationOrderDetailConsRecDbService dftCorpResaleReconciliationOrderDetailConsRecDbService;

    @Autowired
    @Qualifier("dftCorpResaleReconciliationOrderStdDataService")
    private CorpReconciliationOrderStdDataService<DftCorpReconciliationOrderStdVO, FlightTicketCorpReconciliationOrderQueryParam> dftCorpResaleReconciliationOrderStdDataService;

    @Override
    protected void persistReconciliationOrder(DftCorpReconciliationOrder newEntity) {
        dftCorpResaleReconciliationOrderDbService.dbInsert(newEntity);
    }

    @Override
    protected int doUpdateReconciliationOrder(DftCorpReconciliationOrder example) {
        return dftCorpResaleReconciliationOrderDbService.dbUpdate(example);
    }

    @Override
    protected int deleteCorpReconciliationOrders(Map<String, Object> deleteParams) {
        return dftCorpResaleReconciliationOrderDbService.deleteByParams(deleteParams);
    }

    @Override
    protected List<DftCorpReconciliationOrderStdVO> getExistingReconciliationOrders(final String orderId) {
        return dftCorpResaleReconciliationOrderStdDataService.getReconciliationOrders(orderId, buildQueryCfg());
    }

    @Override
    protected DftCorpReconciliationOrderStdVO getExistingReconciliationOrder(Long reconciliationOrderId) {
        return dftCorpResaleReconciliationOrderStdDataService.getReconciliationOrder(reconciliationOrderId, buildQueryCfg());
    }

    @Override
    protected void persistCorpReconciliationOrderConsRecs(List<DftCorpReconciliationOrderConsRec> reconciliationOrderConsRecs) {
        dftCorpResaleReconciliationOrderConsRecDbService.batchInsert(reconciliationOrderConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderConsRecs(Map<String, Object> deleteParams) {
        return dftCorpResaleReconciliationOrderConsRecDbService.deleteByParams(deleteParams);
    }

    @Override
    protected void persistCorpReconciliationOrderDetailConsRecs(List<DftCorpReconciliationOrderDetailConsRec> reconciliationOrderDetailConsRecs) {
        dftCorpResaleReconciliationOrderDetailConsRecDbService.batchInsert(reconciliationOrderDetailConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderDetailConsRecs(Map<String, Object> deleteParams) {
        return dftCorpResaleReconciliationOrderDetailConsRecDbService.deleteByParams(deleteParams);
    }
}
