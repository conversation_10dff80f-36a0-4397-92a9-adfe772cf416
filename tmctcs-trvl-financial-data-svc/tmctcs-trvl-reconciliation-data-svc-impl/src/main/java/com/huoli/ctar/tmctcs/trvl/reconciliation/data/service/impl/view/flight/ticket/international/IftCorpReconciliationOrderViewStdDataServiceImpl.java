package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.flight.ticket.international;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.constant.CorpCategory;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.AbstractCorpReconciliationOrderViewStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.internal.IftCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.internal.IftCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.internal.IftCorpResaleReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.international.internal.IftCorpResaleReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.view.flight.ticket.international.IftCorpReconciliationOrderViewService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.ConsOrderCorpInfoQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.flight.ticket.international.*;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.international.IftCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.FlightTicketCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.international.IftCorpReconciliationOrderStdVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("iftCorpReconciliationOrderViewStdDataService")
public class IftCorpReconciliationOrderViewStdDataServiceImpl
        extends AbstractCorpReconciliationOrderViewStdDataService<IftCorpReconciliationOrder,
        IftCorpOrderConsRecStdVO, IftCorpOrderDetailConsRecStdVO, IftCorpReconciliationOrderStdVO,
        FlightTicketCorpReconciliationOrderQueryParam, IftCorpOrderConsRecQueryParam, IftCorpOrderDetailConsRecQueryParam> {

    @Autowired
    @Qualifier("iftCorpReconciliationOrderViewService")
    private IftCorpReconciliationOrderViewService iftCorpReconciliationOrderViewService;

    @Autowired
    @Qualifier("iftCorpResaleReconciliationOrderConsRecInternalService")
    private IftCorpResaleReconciliationOrderConsRecInternalService iftCorpResaleReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("iftCorpResaleReconciliationOrderDetailConsRecInternalService")
    private IftCorpResaleReconciliationOrderDetailConsRecInternalService iftCorpResaleReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("iftCorpReconciliationOrderConsRecInternalService")
    private IftCorpReconciliationOrderConsRecInternalService iftCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("iftCorpReconciliationOrderDetailConsRecInternalService")
    private IftCorpReconciliationOrderDetailConsRecInternalService iftCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("iftCorpOrderConsRecViewStdDataService")
    private CorpOrderConsRecStdDataService<IftCorpOrderConsRecStdVO, IftCorpOrderConsRecQueryParam> iftCorpOrderConsRecViewStdDataService;

    @Autowired
    @Qualifier("iftCorpOrderDetailConsRecViewStdDataService")
    private CorpOrderDetailConsRecStdDataService<IftCorpOrderDetailConsRecStdVO, IftCorpOrderDetailConsRecQueryParam> iftCorpOrderDetailConsRecViewStdDataService;

    @Override
    public IftCorpReconciliationOrderStdVO transferToStdVO(final IftCorpReconciliationOrder entity) {
        final IftCorpReconciliationOrderStdVO stdVO = new IftCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        stdVO.setTicketId(entity.getTicketId());
        stdVO.setTmcTicketNo(entity.getTmcTicketNo());
        stdVO.setTicketStatus(entity.getTicketStatus());
        if (Objects.nonNull(entity.getDepTime())) {
            stdVO.setDepTime(entity.getDepTime().getTime());
        }
        if (Objects.nonNull(entity.getArrTime())) {
            stdVO.setArrTime(entity.getArrTime().getTime());
        }
        return stdVO;
    }

    @Override
    protected IftCorpOrderConsRecQueryParam buildConsRecQueryParam(final FlightTicketCorpReconciliationOrderQueryParam queryParam) {
        final IftCorpOrderConsRecQueryParam.QueryParamBuilder builder = new IftCorpOrderConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new IftCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build());
        return builder.build();
    }

    @Override
    protected IftCorpOrderDetailConsRecQueryParam buildDetailConsRecQueryParam(final FlightTicketCorpReconciliationOrderQueryParam queryParam) {
        final IftCorpOrderDetailConsRecQueryParam.QueryParamBuilder builder = new IftCorpOrderDetailConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new IftCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build())
                .bizInfo(new IftOrderConsRecBizInfoQueryParam.QueryParamBuilder()
                        .ticketNo(queryParam.getTmcTicketNo())
                        .depTimeFrom(queryParam.getDepTimeFrom())
                        .depTimeTo(queryParam.getDepTimeTo())
                        .build());
        return builder.build();
    }

    @Override
    protected Map<Long, List<Long>> getConsRecIds(final List<Long> reconciliationOrderIds, final String corpBizType) {
        final List<IftCorpReconciliationOrderConsRec> consRecs;
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            consRecs = iftCorpResaleReconciliationOrderConsRecInternalService.findByParams(queryParams,
                    Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                            BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        } else {
            consRecs = iftCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                    Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                            BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));

        }
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(IftCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(IftCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(final List<Long> reconciliationOrderIds, final String corpBizType) {
        final List<IftCorpReconciliationOrderDetailConsRec> detailConsRecs;
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            detailConsRecs =
                    iftCorpResaleReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        } else {
            detailConsRecs =
                    iftCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        }
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(IftCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(IftCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<IftCorpOrderConsRecStdVO> queryConsRecs(final IftCorpOrderConsRecQueryParam queryParam,
                                                           final CorpOrderConsRecQueryCfg queryCfg) {
        return iftCorpOrderConsRecViewStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<IftCorpOrderDetailConsRecStdVO> queryDetailConsRecs(final IftCorpOrderDetailConsRecQueryParam queryParam,
                                                                       final CorpOrderDetailConsRecQueryCfg queryCfg) {
        return iftCorpOrderDetailConsRecViewStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(final Map<String, Object> queryParams) {
        return iftCorpReconciliationOrderViewService.countByParams(queryParams);
    }

    @Override
    protected List<IftCorpReconciliationOrder> searchReconciliationOrders(final Map<String, Object> queryParams,
                                                                          final List<String> showColumns) {
        return iftCorpReconciliationOrderViewService.findByParams(queryParams, showColumns);
    }
}
