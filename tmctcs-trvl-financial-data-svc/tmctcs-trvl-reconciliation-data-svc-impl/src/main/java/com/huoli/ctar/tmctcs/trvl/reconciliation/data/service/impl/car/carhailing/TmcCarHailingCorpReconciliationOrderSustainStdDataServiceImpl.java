package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.car.carhailing;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.carhailing.db.CarHailingCorpReconciliationOrderConsRecDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.carhailing.db.CarHailingCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.carhailing.db.CarHailingCorpReconciliationOrderDetailConsRecDbService;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.carhailing.CarHailingCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.carhailing.CarHailingCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.carhailing.CarHailingCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.CarCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.carhailing.CarHailingCorpReconciliationOrderStdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("tmcCarHailingCorpReconciliationOrderSustainStdDataService")
public class TmcCarHailingCorpReconciliationOrderSustainStdDataServiceImpl
        extends CarHailingCorpReconciliationOrderSustainStdDataService {

    @Autowired
    @Qualifier("carHailingCorpReconciliationOrderDbService")
    private CarHailingCorpReconciliationOrderDbService carHailingCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("carHailingCorpReconciliationOrderConsRecDbService")
    private CarHailingCorpReconciliationOrderConsRecDbService carHailingCorpReconciliationOrderConsRecDbService;

    @Autowired
    @Qualifier("carHailingCorpReconciliationOrderDetailConsRecDbService")
    private CarHailingCorpReconciliationOrderDetailConsRecDbService carHailingCorpReconciliationOrderDetailConsRecDbService;

    @Autowired
    @Qualifier("tmcCarHailingCorpReconciliationOrderStdDataService")
    private CorpReconciliationOrderStdDataService<CarHailingCorpReconciliationOrderStdVO, CarCorpReconciliationOrderQueryParam> tmcCarHailingCorpReconciliationOrderStdDataService;

    @Override
    protected void persistReconciliationOrder(final CarHailingCorpReconciliationOrder newEntity) {
        carHailingCorpReconciliationOrderDbService.dbInsert(newEntity);
    }

    @Override
    protected int doUpdateReconciliationOrder(final CarHailingCorpReconciliationOrder example) {
        return carHailingCorpReconciliationOrderDbService.dbUpdate(example);
    }

    @Override
    protected int deleteCorpReconciliationOrders(final Map<String, Object> deleteParams) {
        return carHailingCorpReconciliationOrderDbService.deleteByParams(deleteParams);
    }

    @Override
    protected List<CarHailingCorpReconciliationOrderStdVO> getExistingReconciliationOrders(String orderId) {
        return tmcCarHailingCorpReconciliationOrderStdDataService.getReconciliationOrders(orderId, buildQueryCfg());
    }

    @Override
    protected CarHailingCorpReconciliationOrderStdVO getExistingReconciliationOrder(final Long reconciliationOrderId) {
        return tmcCarHailingCorpReconciliationOrderStdDataService.getReconciliationOrder(reconciliationOrderId, buildQueryCfg());
    }

    @Override
    protected void persistCorpReconciliationOrderConsRecs(final List<CarHailingCorpReconciliationOrderConsRec> reconciliationOrderConsRecs) {
        carHailingCorpReconciliationOrderConsRecDbService.batchInsert(reconciliationOrderConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderConsRecs(final Map<String, Object> deleteParams) {
        return carHailingCorpReconciliationOrderConsRecDbService.deleteByParams(deleteParams);
    }

    @Override
    protected void persistCorpReconciliationOrderDetailConsRecs(final List<CarHailingCorpReconciliationOrderDetailConsRec> reconciliationOrderDetailConsRecs) {
        carHailingCorpReconciliationOrderDetailConsRecDbService.batchInsert(reconciliationOrderDetailConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderDetailConsRecs(final Map<String, Object> deleteParams) {
        return carHailingCorpReconciliationOrderDetailConsRecDbService.deleteByParams(deleteParams);
    }
}
