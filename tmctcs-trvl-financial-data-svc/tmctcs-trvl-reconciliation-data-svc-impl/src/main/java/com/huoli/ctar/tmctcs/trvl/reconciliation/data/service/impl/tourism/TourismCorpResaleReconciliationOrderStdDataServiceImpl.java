package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.tourism;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.db.TourismCorpResaleReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.internal.TourismCorpResaleReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.internal.TourismCorpResaleReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.internal.TourismCorpResaleReconciliationOrderInternalService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.tourism.TourismCorpOrderConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.tourism.TourismCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.tourism.TourismCorpOrderDetailConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.tourism.TourismCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrderDetailConsRec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("tourismCorpResaleReconciliationOrderStdDataService")
public class TourismCorpResaleReconciliationOrderStdDataServiceImpl
        extends TourismCorpReconciliationOrderStdDataService {

    @Autowired
    @Qualifier("tourismCorpResaleReconciliationOrderInternalService")
    private TourismCorpResaleReconciliationOrderInternalService tourismCorpResaleReconciliationOrderInternalService;

    @Autowired
    @Qualifier("tourismCorpResaleReconciliationOrderDbService")
    private TourismCorpResaleReconciliationOrderDbService tourismCorpResaleReconciliationOrderDbService;

    @Autowired
    @Qualifier("tourismCorpResaleReconciliationOrderConsRecInternalService")
    private TourismCorpResaleReconciliationOrderConsRecInternalService tourismCorpResaleReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("tourismCorpResaleReconciliationOrderDetailConsRecInternalService")
    private TourismCorpResaleReconciliationOrderDetailConsRecInternalService tourismCorpResaleReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("tmcTourismCorpResaleOrderConsRecStdDataService")
    private CorpOrderConsRecStdDataService<TourismCorpOrderConsRecStdVO, TourismCorpOrderConsRecQueryParam> corpResaleOrderConsRecStdDataService;

    @Autowired
    @Qualifier("tmcTourismCorpResaleOrderDetailConsRecStdDataService")
    private CorpOrderDetailConsRecStdDataService<TourismCorpOrderDetailConsRecStdVO, TourismCorpOrderDetailConsRecQueryParam> corpResaleOrderDetailConsRecStdDataService;

    @Override
    protected Map<Long, List<Long>> getConsRecIds(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<TourismCorpReconciliationOrderConsRec> consRecs =
                tourismCorpResaleReconciliationOrderConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(TourismCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(TourismCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<TourismCorpReconciliationOrderDetailConsRec> detailConsRecs =
                tourismCorpResaleReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(TourismCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(TourismCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<TourismCorpOrderConsRecStdVO> getConsRecs(List<Long> consRecIds, CorpOrderConsRecQueryCfg queryCfg) {
        final TourismCorpOrderConsRecQueryParam queryParam = new TourismCorpOrderConsRecQueryParam.QueryParamBuilder()
                .consRecIds(consRecIds)
                .build();
        return corpResaleOrderConsRecStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<TourismCorpOrderDetailConsRecStdVO> getDetailConsRecs(List<Long> detailConsRecIds, CorpOrderDetailConsRecQueryCfg queryCfg) {
        final TourismCorpOrderDetailConsRecQueryParam queryParam = new TourismCorpOrderDetailConsRecQueryParam.QueryParamBuilder()
                .detailConsRecIds(detailConsRecIds)
                .build();
        return corpResaleOrderDetailConsRecStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(Map<String, Object> queryParams) {
        return tourismCorpResaleReconciliationOrderDbService.countByParams(queryParams);
    }

    @Override
    protected List<TourismCorpReconciliationOrder> searchReconciliationOrders(Map<String, Object> queryParams, List<String> showColumns) {
        return tourismCorpResaleReconciliationOrderInternalService.findByParams(queryParams, showColumns);
    }
}
