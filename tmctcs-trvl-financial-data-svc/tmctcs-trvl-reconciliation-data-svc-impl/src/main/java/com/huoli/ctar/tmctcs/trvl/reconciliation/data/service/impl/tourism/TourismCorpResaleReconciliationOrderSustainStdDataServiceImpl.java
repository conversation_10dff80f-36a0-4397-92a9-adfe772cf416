package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.tourism;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.db.TourismCorpResaleReconciliationOrderConsRecDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.db.TourismCorpResaleReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.tourism.db.TourismCorpResaleReconciliationOrderDetailConsRecDbService;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.tourism.TourismCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.tourism.TourismCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.tourism.TourismCorpReconciliationOrderStdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("tourismCorpResaleReconciliationOrderSustainStdDataService")
public class TourismCorpResaleReconciliationOrderSustainStdDataServiceImpl
        extends TourismCorpReconciliationOrderSustainStdDataService {

    @Autowired
    @Qualifier("tourismCorpResaleReconciliationOrderDbService")
    private TourismCorpResaleReconciliationOrderDbService tourismCorpResaleReconciliationOrderDbService;

    @Autowired
    @Qualifier("tourismCorpResaleReconciliationOrderConsRecDbService")
    private TourismCorpResaleReconciliationOrderConsRecDbService tourismCorpResaleReconciliationOrderConsRecDbService;

    @Autowired
    @Qualifier("tourismCorpResaleReconciliationOrderDetailConsRecDbService")
    private TourismCorpResaleReconciliationOrderDetailConsRecDbService tourismCorpResaleReconciliationOrderDetailConsRecDbService;

    @Autowired
    @Qualifier("tourismCorpResaleReconciliationOrderStdDataService")
    private CorpReconciliationOrderStdDataService<TourismCorpReconciliationOrderStdVO, TourismCorpReconciliationOrderQueryParam> tourismCorpResaleReconciliationOrderStdDataService;

    @Override
    protected void persistReconciliationOrder(TourismCorpReconciliationOrder newEntity) {
        tourismCorpResaleReconciliationOrderDbService.dbInsert(newEntity);
    }

    @Override
    protected int doUpdateReconciliationOrder(TourismCorpReconciliationOrder example) {
        return tourismCorpResaleReconciliationOrderDbService.dbUpdate(example);
    }

    @Override
    protected int deleteCorpReconciliationOrders(Map<String, Object> deleteParams) {
        return tourismCorpResaleReconciliationOrderDbService.deleteByParams(deleteParams);
    }

    @Override
    protected List<TourismCorpReconciliationOrderStdVO> getExistingReconciliationOrders(final String orderId) {
        return tourismCorpResaleReconciliationOrderStdDataService.getReconciliationOrders(orderId, buildQueryCfg());
    }

    @Override
    protected TourismCorpReconciliationOrderStdVO getExistingReconciliationOrder(Long reconciliationOrderId) {
        return tourismCorpResaleReconciliationOrderStdDataService.getReconciliationOrder(reconciliationOrderId, buildQueryCfg());
    }

    @Override
    protected void persistCorpReconciliationOrderConsRecs(List<TourismCorpReconciliationOrderConsRec> reconciliationOrderConsRecs) {
        tourismCorpResaleReconciliationOrderConsRecDbService.batchInsert(reconciliationOrderConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderConsRecs(Map<String, Object> deleteParams) {
        return tourismCorpResaleReconciliationOrderConsRecDbService.deleteByParams(deleteParams);
    }

    @Override
    protected void persistCorpReconciliationOrderDetailConsRecs(List<TourismCorpReconciliationOrderDetailConsRec> reconciliationOrderDetailConsRecs) {
        tourismCorpResaleReconciliationOrderDetailConsRecDbService.batchInsert(reconciliationOrderDetailConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderDetailConsRecs(Map<String, Object> deleteParams) {
        return tourismCorpResaleReconciliationOrderDetailConsRecDbService.deleteByParams(deleteParams);
    }
}
