package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.huoli.selected;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.huoli.selected.db.HlSelectedCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.huoli.selected.internal.HlSelectedCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.huoli.selected.internal.HlSelectedCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.huoli.selected.internal.HlSelectedCorpReconciliationOrderInternalService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.huoli.selected.HlSelectedCorpOrderConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.huoli.selected.HlSelectedCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.huoli.selected.HlSelectedCorpOrderDetailConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.huoli.selected.HlSelectedCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.huoli.selected.HlSelectedCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.huoli.selected.HlSelectedCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.huoli.selected.HlSelectedCorpReconciliationOrderDetailConsRec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("tmcHlSelectedCorpReconciliationOrderStdDataService")
public class TmcHlSelectedCorpReconciliationOrderStdDataServiceImpl
        extends HlSelectedCorpReconciliationOrderStdDataService {

    @Autowired
    @Qualifier("hlSelectedCorpReconciliationOrderInternalService")
    private HlSelectedCorpReconciliationOrderInternalService hlSelectedCorpReconciliationOrderInternalService;

    @Autowired
    @Qualifier("hlSelectedCorpReconciliationOrderDbService")
    private HlSelectedCorpReconciliationOrderDbService hlSelectedCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("hlSelectedCorpReconciliationOrderConsRecInternalService")
    private HlSelectedCorpReconciliationOrderConsRecInternalService hlSelectedCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("hlSelectedCorpReconciliationOrderDetailConsRecInternalService")
    private HlSelectedCorpReconciliationOrderDetailConsRecInternalService hlSelectedCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("tmcHlSelectedCorpOrderConsRecStdDataService")
    private CorpOrderConsRecStdDataService<HlSelectedCorpOrderConsRecStdVO, HlSelectedCorpOrderConsRecQueryParam> corpOrderConsRecStdDataService;

    @Autowired
    @Qualifier("tmcHlSelectedCorpOrderDetailConsRecStdDataService")
    private CorpOrderDetailConsRecStdDataService<HlSelectedCorpOrderDetailConsRecStdVO, HlSelectedCorpOrderDetailConsRecQueryParam> corpOrderDetailConsRecStdDataService;

    @Override
    protected Map<Long, List<Long>> getConsRecIds(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<HlSelectedCorpReconciliationOrderConsRec> consRecs =
                hlSelectedCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(HlSelectedCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(HlSelectedCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<HlSelectedCorpReconciliationOrderDetailConsRec> detailConsRecs =
                hlSelectedCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(HlSelectedCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(HlSelectedCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<HlSelectedCorpOrderConsRecStdVO> getConsRecs(List<Long> consRecIds, CorpOrderConsRecQueryCfg queryCfg) {
        final HlSelectedCorpOrderConsRecQueryParam queryParam = new HlSelectedCorpOrderConsRecQueryParam.QueryParamBuilder()
                .consRecIds(consRecIds)
                .build();
        return corpOrderConsRecStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<HlSelectedCorpOrderDetailConsRecStdVO> getDetailConsRecs(List<Long> detailConsRecIds, CorpOrderDetailConsRecQueryCfg queryCfg) {
        final HlSelectedCorpOrderDetailConsRecQueryParam queryParam = new HlSelectedCorpOrderDetailConsRecQueryParam.QueryParamBuilder()
                .detailConsRecIds(detailConsRecIds)
                .build();
        return corpOrderDetailConsRecStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(Map<String, Object> queryParams) {
        return hlSelectedCorpReconciliationOrderDbService.countByParams(queryParams);
    }

    @Override
    protected List<HlSelectedCorpReconciliationOrder> searchReconciliationOrders(Map<String, Object> queryParams, List<String> showColumns) {
        return hlSelectedCorpReconciliationOrderInternalService.findByParams(queryParams, showColumns);
    }
}
