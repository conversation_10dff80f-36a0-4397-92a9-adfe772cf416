package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.car.huolicar;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.huolicar.HuoliCarCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.huolicar.HuoliCarCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.huolicar.HuoliCarCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.CarCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.car.huolicar.HuoliCarCorpReconciliationOrderStdVO;

public abstract class HuoliCarCorpReconciliationOrderStdDataService
        extends AbstractCorpReconciliationOrderStdDataService<
        HuoliCarCorpReconciliationOrder, HuoliCarCorpOrderConsRecStdVO, HuoliCarCorpOrderDetailConsRecStdVO,
        HuoliCarCorpReconciliationOrderStdVO, CarCorpReconciliationOrderQueryParam> {

    @Override
    protected CarCorpReconciliationOrderQueryParam buildQueryParam(final String orderId) {
        return new CarCorpReconciliationOrderQueryParam.Builder()
                .orderId(orderId)
                .build();
    }

    @Override
    protected CarCorpReconciliationOrderQueryParam buildQueryParam(Long reconciliationOrderId) {
        return new CarCorpReconciliationOrderQueryParam.Builder()
                .reconciliationOrderId(reconciliationOrderId)
                .build();
    }

    @Override
    public HuoliCarCorpReconciliationOrderStdVO transferToStdVO(HuoliCarCorpReconciliationOrder entity) {
        final HuoliCarCorpReconciliationOrderStdVO stdVO = new HuoliCarCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        return stdVO;
    }
}
