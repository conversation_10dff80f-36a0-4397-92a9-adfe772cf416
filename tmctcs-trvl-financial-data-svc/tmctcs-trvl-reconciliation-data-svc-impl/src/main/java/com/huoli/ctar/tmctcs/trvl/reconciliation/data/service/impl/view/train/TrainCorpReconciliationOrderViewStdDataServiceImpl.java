package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.train;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmccbs.biz.domain.cms.cam.constant.CorpCategory;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.view.AbstractCorpReconciliationOrderViewStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.internal.TrainCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.internal.TrainCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.internal.TrainCorpResaleReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.train.internal.TrainCorpResaleReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.view.train.TrainCorpReconciliationOrderViewService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.ConsOrderCorpInfoQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.train.*;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.train.TrainCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.train.TrainCorpReconciliationOrderStdVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("trainCorpReconciliationOrderViewStdDataService")
public class TrainCorpReconciliationOrderViewStdDataServiceImpl
        extends AbstractCorpReconciliationOrderViewStdDataService<TrainCorpReconciliationOrder,
        TrainCorpOrderConsRecStdVO, TrainCorpOrderDetailConsRecStdVO, TrainCorpReconciliationOrderStdVO,
        TrainCorpReconciliationOrderQueryParam, TrainCorpOrderConsRecQueryParam, TrainCorpOrderDetailConsRecQueryParam> {

    @Autowired
    @Qualifier("trainCorpReconciliationOrderViewService")
    private TrainCorpReconciliationOrderViewService trainCorpReconciliationOrderViewService;

    @Autowired
    @Qualifier("trainCorpResaleReconciliationOrderConsRecInternalService")
    private TrainCorpResaleReconciliationOrderConsRecInternalService trainCorpResaleReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("trainCorpResaleReconciliationOrderDetailConsRecInternalService")
    private TrainCorpResaleReconciliationOrderDetailConsRecInternalService trainCorpResaleReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("trainCorpReconciliationOrderConsRecInternalService")
    private TrainCorpReconciliationOrderConsRecInternalService trainCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("trainCorpReconciliationOrderDetailConsRecInternalService")
    private TrainCorpReconciliationOrderDetailConsRecInternalService trainCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("trainCorpOrderConsRecViewStdDataService")
    private CorpOrderConsRecStdDataService<TrainCorpOrderConsRecStdVO, TrainCorpOrderConsRecQueryParam> trainCorpOrderConsRecViewStdDataService;

    @Autowired
    @Qualifier("trainCorpOrderDetailConsRecViewStdDataService")
    private CorpOrderDetailConsRecStdDataService<TrainCorpOrderDetailConsRecStdVO, TrainCorpOrderDetailConsRecQueryParam> trainCorpOrderDetailConsRecViewStdDataService;

    @Override
    public TrainCorpReconciliationOrderStdVO transferToStdVO(final TrainCorpReconciliationOrder entity) {
        final TrainCorpReconciliationOrderStdVO stdVO = new TrainCorpReconciliationOrderStdVO();
        assembleCommonAttributes(stdVO, entity);
        stdVO.setSubOrderId(entity.getSubOrderId());
        stdVO.setSpOrderId(entity.getSpOrderId());
        stdVO.setTicketStatus(entity.getTicketStatus());
        if (Objects.nonNull(entity.getDepTime())) {
            stdVO.setDepTime(entity.getDepTime().getTime());
        }
        if (Objects.nonNull(entity.getArrTime())) {
            stdVO.setArrTime(entity.getArrTime().getTime());
        }
        return stdVO;
    }

    @Override
    protected TrainCorpOrderConsRecQueryParam buildConsRecQueryParam(final TrainCorpReconciliationOrderQueryParam queryParam) {
        final TrainCorpOrderConsRecQueryParam.QueryParamBuilder builder = new TrainCorpOrderConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new TrainCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build());
        return builder.build();
    }

    @Override
    protected TrainCorpOrderDetailConsRecQueryParam buildDetailConsRecQueryParam(final TrainCorpReconciliationOrderQueryParam queryParam) {
        final TrainCorpOrderDetailConsRecQueryParam.QueryParamBuilder builder = new TrainCorpOrderDetailConsRecQueryParam.QueryParamBuilder();
        builder.consInfo(new TrainCorpOrderConsInfoQueryParam.QueryParamBuilder()
                        .transStartTime(queryParam.getTransStartTime())
                        .transEndTime(queryParam.getTransEndTime())
                        .orderId(queryParam.getOrderId())
                        .tmcPayTypes(queryParam.getTmcPayTypes())
                        .tmcActualPayTypes(queryParam.getTmcActualPayTypes())
                        .resourceProviders(queryParam.getResourceProviders())
                        .advancePymtSubs(queryParam.getAdvancePymtSubs())
                        .build())
                .corpInfo(new ConsOrderCorpInfoQueryParam.QueryParamBuilder()
                        .corpBizType(queryParam.getCorpBizType())
                        .corpCode(queryParam.getCorpCode())
                        .build())
                .bizInfo(new TrainOrderConsRecBizInfoQueryParam.QueryParamBuilder()
                        .depTimeFrom(queryParam.getDepTimeFrom())
                        .depTimeTo(queryParam.getDepTimeTo())
                        .build());
        return builder.build();
    }

    @Override
    protected Map<Long, List<Long>> getConsRecIds(final List<Long> reconciliationOrderIds, final String corpBizType) {
        final List<TrainCorpReconciliationOrderConsRec> consRecs;
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            consRecs = trainCorpResaleReconciliationOrderConsRecInternalService.findByParams(queryParams,
                    Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                            BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        } else {
            consRecs = trainCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                    Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                            BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));

        }
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(TrainCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(TrainCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(final List<Long> reconciliationOrderIds, final String corpBizType) {
        final List<TrainCorpReconciliationOrderDetailConsRec> detailConsRecs;
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        if (StringUtils.equals(CorpCategory.RESALE_CORP.getCode(), corpBizType)) {
            detailConsRecs =
                    trainCorpResaleReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        } else {
            detailConsRecs =
                    trainCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                            Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                    BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        }
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(TrainCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(TrainCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<TrainCorpOrderConsRecStdVO> queryConsRecs(final TrainCorpOrderConsRecQueryParam queryParam,
                                                             final CorpOrderConsRecQueryCfg queryCfg) {
        return trainCorpOrderConsRecViewStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<TrainCorpOrderDetailConsRecStdVO> queryDetailConsRecs(final TrainCorpOrderDetailConsRecQueryParam queryParam,
                                                                         final CorpOrderDetailConsRecQueryCfg queryCfg) {
        return trainCorpOrderDetailConsRecViewStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(final Map<String, Object> queryParams) {
        return trainCorpReconciliationOrderViewService.countByParams(queryParams);
    }

    @Override
    protected List<TrainCorpReconciliationOrder> searchReconciliationOrders(final Map<String, Object> queryParams, final List<String> showColumns) {
        return trainCorpReconciliationOrderViewService.findByParams(queryParams, showColumns);
    }
}
