package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.train;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.train.TrainCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.train.TrainCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.train.TrainCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.train.TrainCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.train.TrainCorpReconciliationOrderStdVO;

import java.util.Objects;

public abstract class TrainCorpReconciliationOrderStdDataService
        extends AbstractCorpReconciliationOrderStdDataService<
        TrainCorpReconciliationOrder, TrainCorpOrderConsRecStdVO, TrainCorpOrderDetailConsRecStdVO,
        TrainCorpReconciliationOrderStdVO, TrainCorpReconciliationOrderQueryParam> {

    @Override
    protected TrainCorpReconciliationOrderQueryParam buildQueryParam(final String orderId) {
        return new TrainCorpReconciliationOrderQueryParam.Builder()
                .orderId(orderId)
                .build();
    }

    @Override
    protected TrainCorpReconciliationOrderQueryParam buildQueryParam(Long reconciliationOrderId) {
        return new TrainCorpReconciliationOrderQueryParam.Builder()
                .reconciliationOrderId(reconciliationOrderId)
                .build();
    }

    @Override
    public TrainCorpReconciliationOrderStdVO transferToStdVO(TrainCorpReconciliationOrder entity) {
        final TrainCorpReconciliationOrderStdVO stdVO = new TrainCorpReconciliationOrderStdVO();
        stdVO.setSubOrderId(entity.getSubOrderId());
        stdVO.setSpOrderId(entity.getSpOrderId());
        stdVO.setTicketStatus(entity.getTicketStatus());
        if (Objects.nonNull(entity.getDepTime())) {
            stdVO.setDepTime(entity.getDepTime().getTime());
        }
        if (Objects.nonNull(entity.getArrTime())) {
            stdVO.setArrTime(entity.getArrTime().getTime());
        }
        assembleCommonAttributes(stdVO, entity);
        return stdVO;
    }
}
