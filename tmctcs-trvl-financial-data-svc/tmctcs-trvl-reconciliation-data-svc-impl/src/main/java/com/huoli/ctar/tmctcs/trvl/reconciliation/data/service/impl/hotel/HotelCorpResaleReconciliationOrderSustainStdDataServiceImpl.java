package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.hotel;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.db.HotelCorpResaleReconciliationOrderConsRecDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.db.HotelCorpResaleReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.hotel.db.HotelCorpResaleReconciliationOrderDetailConsRecDbService;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.hotel.HotelCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.hotel.HotelCorpReconciliationOrderStdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("hotelCorpResaleReconciliationOrderSustainStdDataService")
public class HotelCorpResaleReconciliationOrderSustainStdDataServiceImpl
        extends HotelCorpReconciliationOrderSustainStdDataService {

    @Autowired
    @Qualifier("hotelCorpResaleReconciliationOrderDbService")
    private HotelCorpResaleReconciliationOrderDbService hotelCorpResaleReconciliationOrderDbService;

    @Autowired
    @Qualifier("hotelCorpResaleReconciliationOrderConsRecDbService")
    private HotelCorpResaleReconciliationOrderConsRecDbService hotelCorpResaleReconciliationOrderConsRecDbService;

    @Autowired
    @Qualifier("hotelCorpResaleReconciliationOrderDetailConsRecDbService")
    private HotelCorpResaleReconciliationOrderDetailConsRecDbService hotelCorpResaleReconciliationOrderDetailConsRecDbService;

    @Autowired
    @Qualifier("hotelCorpResaleReconciliationOrderStdDataService")
    private CorpReconciliationOrderStdDataService<HotelCorpReconciliationOrderStdVO, HotelCorpReconciliationOrderQueryParam> hotelCorpResaleReconciliationOrderStdDataService;

    @Override
    protected void persistReconciliationOrder(HotelCorpReconciliationOrder newEntity) {
        hotelCorpResaleReconciliationOrderDbService.dbInsert(newEntity);
    }

    @Override
    protected int doUpdateReconciliationOrder(HotelCorpReconciliationOrder example) {
        return hotelCorpResaleReconciliationOrderDbService.dbUpdate(example);
    }

    @Override
    protected int deleteCorpReconciliationOrders(Map<String, Object> deleteParams) {
        return hotelCorpResaleReconciliationOrderDbService.deleteByParams(deleteParams);
    }

    @Override
    protected List<HotelCorpReconciliationOrderStdVO> getExistingReconciliationOrders(final String orderId) {
        return hotelCorpResaleReconciliationOrderStdDataService.getReconciliationOrders(orderId, buildQueryCfg());
    }

    @Override
    protected HotelCorpReconciliationOrderStdVO getExistingReconciliationOrder(Long reconciliationOrderId) {
        return hotelCorpResaleReconciliationOrderStdDataService.getReconciliationOrder(reconciliationOrderId, buildQueryCfg());
    }

    @Override
    protected void persistCorpReconciliationOrderConsRecs(List<HotelCorpReconciliationOrderConsRec> reconciliationOrderConsRecs) {
        hotelCorpResaleReconciliationOrderConsRecDbService.batchInsert(reconciliationOrderConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderConsRecs(Map<String, Object> deleteParams) {
        return hotelCorpResaleReconciliationOrderConsRecDbService.deleteByParams(deleteParams);
    }

    @Override
    protected void persistCorpReconciliationOrderDetailConsRecs(List<HotelCorpReconciliationOrderDetailConsRec> reconciliationOrderDetailConsRecs) {
        hotelCorpResaleReconciliationOrderDetailConsRecDbService.batchInsert(reconciliationOrderDetailConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderDetailConsRecs(Map<String, Object> deleteParams) {
        return hotelCorpResaleReconciliationOrderDetailConsRecDbService.deleteByParams(deleteParams);
    }
}
