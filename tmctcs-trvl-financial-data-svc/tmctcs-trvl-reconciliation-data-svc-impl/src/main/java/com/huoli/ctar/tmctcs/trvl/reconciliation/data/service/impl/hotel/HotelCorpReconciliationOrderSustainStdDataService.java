package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.hotel;

import com.huoli.ctar.core.infra.constant.BaseShowColumn;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.AbstractCorpReconciliationOrderSustainStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.entity.CorpOrderConsRecShowColumn;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.hotel.HotelCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.CorpReconciliationOrderQueryCfg;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.hotel.HotelCorpReconciliationOrderStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.hotel.HotelCorpReconciliationOrderSustainStdVO;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.hotel.HotelCorpReconciliationOrderUpdateSummary;

import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

public abstract class HotelCorpReconciliationOrderSustainStdDataService
        extends AbstractCorpReconciliationOrderSustainStdDataService<
        HotelCorpReconciliationOrder, HotelCorpReconciliationOrderSustainStdVO, HotelCorpReconciliationOrderUpdateSummary,
        HotelCorpReconciliationOrderConsRec, HotelCorpReconciliationOrderDetailConsRec,
        HotelCorpReconciliationOrderStdVO> {

    protected CorpReconciliationOrderQueryCfg buildQueryCfg() {
        return new CorpReconciliationOrderQueryCfg.QueryCfgBuilder()
                .needConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .consRecQueryCfg(new CorpOrderConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(), CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .needDetailConsRecs(GlobalConstant.FLAG_YES_VALUE)
                .detailConsRecQueryCfg(new CorpOrderDetailConsRecQueryCfg.QueryCfgBuilder()
                        .addShowColumns(Arrays.asList(BaseShowColumn.ID.getName(), CorpOrderConsRecShowColumn.ORDER_ID.getName()))
                        .build())
                .addShowColumns(HotelCorpReconciliationOrder.allColumns())
                .build();
    }

    @Override
    protected HotelCorpReconciliationOrderConsRec buildCorpReconciliationOrderConsRec(Long consRecId, Long reconciliationOrderId) {
        return new HotelCorpReconciliationOrderConsRec(reconciliationOrderId, consRecId);
    }

    @Override
    protected HotelCorpReconciliationOrderDetailConsRec buildCorpReconciliationOrderDetailConsRec(Long detailConsRecId, Long reconciliationOrderId) {
        return new HotelCorpReconciliationOrderDetailConsRec(reconciliationOrderId, detailConsRecId);
    }

    @Override
    protected HotelCorpReconciliationOrder toNewEntity(HotelCorpReconciliationOrderSustainStdVO reconciliationOrder) {
        final HotelCorpReconciliationOrder newEntity = new HotelCorpReconciliationOrder();

        assembleOtherAttributes(newEntity, reconciliationOrder);
        assembleCommonAttributes(newEntity, reconciliationOrder);
        assembleCommonAttributesOnCreation(newEntity, reconciliationOrder);
        return newEntity;
    }

    @Override
    protected HotelCorpReconciliationOrder transferToEntity(HotelCorpReconciliationOrderUpdateSummary updateSummary) {
        final HotelCorpReconciliationOrder example = new HotelCorpReconciliationOrder();

        assembleOtherAttributes(example, updateSummary);
        assembleCommonAttributes(example, updateSummary);
        return example;
    }

    @Override
    protected HotelCorpReconciliationOrderUpdateSummary getUpdateSummary(HotelCorpReconciliationOrderSustainStdVO reconciliationOrder,
                                                                         HotelCorpReconciliationOrderStdVO existingReconciliationOrder) {
        return reconciliationOrder.getUpdateSummary(existingReconciliationOrder);
    }

    private void assembleOtherAttributes(HotelCorpReconciliationOrder newEntity,
                                         HotelCorpReconciliationOrderSustainStdVO reconciliationOrder) {
        newEntity.setNumOfRoomNights(reconciliationOrder.getNumOfRoomNights());
        newEntity.setHotelName(reconciliationOrder.getHotelName());
        newEntity.setHotelType(reconciliationOrder.getHotelType());
        newEntity.setResourceChannel(reconciliationOrder.getResourceChannel());
        newEntity.setCheckInDate(new Date(reconciliationOrder.getCheckInDate()));
        newEntity.setCheckOutDate(new Date(reconciliationOrder.getCheckOutDate()));
    }

    private void assembleOtherAttributes(HotelCorpReconciliationOrder newEntity,
                                         HotelCorpReconciliationOrderUpdateSummary updateSummary) {
        newEntity.setNumOfRoomNights(updateSummary.getUpdateInfo().getNumOfRoomNights());
        newEntity.setHotelName(updateSummary.getUpdateInfo().getHotelName());
        newEntity.setHotelType(updateSummary.getUpdateInfo().getHotelType());
        newEntity.setResourceChannel(updateSummary.getUpdateInfo().getResourceChannel());
        if (Objects.nonNull(updateSummary.getUpdateInfo().getCheckInDate())) {
            newEntity.setCheckInDate(new Date(updateSummary.getUpdateInfo().getCheckInDate()));
        }
        if (Objects.nonNull(updateSummary.getUpdateInfo().getCheckOutDate())) {
            newEntity.setCheckOutDate(new Date(updateSummary.getUpdateInfo().getCheckOutDate()));
        }
    }
}
