package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.car.carhailing;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.common.utils.QueryParamBuilder;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderConsRecStdDataService;
import com.huoli.ctar.tmctcs.trvl.consume.data.service.CorpOrderDetailConsRecStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.carhailing.db.CarHailingCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.carhailing.internal.CarHailingCorpReconciliationOrderConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.carhailing.internal.CarHailingCorpReconciliationOrderDetailConsRecInternalService;
import com.huoli.ctar.tmctfs.basic.srd.service.car.carhailing.internal.CarHailingCorpReconciliationOrderInternalService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecQueryCfg;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.carhailing.CarHailingCorpOrderConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.carhailing.CarHailingCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.carhailing.CarHailingCorpOrderDetailConsRecQueryParam;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.car.carhailing.CarHailingCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.carhailing.CarHailingCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.carhailing.CarHailingCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.car.carhailing.CarHailingCorpReconciliationOrderDetailConsRec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("tmcCarHailingCorpReconciliationOrderStdDataService")
public class TmcCarHailingCorpReconciliationOrderStdDataServiceImpl
        extends CarHailingCorpReconciliationOrderStdDataService {

    @Autowired
    @Qualifier("carHailingCorpReconciliationOrderInternalService")
    private CarHailingCorpReconciliationOrderInternalService carHailingCorpReconciliationOrderInternalService;

    @Autowired
    @Qualifier("carHailingCorpReconciliationOrderDbService")
    private CarHailingCorpReconciliationOrderDbService carHailingCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("carHailingCorpReconciliationOrderConsRecInternalService")
    private CarHailingCorpReconciliationOrderConsRecInternalService carHailingCorpReconciliationOrderConsRecInternalService;

    @Autowired
    @Qualifier("carHailingCorpReconciliationOrderDetailConsRecInternalService")
    private CarHailingCorpReconciliationOrderDetailConsRecInternalService carHailingCorpReconciliationOrderDetailConsRecInternalService;

    @Autowired
    @Qualifier("tmcCarHailingCorpOrderConsRecStdDataService")
    private CorpOrderConsRecStdDataService<CarHailingCorpOrderConsRecStdVO, CarHailingCorpOrderConsRecQueryParam> corpOrderConsRecStdDataService;

    @Autowired
    @Qualifier("tmcCarHailingCorpOrderDetailConsRecStdDataService")
    private CorpOrderDetailConsRecStdDataService<CarHailingCorpOrderDetailConsRecStdVO, CarHailingCorpOrderDetailConsRecQueryParam> corpOrderDetailConsRecStdDataService;

    @Override
    protected Map<Long, List<Long>> getConsRecIds(final List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<CarHailingCorpReconciliationOrderConsRec> consRecs =
                carHailingCorpReconciliationOrderConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderConsRec.ShowColumn.CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(consRecs)) {
            return Collections.emptyMap();
        }
        return consRecs.stream()
                .collect(Collectors.groupingBy(CarHailingCorpReconciliationOrderConsRec::getReconciliationOrderId,
                        Collectors.mapping(CarHailingCorpReconciliationOrderConsRec::getConsRecId, Collectors.toList())));
    }

    @Override
    protected Map<Long, List<Long>> getDetailConsRecIs(final List<Long> reconciliationOrderIds) {
        final Map<String, Object> queryParams = QueryParamBuilder.get()
                .param("reconciliationOrderIds", reconciliationOrderIds)
                .build();
        final List<CarHailingCorpReconciliationOrderDetailConsRec> detailConsRecs =
                carHailingCorpReconciliationOrderDetailConsRecInternalService.findByParams(queryParams,
                        Arrays.asList(BaseCorpReconciliationOrderDetailConsRec.ShowColumn.RECONCILIATION_ORDER_ID.getName(),
                                BaseCorpReconciliationOrderDetailConsRec.ShowColumn.DETAIL_CONS_REC_ID.getName()));
        if (ListUtil.isEmpty(detailConsRecs)) {
            return Collections.emptyMap();
        }
        return detailConsRecs.stream()
                .collect(Collectors.groupingBy(CarHailingCorpReconciliationOrderDetailConsRec::getReconciliationOrderId,
                        Collectors.mapping(CarHailingCorpReconciliationOrderDetailConsRec::getDetailConsRecId, Collectors.toList())));
    }

    @Override
    protected List<CarHailingCorpOrderConsRecStdVO> getConsRecs(List<Long> consRecIds, CorpOrderConsRecQueryCfg queryCfg) {
        final CarHailingCorpOrderConsRecQueryParam queryParam = new CarHailingCorpOrderConsRecQueryParam.QueryParamBuilder()
                .consRecIds(consRecIds)
                .build();
        return corpOrderConsRecStdDataService.queryConsRecs(queryParam, queryCfg);
    }

    @Override
    protected List<CarHailingCorpOrderDetailConsRecStdVO> getDetailConsRecs(List<Long> detailConsRecIds, CorpOrderDetailConsRecQueryCfg queryCfg) {
        final CarHailingCorpOrderDetailConsRecQueryParam queryParam = new CarHailingCorpOrderDetailConsRecQueryParam.QueryParamBuilder()
                .detailConsRecIds(detailConsRecIds)
                .build();
        return corpOrderDetailConsRecStdDataService.queryDetailConsRecs(queryParam, queryCfg);
    }

    @Override
    protected int countReconciliationOrders(final Map<String, Object> queryParams) {
        return carHailingCorpReconciliationOrderDbService.countByParams(queryParams);
    }

    @Override
    protected List<CarHailingCorpReconciliationOrder> searchReconciliationOrders(final Map<String, Object> queryParams, final List<String> showColumns) {
        return carHailingCorpReconciliationOrderInternalService.findByParams(queryParams, showColumns);
    }
}
