package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl;

import com.huoli.ctar.common.utils.JsonUtil;
import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.exception.ServiceException;
import com.huoli.ctar.core.infra.model.StdDataVO;
import com.huoli.ctar.core.infra.service.AbstractSustainStdDataService;
import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderSustainStdDataService;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.ComparableCorpOrderConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.ComparableCorpOrderDetailConsRecStdVO;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.CorpOrderDetailConsRecUniqueKey;
import com.huoli.ctar.tmctfs.beans.cdm.vo.std.OrderConsRecBizInfoStdVO;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.BaseCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

public abstract class AbstractCorpReconciliationOrderSustainStdDataService<
        E extends BaseCorpReconciliationOrder,
        R extends BaseCorpReconciliationOrderSustainStdVO,
        S extends BaseCorpReconciliationOrderUpdateSummary<? extends BaseCorpReconciliationOrderUpdateInfo>,
        CR extends BaseCorpReconciliationOrderConsRec,
        DCR extends BaseCorpReconciliationOrderDetailConsRec,
        RS extends BaseCorpReconciliationOrderStdVO<? extends ComparableCorpOrderConsRecStdVO, ? extends ComparableCorpOrderDetailConsRecStdVO<? extends OrderConsRecBizInfoStdVO, ? extends CorpOrderDetailConsRecUniqueKey>>>
        extends AbstractSustainStdDataService
        implements CorpReconciliationOrderSustainStdDataService<R> {

    private static final Logger log = LoggerFactory.getLogger(AbstractCorpReconciliationOrderSustainStdDataService.class);

    @Override
    @Transactional(value = "tmccsaMysqlTransactionManager")
    public Long saveReconciliationOrder(final R reconciliationOrder) {
        if (Objects.isNull(reconciliationOrder)) {
            return GlobalConstant.DEFAULT_SID;
        }
        final E newEntity = toNewEntity(reconciliationOrder);
        newEntity.assignDefaultValueIfNecessary();
        persistReconciliationOrder(newEntity);

        final Long reconciliationOrderId = newEntity.getSid();
        configCorpReconciliationOrderConsRecRelationships(reconciliationOrder.getConsRecIds(), reconciliationOrderId);
        configCorpReconciliationOrderDetailConsRecRelationships(reconciliationOrder.getDetailConsRecIds(), reconciliationOrderId);
        return reconciliationOrderId;
    }

    @Override
    @Transactional(value = "tmccsaMysqlTransactionManager")
    public void updateReconciliationOrder(final R reconciliationOrder) {
        final RS existingReconciliationOrder = getExistingReconciliationOrder(reconciliationOrder.getId());
        if (Objects.isNull(existingReconciliationOrder)) {
            throw new ServiceException(String.format("标准对账订单不存在, id: %s, orderId: %s, orderType: %s",
                    reconciliationOrder.getId(), reconciliationOrder.getOrderId(), reconciliationOrder.getOrderType()));
        }
        final S updateSummary = getUpdateSummary(reconciliationOrder, existingReconciliationOrder);
        if (updateSummary.isNeedUpdate()) {
            final E example = transferToEntity(updateSummary);
            assembleCommonAttributesOnUpdate(example, existingReconciliationOrder, reconciliationOrder.getLastModifiedBy(), null);

            final int updatedRows = doUpdateReconciliationOrder(example);
            if (0 == updatedRows) {
                throw new ServiceException(String.format("标准对账订单已经被其他人修改, id: %s, orderId: %s, orderType: %s",
                        reconciliationOrder.getId(), reconciliationOrder.getOrderId(), reconciliationOrder.getOrderType()));
            }
        }
        final List<Long> existingConsRecIds = ListUtil.isEmpty(existingReconciliationOrder.getConsRecs())
                ? Collections.emptyList() : existingReconciliationOrder.getConsRecs().stream().map(StdDataVO::getId).collect(Collectors.toList());
        final List<Long> existingDetailConsRecIds = ListUtil.isEmpty(existingReconciliationOrder.getDetailConsRecs())
                ? Collections.emptyList() : existingReconciliationOrder.getDetailConsRecs().stream().map(StdDataVO::getId).collect(Collectors.toList());

        syncCorpReconciliationOrderConsRecRelationships(reconciliationOrder.getConsRecIds(), existingConsRecIds, reconciliationOrder.getId());
        syncCorpReconciliationOrderDetailConsRecRelationships(reconciliationOrder.getDetailConsRecIds(), existingDetailConsRecIds, reconciliationOrder.getId());
    }

    protected abstract void persistReconciliationOrder(final E newEntity);

    protected abstract int doUpdateReconciliationOrder(final E example);

    @Override
    public void removeReconciliationOrders(final String orderId) {
        final List<RS> existingReconciliationOrders = getExistingReconciliationOrders(orderId);
        if (ListUtil.isEmpty(existingReconciliationOrders)) {
            return;
        }
        for (final RS existingReconciliationOrder : existingReconciliationOrders) {
            removeReconciliationOrder(existingReconciliationOrder.getId());
        }
    }

    @Override
    @Transactional(value = "tmccsaMysqlTransactionManager")
    public void removeReconciliationOrder(final Long reconciliationOrderId) {
        final RS existingReconciliationOrder = getExistingReconciliationOrder(reconciliationOrderId);
        if (Objects.isNull(existingReconciliationOrder)) {
            throw new ServiceException(String.format("标准对账订单不存在, id: %s", reconciliationOrderId));
        }
        final int deletedReconciliationOrderRows = deleteCorpReconciliationOrder(reconciliationOrderId);
        if (log.isInfoEnabled()) {
            log.info(String.format("删除标准对账订单%s条, id: %s, orderId: %s, orderType: %s",
                    deletedReconciliationOrderRows, existingReconciliationOrder.getId(), existingReconciliationOrder.getOrderId(), existingReconciliationOrder.getOrderType()));
        }

        final int deletedConsRecRows = deleteCorpReconciliationOrderConsRecs(reconciliationOrderId);
        if (log.isInfoEnabled()) {
            log.info(String.format("删除标准对账订单%s条订单纬度消费记录, id: %s, orderId: %s, orderType: %s",
                    deletedConsRecRows, existingReconciliationOrder.getId(), existingReconciliationOrder.getOrderId(), existingReconciliationOrder.getOrderType()));
        }
        final int deletedDetailConsRecRows = deleteCorpReconciliationOrderDetailConsRecs(reconciliationOrderId);
        if (log.isInfoEnabled()) {
            log.info(String.format("删除标准对账订单%s条明细消费记录, id: %s, orderId: %s, orderType: %s",
                    deletedDetailConsRecRows, existingReconciliationOrder.getId(), existingReconciliationOrder.getOrderId(), existingReconciliationOrder.getOrderType()));
        }
    }

    protected int deleteCorpReconciliationOrder(final Long reconciliationOrderId) {
        final Map<String, Object> deleteParams = new HashMap<>();
        deleteParams.put("sid", reconciliationOrderId);
        return deleteCorpReconciliationOrders(deleteParams);
    }

    protected abstract int deleteCorpReconciliationOrders(final Map<String, Object> deleteParams);

    protected int deleteCorpReconciliationOrderConsRecs(final Long reconciliationOrderId) {
        final Map<String, Object> deleteParams = new HashMap<>();
        deleteParams.put("reconciliationOrderId", reconciliationOrderId);
        return deleteCorpReconciliationOrderConsRecs(deleteParams);
    }

    protected int deleteCorpReconciliationOrderDetailConsRecs(final Long reconciliationOrderId) {
        final Map<String, Object> deleteParams = new HashMap<>();
        deleteParams.put("reconciliationOrderId", reconciliationOrderId);
        return deleteCorpReconciliationOrderDetailConsRecs(deleteParams);
    }

    protected abstract List<RS> getExistingReconciliationOrders(final String orderId);

    protected abstract RS getExistingReconciliationOrder(final Long reconciliationOrderId);

    protected abstract CR buildCorpReconciliationOrderConsRec(final Long consRecId, final Long reconciliationOrderId);

    protected abstract DCR buildCorpReconciliationOrderDetailConsRec(final Long detailConsRecId, final Long reconciliationOrderId);

    protected abstract void persistCorpReconciliationOrderConsRecs(final List<CR> reconciliationOrderConsRecs);

    protected abstract int deleteCorpReconciliationOrderConsRecs(final Map<String, Object> deleteParams);

    protected abstract void persistCorpReconciliationOrderDetailConsRecs(final List<DCR> reconciliationOrderDetailConsRecs);

    protected abstract int deleteCorpReconciliationOrderDetailConsRecs(final Map<String, Object> deleteParams);

    private void syncCorpReconciliationOrderDetailConsRecRelationships(final List<Long> detailConsRecIds,
                                                                       final List<Long> existingDetailConsRecIds,
                                                                       final Long reconciliationOrderId) {

        if (ListUtil.isEmpty(detailConsRecIds) && ListUtil.isEmpty(existingDetailConsRecIds)) {
            return;
        }
        final List<Long> remainingDetailConsRecIds =
                ListUtil.getIntersection(detailConsRecIds, existingDetailConsRecIds);
        final List<Long> removedDetailConsRecIds = ListUtil.getDifference(existingDetailConsRecIds, remainingDetailConsRecIds);
        final List<Long> addedDetailConsRecIds = ListUtil.getDifference(detailConsRecIds, remainingDetailConsRecIds);

        if (ListUtil.isNotEmpty(removedDetailConsRecIds)) {
            removeCorpReconciliationOrderDetailConsRecRelationships(removedDetailConsRecIds, reconciliationOrderId);
        }
        if (ListUtil.isNotEmpty(addedDetailConsRecIds)) {
            configCorpReconciliationOrderDetailConsRecRelationships(addedDetailConsRecIds, reconciliationOrderId);
        }
    }

    private void configCorpReconciliationOrderDetailConsRecRelationships(final List<Long> detailConsRecIds,
                                                                         final Long reconciliationOrderId) {
        if (ListUtil.isEmpty(detailConsRecIds)) {
            return;
        }
        final Date currentTime = new Date(System.currentTimeMillis());
        final List<DCR> reconciliationOrderDetailConsRecs = new ArrayList<>(detailConsRecIds.size());
        for (final Long detailConsRecId : detailConsRecIds) {
            final DCR reconciliationOrderDetailConsRec = buildCorpReconciliationOrderDetailConsRec(detailConsRecId, reconciliationOrderId);
            reconciliationOrderDetailConsRec.setCreatedBy(GlobalConstant.CtarSys.TMCTCS);
            reconciliationOrderDetailConsRec.setCreatedTime(currentTime);
            reconciliationOrderDetailConsRec.setVersionNum(GlobalConstant.INIT_VERSION_NUM_VALUE);
            reconciliationOrderDetailConsRecs.add(reconciliationOrderDetailConsRec);
        }
        persistCorpReconciliationOrderDetailConsRecs(reconciliationOrderDetailConsRecs);

        if (log.isInfoEnabled()) {
            log.info(String.format("configCorpReconciliationOrderDetailConsRecRelationships detailConsRecIds: %s, reconciliationOrderId: %s",
                    JsonUtil.toJson(detailConsRecIds), reconciliationOrderId));
        }
    }

    private void removeCorpReconciliationOrderDetailConsRecRelationships(final List<Long> detailConsRecIds,
                                                                         final Long reconciliationOrderId) {
        if (ListUtil.isEmpty(detailConsRecIds)) {
            return;
        }
        final Map<String, Object> deleteParams = new HashMap<>();
        deleteParams.put("detailConsRecIds", detailConsRecIds);
        deleteParams.put("reconciliationOrderId", reconciliationOrderId);
        final int deletedRows = deleteCorpReconciliationOrderDetailConsRecs(deleteParams);
        if (deletedRows != detailConsRecIds.size()) {
            throw new ServiceException("对账订单已被其他人修改");
        }

        if (log.isInfoEnabled()) {
            log.info(String.format("removeCorpReconciliationOrderDetailConsRecRelationships detailConsRecIds: %s, reconciliationOrderId: %s",
                    JsonUtil.toJson(detailConsRecIds), reconciliationOrderId));
        }
    }

    private void configCorpReconciliationOrderConsRecRelationships(final List<Long> consRecIds,
                                                                   final Long reconciliationOrderId) {
        if (ListUtil.isEmpty(consRecIds)) {
            return;
        }
        final Date currentTime = new Date(System.currentTimeMillis());
        final List<CR> reconciliationOrderConsRecs = new ArrayList<>(consRecIds.size());
        for (final Long consRecId : consRecIds) {
            final CR reconciliationOrderConsRec = buildCorpReconciliationOrderConsRec(consRecId, reconciliationOrderId);
            reconciliationOrderConsRec.setCreatedBy(GlobalConstant.CtarSys.TMCTCS);
            reconciliationOrderConsRec.setCreatedTime(currentTime);
            reconciliationOrderConsRec.setVersionNum(GlobalConstant.INIT_VERSION_NUM_VALUE);
            reconciliationOrderConsRecs.add(reconciliationOrderConsRec);
        }
        persistCorpReconciliationOrderConsRecs(reconciliationOrderConsRecs);

        if (log.isInfoEnabled()) {
            log.info(String.format("configCorpReconciliationOrderConsRecRelationships consRecIds: %s, reconciliationOrderId: %s",
                    JsonUtil.toJson(consRecIds), reconciliationOrderId));
        }
    }

    private void removeCorpReconciliationOrderConsRecRelationships(final List<Long> consRecIds,
                                                                   final Long reconciliationOrderId) {
        if (ListUtil.isEmpty(consRecIds)) {
            return;
        }
        final Map<String, Object> deleteParams = new HashMap<>();
        deleteParams.put("consRecIds", consRecIds);
        deleteParams.put("reconciliationOrderId", reconciliationOrderId);
        final int deletedRows = deleteCorpReconciliationOrderConsRecs(deleteParams);
        if (deletedRows != consRecIds.size()) {
            throw new ServiceException("对账订单已被其他人修改");
        }

        if (log.isInfoEnabled()) {
            log.info(String.format("removeCorpReconciliationOrderConsRecRelationships consRecIds: %s, reconciliationOrderId: %s",
                    JsonUtil.toJson(consRecIds), reconciliationOrderId));
        }
    }

    private void syncCorpReconciliationOrderConsRecRelationships(final List<Long> consRecIds,
                                                                 final List<Long> existingConsRecIds,
                                                                 final Long reconciliationOrderId) {

        if (ListUtil.isEmpty(consRecIds) && ListUtil.isEmpty(existingConsRecIds)) {
            return;
        }
        final List<Long> remainingConsRecIds =
                ListUtil.getIntersection(consRecIds, existingConsRecIds);
        final List<Long> removedConsRecIds = ListUtil.getDifference(existingConsRecIds, remainingConsRecIds);
        final List<Long> addedConsRecIds = ListUtil.getDifference(consRecIds, remainingConsRecIds);

        if (ListUtil.isNotEmpty(removedConsRecIds)) {
            removeCorpReconciliationOrderConsRecRelationships(removedConsRecIds, reconciliationOrderId);
        }
        if (ListUtil.isNotEmpty(addedConsRecIds)) {
            configCorpReconciliationOrderConsRecRelationships(addedConsRecIds, reconciliationOrderId);
        }
    }

    protected abstract E toNewEntity(final R reconciliationOrder);

    protected abstract E transferToEntity(final S updateSummary);

    protected abstract S getUpdateSummary(final R reconciliationOrder, final RS existingReconciliationOrder);

    protected void assembleCommonAttributes(final E example, final S updateSummary) {
        example.setAccountingFinanceObj(updateSummary.getUpdateInfo().getAccountingFinanceObj());
        example.setOrderType(updateSummary.getUpdateInfo().getOrderType());
        example.setOrderStatus(updateSummary.getUpdateInfo().getOrderStatus());
        if (Objects.nonNull(updateSummary.getUpdateInfo().getTransTimeFrom())) {
            example.setTransTimeFrom(new Date(updateSummary.getUpdateInfo().getTransTimeFrom()));
        }
        if (Objects.nonNull(updateSummary.getUpdateInfo().getTransTimeTo())) {
            example.setTransTimeTo(new Date(updateSummary.getUpdateInfo().getTransTimeTo()));
        }
        if (Objects.nonNull(updateSummary.getUpdateInfo().getSettleDate())) {
            example.setSettleDate(new Date(updateSummary.getUpdateInfo().getSettleDate()));
        }
        if (Objects.nonNull(updateSummary.getUpdateInfo().getTransTime())) {
            example.setTransTime(new Date(updateSummary.getUpdateInfo().getTransTime()));
        }
        example.setCorpId(updateSummary.getUpdateInfo().getCorpId());
        example.setCorpCode(updateSummary.getUpdateInfo().getCorpCode());
        example.setCorpCategory(updateSummary.getUpdateInfo().getCorpCategory());
        example.setCorpBizType(updateSummary.getUpdateInfo().getCorpBizType());
    }

    protected void assembleCommonAttributes(final E newEntity, final R reconciliationOrder) {
        newEntity.setAccountingFinanceObj(reconciliationOrder.getAccountingFinanceObj());
        newEntity.setOrderId(reconciliationOrder.getOrderId());
        newEntity.setOrderType(reconciliationOrder.getOrderType());
        newEntity.setOrderStatus(reconciliationOrder.getOrderStatus());
        if (Objects.nonNull(reconciliationOrder.getTransTimeFrom())) {
            newEntity.setTransTimeFrom(new Date(reconciliationOrder.getTransTimeFrom()));
        }
        if (Objects.nonNull(reconciliationOrder.getTransTimeTo())) {
            newEntity.setTransTimeTo(new Date(reconciliationOrder.getTransTimeTo()));
        }
        if (Objects.nonNull(reconciliationOrder.getSettleDate())) {
            newEntity.setSettleDate(new Date(reconciliationOrder.getSettleDate()));
        }
        if (Objects.nonNull(reconciliationOrder.getTransTime())) {
            newEntity.setTransTime(new Date(reconciliationOrder.getTransTime()));
        }
        newEntity.setCorpId(reconciliationOrder.getCorpId());
        newEntity.setCorpCode(reconciliationOrder.getCorpCode());
        newEntity.setCorpCategory(reconciliationOrder.getCorpCategory());
        newEntity.setCorpBizType(reconciliationOrder.getCorpBizType());
    }
}
