package com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.impl.flight.ticket.domestic;

import com.huoli.ctar.tmctcs.trvl.reconciliation.data.service.CorpReconciliationOrderStdDataService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.db.DftCorpReconciliationOrderConsRecDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.db.DftCorpReconciliationOrderDbService;
import com.huoli.ctar.tmctfs.basic.srd.service.flight.ticket.domestic.db.DftCorpReconciliationOrderDetailConsRecDbService;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrder;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrderConsRec;
import com.huoli.ctar.tmctfs.beans.srd.entity.flight.ticket.domestic.DftCorpReconciliationOrderDetailConsRec;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.FlightTicketCorpReconciliationOrderQueryParam;
import com.huoli.ctar.tmctfs.beans.srd.vo.std.flight.ticket.domestic.DftCorpReconciliationOrderStdVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("tmcDftCorpReconciliationOrderSustainStdDataService")
public class TmcDftCorpReconciliationOrderSustainStdDataServiceImpl
        extends DftCorpReconciliationOrderSustainStdDataService {

    @Autowired
    @Qualifier("dftCorpReconciliationOrderDbService")
    private DftCorpReconciliationOrderDbService dftCorpReconciliationOrderDbService;

    @Autowired
    @Qualifier("dftCorpReconciliationOrderConsRecDbService")
    private DftCorpReconciliationOrderConsRecDbService dftCorpReconciliationOrderConsRecDbService;

    @Autowired
    @Qualifier("dftCorpReconciliationOrderDetailConsRecDbService")
    private DftCorpReconciliationOrderDetailConsRecDbService dftCorpReconciliationOrderDetailConsRecDbService;

    @Autowired
    @Qualifier("tmcDftCorpReconciliationOrderStdDataService")
    private CorpReconciliationOrderStdDataService<DftCorpReconciliationOrderStdVO, FlightTicketCorpReconciliationOrderQueryParam> tmcDftCorpReconciliationOrderStdDataService;

    @Override
    protected void persistReconciliationOrder(DftCorpReconciliationOrder newEntity) {
        dftCorpReconciliationOrderDbService.dbInsert(newEntity);
    }

    @Override
    protected int doUpdateReconciliationOrder(DftCorpReconciliationOrder example) {
        return dftCorpReconciliationOrderDbService.dbUpdate(example);
    }

    @Override
    protected int deleteCorpReconciliationOrders(Map<String, Object> deleteParams) {
        return dftCorpReconciliationOrderDbService.deleteByParams(deleteParams);
    }

    @Override
    protected List<DftCorpReconciliationOrderStdVO> getExistingReconciliationOrders(final String orderId) {
        return tmcDftCorpReconciliationOrderStdDataService.getReconciliationOrders(orderId, buildQueryCfg());
    }

    @Override
    protected DftCorpReconciliationOrderStdVO getExistingReconciliationOrder(Long reconciliationOrderId) {
        return tmcDftCorpReconciliationOrderStdDataService.getReconciliationOrder(reconciliationOrderId, buildQueryCfg());
    }

    @Override
    protected void persistCorpReconciliationOrderConsRecs(List<DftCorpReconciliationOrderConsRec> reconciliationOrderConsRecs) {
        dftCorpReconciliationOrderConsRecDbService.batchInsert(reconciliationOrderConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderConsRecs(Map<String, Object> deleteParams) {
        return dftCorpReconciliationOrderConsRecDbService.deleteByParams(deleteParams);
    }

    @Override
    protected void persistCorpReconciliationOrderDetailConsRecs(List<DftCorpReconciliationOrderDetailConsRec> reconciliationOrderDetailConsRecs) {
        dftCorpReconciliationOrderDetailConsRecDbService.batchInsert(reconciliationOrderDetailConsRecs);
    }

    @Override
    protected int deleteCorpReconciliationOrderDetailConsRecs(Map<String, Object> deleteParams) {
        return dftCorpReconciliationOrderDetailConsRecDbService.deleteByParams(deleteParams);
    }
}
