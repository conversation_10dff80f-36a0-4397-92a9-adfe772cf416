<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  
  <parent>
    <groupId>com.huoli.ctar</groupId>
    <artifactId>tmctcs-trvl-financial-data-svc</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  
  <artifactId>tmctcs-trvl-reconciliation-data-svc-impl</artifactId>
  <version>${tmctcs-module-version}</version>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>
  
  <dependencies>
    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmctcs-trvl-reconciliation-data-svc-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmctcs-trvl-consume-data-svc-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.huoli.ctar</groupId>
      <artifactId>tmccbs-tfs-basic-svc-impl</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-tx</artifactId>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>