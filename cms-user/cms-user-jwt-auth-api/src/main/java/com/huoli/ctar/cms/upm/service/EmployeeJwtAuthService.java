package com.huoli.ctar.cms.upm.service;

import com.huoli.ctar.saaa.model.UserBasicInfo;
import com.huoli.ctar.saaa.model.UserContext;

public interface EmployeeJwtAuthService {

    void resetPassword(final Long userId);

    void changePassword(final String originPwd, final String newPwd);

    UserBasicInfo loadUserBasicInfoByUserName(String username);

    UserContext loadUserContextByUsername(String username);

    UserBasicInfo login(String username, String password);
}
