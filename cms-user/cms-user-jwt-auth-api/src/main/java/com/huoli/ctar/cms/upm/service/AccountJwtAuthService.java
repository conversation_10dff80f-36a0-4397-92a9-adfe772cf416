package com.huoli.ctar.cms.upm.service;

import com.huoli.ctar.saaa.model.UserBasicInfo;
import com.huoli.ctar.saaa.model.UserContext;

public interface AccountJwtAuthService {
    void changePassword(final String originPwd, final String newPwd);
    void resetPassword(final Long userId);
    UserBasicInfo login(String username, String password);
    UserBasicInfo loadUserBasicInfoByUserName(String username);
    UserContext loadUserContextByUsername(String username);
}
