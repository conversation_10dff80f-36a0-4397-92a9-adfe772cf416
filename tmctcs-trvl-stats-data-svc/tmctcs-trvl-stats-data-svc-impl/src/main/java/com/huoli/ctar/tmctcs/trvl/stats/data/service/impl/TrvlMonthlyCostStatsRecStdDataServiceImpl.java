package com.huoli.ctar.tmctcs.trvl.stats.data.service.impl;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.core.infra.service.AbstractStdDataService;
import com.huoli.ctar.tmccbs.biz.domain.cos.cta.entity.TrvlMonthlyCostStatsRec;
import com.huoli.ctar.tmccbs.biz.domain.cos.cta.vo.std.TrvlMonthlyCostStatsRecQueryParam;
import com.huoli.ctar.tmccbs.biz.domain.cos.cta.vo.std.TrvlMonthlyCostStatsRecStdVO;
import com.huoli.ctar.tmccbs.biz.domain.vo.StdDataQueryCfg;
import com.huoli.ctar.tmccbs.cos.basic.cta.service.internal.TrvlMonthlyCostStatsRecInternalService;
import com.huoli.ctar.tmctcs.trvl.stats.data.service.TrvlMonthlyCostStatsRecStdDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("trvlMonthlyCostStatsRecStdDataService")
public class TrvlMonthlyCostStatsRecStdDataServiceImpl
        extends AbstractStdDataService
        implements TrvlMonthlyCostStatsRecStdDataService {

    @Autowired
    @Qualifier("trvlMonthlyCostStatsRecInternalService")
    private TrvlMonthlyCostStatsRecInternalService trvlMonthlyCostStatsRecInternalService;

    @Override
    public List<TrvlMonthlyCostStatsRecStdVO> findStatsRecs(final Long corpId,
                                                            final Integer statsYearFrom,
                                                            final Integer statsYearTo,
                                                            final StdDataQueryCfg queryCfg) {

        final TrvlMonthlyCostStatsRecQueryParam queryParam = new TrvlMonthlyCostStatsRecQueryParam.QueryParamBuilder()
                .corpId(corpId)
                .statsYearFrom(statsYearFrom)
                .statsYearTo(statsYearTo)
                .build();
        return queryStatsRecs(queryParam, queryCfg);
    }

    @Override
    public List<TrvlMonthlyCostStatsRecStdVO> queryStatsRecs(final Long corpId,
                                                             final Integer statsYear,
                                                             final Integer statsMonthFrom,
                                                             final Integer statsMonthTo,
                                                             final StdDataQueryCfg queryCfg) {

        final TrvlMonthlyCostStatsRecQueryParam queryParam = new TrvlMonthlyCostStatsRecQueryParam.QueryParamBuilder()
                .corpId(corpId)
                .statsYear(statsYear)
                .statsMonthFrom(statsMonthFrom)
                .statsMonthTo(statsMonthTo)
                .build();
        return queryStatsRecs(queryParam, queryCfg);
    }

    @Override
    public List<TrvlMonthlyCostStatsRecStdVO> queryStatsRecs(final Long corpId,
                                                             final Integer statsYear,
                                                             final Integer statsMonth,
                                                             final StdDataQueryCfg queryCfg) {

        final TrvlMonthlyCostStatsRecQueryParam queryParam = new TrvlMonthlyCostStatsRecQueryParam.QueryParamBuilder()
                .corpId(corpId)
                .statsYear(statsYear)
                .statsMonth(statsMonth)
                .build();
        return queryStatsRecs(queryParam, queryCfg);
    }

    @Override
    public List<TrvlMonthlyCostStatsRecStdVO> queryStatsRecs(final TrvlMonthlyCostStatsRecQueryParam queryParam,
                                                             final StdDataQueryCfg queryCfg) {

        final Map<String, Object> queryParams = queryParam.toQueryParams();
        final List<TrvlMonthlyCostStatsRec> statsRecs =
                trvlMonthlyCostStatsRecInternalService.findByParams(queryParams, Objects.isNull(queryCfg) ? null : queryCfg.getShowColumns());
        if (ListUtil.isEmpty(statsRecs)) {
            return Collections.emptyList();
        }
        return statsRecs.stream()
                .map(this::transferToStdVO)
                .collect(Collectors.toList());
    }

    public TrvlMonthlyCostStatsRecStdVO transferToStdVO(final TrvlMonthlyCostStatsRec trvlMonthlyCostStatsRec) {
        final TrvlMonthlyCostStatsRecStdVO stdVO = new TrvlMonthlyCostStatsRecStdVO();
        stdVO.setServiceId(trvlMonthlyCostStatsRec.getServiceId());
        stdVO.setSavingAmount(trvlMonthlyCostStatsRec.getSavingAmount());
        stdVO.setLosingAmount(trvlMonthlyCostStatsRec.getLosingAmount());
        stdVO.setStatsYear(trvlMonthlyCostStatsRec.getStatsYear());
        stdVO.setStatsMonth(trvlMonthlyCostStatsRec.getStatsMonth());
        stdVO.setCorpId(Objects.isNull(trvlMonthlyCostStatsRec.getCorpId()) ? null : Long.parseLong(trvlMonthlyCostStatsRec.getCorpId()));
        assembleCommonAttributes(stdVO, trvlMonthlyCostStatsRec);
        return stdVO;
    }
}
