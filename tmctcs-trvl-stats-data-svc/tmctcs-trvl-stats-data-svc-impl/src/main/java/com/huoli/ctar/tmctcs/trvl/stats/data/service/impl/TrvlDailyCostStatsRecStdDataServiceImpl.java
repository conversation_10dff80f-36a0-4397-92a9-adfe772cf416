package com.huoli.ctar.tmctcs.trvl.stats.data.service.impl;

import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.core.infra.service.AbstractStdDataService;
import com.huoli.ctar.tmccbs.biz.domain.cos.cta.entity.TrvlDailyCostStatsRec;
import com.huoli.ctar.tmccbs.biz.domain.cos.cta.vo.std.TrvlDailyCostStatsRecQueryParam;
import com.huoli.ctar.tmccbs.biz.domain.cos.cta.vo.std.TrvlDailyCostStatsRecStdVO;
import com.huoli.ctar.tmccbs.biz.domain.vo.StdDataQueryCfg;
import com.huoli.ctar.tmccbs.cos.basic.cta.service.internal.TrvlDailyCostStatsRecInternalService;
import com.huoli.ctar.tmctcs.trvl.stats.data.service.TrvlDailyCostStatsRecStdDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("trvlDailyCostStatsRecStdDataService")
public class TrvlDailyCostStatsRecStdDataServiceImpl
        extends AbstractStdDataService
        implements TrvlDailyCostStatsRecStdDataService {

    @Autowired
    @Qualifier("trvlDailyCostStatsRecInternalService")
    private TrvlDailyCostStatsRecInternalService trvlDailyCostStatsRecInternalService;

    @Override
    public List<TrvlDailyCostStatsRecStdVO> queryStatsRecs(final Long corpId,
                                                           final Long statsDateFrom,
                                                           final Long statsDateTo,
                                                           final StdDataQueryCfg queryCfg) {

        final TrvlDailyCostStatsRecQueryParam queryParam = new TrvlDailyCostStatsRecQueryParam.QueryParamBuilder()
                .corpId(corpId)
                .statsDateFrom(statsDateFrom)
                .statsDateTo(statsDateTo)
                .build();
        return queryStatsRecs(queryParam, queryCfg);
    }

    @Override
    public List<TrvlDailyCostStatsRecStdVO> queryStatsRecs(final TrvlDailyCostStatsRecQueryParam queryParam,
                                                           final StdDataQueryCfg queryCfg) {

        final Map<String, Object> queryParams = queryParam.toQueryParams();
        final List<TrvlDailyCostStatsRec> statsRecs =
                trvlDailyCostStatsRecInternalService.findByParams(queryParams, Objects.isNull(queryCfg) ? null : queryCfg.getShowColumns());
        if (ListUtil.isEmpty(statsRecs)) {
            return Collections.emptyList();
        }
        return statsRecs.stream()
                .map(this::transferToStdVO)
                .collect(Collectors.toList());
    }

    public TrvlDailyCostStatsRecStdVO transferToStdVO(final TrvlDailyCostStatsRec trvlDailyCostStatsRec) {
        final TrvlDailyCostStatsRecStdVO stdVO = new TrvlDailyCostStatsRecStdVO();

        stdVO.setServiceId(trvlDailyCostStatsRec.getServiceId());
        stdVO.setSavingAmount(trvlDailyCostStatsRec.getSavingAmount());
        stdVO.setLosingAmount(trvlDailyCostStatsRec.getLosingAmount());
        stdVO.setStatsDate(Objects.isNull(trvlDailyCostStatsRec.getStatsDate()) ? null : trvlDailyCostStatsRec.getStatsDate().getTime());
        stdVO.setCorpId(Objects.isNull(trvlDailyCostStatsRec.getCorpId()) ? null : Long.parseLong(trvlDailyCostStatsRec.getCorpId()));

        assembleCommonAttributes(stdVO, trvlDailyCostStatsRec);
        return stdVO;
    }
}
