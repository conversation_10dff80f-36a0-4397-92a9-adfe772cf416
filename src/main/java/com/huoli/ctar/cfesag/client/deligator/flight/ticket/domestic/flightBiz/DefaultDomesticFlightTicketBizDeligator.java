package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.book.model.DftOrderSubmitParam;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.flightBiz.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

public class DefaultDomesticFlightTicketBizDeligator extends BaseDeligator implements DomesticFlightTicketBizDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultDomesticFlightTicketBizDeligator.class);

    public DefaultDomesticFlightTicketBizDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public FlightBizTicketInfo queryTicketStatus(final String orderId, final String ticketNo) {
        final String url = String.format("%s/external/sys/api/dft/flight-biz/orders/%s/ticketNos/%s/query", this.getCfesagHost(), orderId, ticketNo);
        ResponseEntity<BaseResult<FlightBizTicketInfo>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<FlightBizTicketInfo>>() {});
        final BaseResult<FlightBizTicketInfo> result = checkResponse(response);
        return result.getData();
    }

    public List<DftChannelInfo> queryChannelInfo(){
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/filghtBiz/queryCabinDetail", this.getCfesagHost());
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity<DftOrderSubmitParam> httpEntity = new HttpEntity<>(null, httpHeaders);
        ResponseEntity<BaseResult<List<DftChannelInfo>>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<List<DftChannelInfo>>>() {
        });
        // 票务 0：成功
        if(response.getBody().getCode() == 0){
            response.getBody().setCode(1);
        }
        final BaseResult<List<DftChannelInfo>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<DftChannelInfo> queryChannelInfos() {
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/filghtBiz/channel/query", this.getCfesagHost());
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity<DftChannelInfoParams> httpEntity = new HttpEntity<>(null, httpHeaders);
        ResponseEntity<BaseResult<DftChannelInfoResult>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<DftChannelInfoResult>>() {
        });
        // 票务 0：成功
        if(response.getBody().getCode() == 0){
            response.getBody().setCode(1);
        }
        final BaseResult<DftChannelInfoResult> result = checkResponse(response);
        return result.getData().getChannelConfigs();
    }

    @Override
    public TicketValidResult ticketValid(final TicketValidParam param){
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/filghtBiz/ticket/valid", this.getCfesagHost());
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity<TicketValidParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        ResponseEntity<BaseResult<TicketValidResult>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<TicketValidResult>>() {
        });
        // 票务 0：成功
        if(response.getBody().getCode() == 0){
            response.getBody().setCode(1);
        }
        final BaseResult<TicketValidResult> result = checkResponse(response);
        return result.getData();
    }
}
