package com.huoli.ctar.cfesag.client.deligator.vetech.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.vetech.VetechDeligator;
import com.huoli.ctar.cfesag.external.sys.api.vetech.model.VetechOrder;
import com.huoli.ctar.cfesag.external.sys.api.vetech.model.VetechResult;
import com.huoli.ctar.cfesag.external.sys.api.vetech.model.VetechSyncInvoiceParam;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Slf4j
public class DefaultVetechDeligator extends BaseDeligator implements VetechDeligator {

    public DefaultVetechDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String pushOrderInfo(final List<VetechOrder> orders) {
        final HttpHeaders headers = constructCommonHeaders();

        final HttpEntity<List<VetechOrder>> httpEntity = new HttpEntity<>(orders, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/vetech/orders/push");
        final ResponseEntity<BaseResult<String>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> baseResult = checkResponse(result);
        return baseResult.getData();
    }

    /**
     * 同步发票
     * @param param
     * @return
     */
    @Override
    public VetechResult syncInvoice(VetechSyncInvoiceParam param) {
        final HttpHeaders headers = constructCommonHeaders();
        final HttpEntity<VetechSyncInvoiceParam> httpEntity = new HttpEntity<>(param, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/vetech/settleBill/syncInvoice");
        final ResponseEntity<BaseResult<VetechResult>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<VetechResult>>() {
        }, false);
        final BaseResult<VetechResult> baseResult = checkResponse(result);
        return baseResult.getData();
    }


}
