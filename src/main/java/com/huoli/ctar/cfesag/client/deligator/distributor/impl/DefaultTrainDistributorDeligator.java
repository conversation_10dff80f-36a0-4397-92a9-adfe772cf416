package com.huoli.ctar.cfesag.client.deligator.distributor.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.distributor.TrainDistributorDeligator;
import com.huoli.ctar.cfesag.external.sys.api.distributor.model.*;
import com.huoli.ctar.cfesag.external.sys.api.distributor.vo.*;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.util.TraceContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

@Slf4j
public class DefaultTrainDistributorDeligator extends BaseDeligator implements TrainDistributorDeligator {

    public DefaultTrainDistributorDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost,log);
    }


    @Override
    public TrainOccupyingASeatResult occupyingASeat(TrainOccupyingASeatParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        HttpEntity<TrainOccupyingASeatParam> httpEntity = new HttpEntity<>(param, headers);
        String url = String.format("%s/external/sys/api/train/distributor/occupyingASeat", this.getCfesagHost());
        ResponseEntity<BaseResult<TrainOccupyingASeatResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TrainOccupyingASeatResult>>() {
        });
        final BaseResult<TrainOccupyingASeatResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public TrainConfirmTicketResult confirmTicket(TrainConfirmTicketParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        HttpEntity<TrainConfirmTicketParam> httpEntity = new HttpEntity<>(param, headers);

        String url = String.format("%s/external/sys/api/train/distributor/confirmTicket", this.getCfesagHost());
        ResponseEntity<BaseResult<TrainConfirmTicketResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TrainConfirmTicketResult>>() {
        });
        final BaseResult<TrainConfirmTicketResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public TrainCancelOrderResult cancelOrder(TrainDistributorCancelOrderParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        HttpEntity<TrainDistributorCancelOrderParam> httpEntity = new HttpEntity<>(param, headers);

        String url = String.format("%s/external/sys/api/train/distributor/cancelOrder", this.getCfesagHost());
        ResponseEntity<BaseResult<TrainCancelOrderResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TrainCancelOrderResult>>() {
        });
        final BaseResult<TrainCancelOrderResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public TrainRefundTicketResult refundTicket(TrainDistributorRefundTicketParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        HttpEntity<TrainDistributorRefundTicketParam> httpEntity = new HttpEntity<>(param, headers);

        String url = String.format("%s/external/sys/api/train/distributor/refundTicket", this.getCfesagHost());
        ResponseEntity<BaseResult<TrainRefundTicketResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TrainRefundTicketResult>>() {
        });
        final BaseResult<TrainRefundTicketResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public TrainChangeTicketResult changeTicket(TrainChangeTicketParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        HttpEntity<TrainChangeTicketParam> httpEntity = new HttpEntity<>(param, headers);

        String url = String.format("%s/external/sys/api/train/distributor/changeTicket", this.getCfesagHost());
        ResponseEntity<BaseResult<TrainChangeTicketResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TrainChangeTicketResult>>() {
        });
        final BaseResult<TrainChangeTicketResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public TrainConfirmChangeTicketResult confirmChange(TrainConfirmChangeTicketParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        HttpEntity<TrainConfirmChangeTicketParam> httpEntity = new HttpEntity<>(param, headers);

        String url = String.format("%s/external/sys/api/train/distributor/confirmChange", this.getCfesagHost());
        ResponseEntity<BaseResult<TrainConfirmChangeTicketResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TrainConfirmChangeTicketResult>>() {
        });
        final BaseResult<TrainConfirmChangeTicketResult> result = checkResponse(response);
        return result.getData();

    }

    @Override
    public TrainCancelChangeTicketResult cancelChange(TrainCancelChangeTicketParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        HttpEntity<TrainCancelChangeTicketParam> httpEntity = new HttpEntity<>(param, headers);

        String url = String.format("%s/external/sys/api/train/distributor/cancelChange", this.getCfesagHost());
        ResponseEntity<BaseResult<TrainCancelChangeTicketResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TrainCancelChangeTicketResult>>() {
        });
        final BaseResult<TrainCancelChangeTicketResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public TrainBuyTicketResult buyTicket(TrainBuyTicketParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        HttpEntity<TrainBuyTicketParam> httpEntity = new HttpEntity<>(param, headers);

        String url = String.format("%s/external/sys/api/train/distributor/buyTicket", this.getCfesagHost());
        ResponseEntity<BaseResult<TrainBuyTicketResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TrainBuyTicketResult>>() {
        });
        final BaseResult<TrainBuyTicketResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String platformCancelOrder(BaseOrderParams params) {
        return platformBaseApi(params,"cancelOrder");
    }

    @Override
    public String platformRefundTicket(BaseOrderParams params) {
        return platformBaseApi(params,"refundTicket");
    }

    @Override
    public String platformCancelChange(BaseOrderParams params) {
        return platformBaseApi(params,"cancelChange");
    }


    public String platformBaseApi(BaseOrderParams params,String endUrl){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        HttpEntity<BaseOrderParams> httpEntity = new HttpEntity<>(params, headers);

        String url = String.format("%s/external/sys/api/train/platform/%s", this.getCfesagHost(), endUrl);
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
}
