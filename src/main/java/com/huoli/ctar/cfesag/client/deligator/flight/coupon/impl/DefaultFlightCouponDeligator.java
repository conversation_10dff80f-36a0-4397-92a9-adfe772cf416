package com.huoli.ctar.cfesag.client.deligator.flight.coupon.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.coupon.FlightCouponDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.coupon.model.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

@Slf4j
public class DefaultFlightCouponDeligator extends BaseDeligator implements FlightCouponDeligator {

    public DefaultFlightCouponDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public FlightCouponListResult queryFlightCoupon(FlightCouponQueryParam param) {
        final HttpHeaders headers = constructCommonHeaders();

        final HttpEntity<FlightCouponQueryParam> httpEntity = new HttpEntity<>(param, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/flightCoupon/query");
        final ResponseEntity<BaseResult<FlightCouponListResult>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FlightCouponListResult>>() {
        });
        final BaseResult<FlightCouponListResult> baseResult = checkResponse(result);
        return baseResult.getData();
    }

    @Override
    public FlightCouponManageListResult queryFlightCouponManage(FlightCouponManageQueryParam param) {
        final HttpHeaders headers = constructCommonHeaders();

        final HttpEntity<FlightCouponManageQueryParam> httpEntity = new HttpEntity<>(param, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/flightCoupon/queryManage");
        final ResponseEntity<BaseResult<FlightCouponManageListResult>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FlightCouponManageListResult>>() {
        });
        final BaseResult<FlightCouponManageListResult> baseResult = checkResponse(result);
        return baseResult.getData();
    }

    @Override
    public String delete(FlightCouponManageDeleteParam param) {
        final HttpHeaders headers = constructCommonHeaders();

        final HttpEntity<FlightCouponManageDeleteParam> httpEntity = new HttpEntity<>(param, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/flightCoupon/delete");
        final ResponseEntity<BaseResult<String>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> baseResult = checkResponse(result);
        return baseResult.getData();
    }

    @Override
    public String upload(FlightCouponManageUploadParam param) {
        final HttpHeaders headers = constructCommonHeaders();

        final HttpEntity<FlightCouponManageUploadParam> httpEntity = new HttpEntity<>(param, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/flightCoupon/upload");
        final ResponseEntity<BaseResult<String>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> baseResult = checkResponse(result);
        return baseResult.getData();
    }
}
