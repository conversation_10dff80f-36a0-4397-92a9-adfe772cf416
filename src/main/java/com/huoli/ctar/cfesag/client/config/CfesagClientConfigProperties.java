package com.huoli.ctar.cfesag.client.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Import;

@Getter
@Setter
@Import({ DeligatorConfiguration.class })
@ConfigurationProperties(prefix = "com.huoli.ctar.cfesag")
public class CfesagClientConfigProperties {
    private String host;
}
