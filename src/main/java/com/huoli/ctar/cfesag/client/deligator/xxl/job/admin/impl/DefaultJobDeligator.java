package com.huoli.ctar.cfesag.client.deligator.xxl.job.admin.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.xxl.job.admin.JobDeligator;
import com.huoli.ctar.cfesag.external.sys.api.xxl.job.admin.model.TriggerJobRequestParams;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

@Slf4j
public class DefaultJobDeligator extends BaseDeligator implements JobDeligator {

    public DefaultJobDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }
    @Override
    public String triggerJob(TriggerJobRequestParams triggerJobRequestParams) {
        HttpHeaders headers = constructCommonHeaders();
        HttpEntity<TriggerJobRequestParams> httpEntity = new HttpEntity<>(triggerJobRequestParams, headers);
        String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/jobinfo/trigger");
        ResponseEntity<BaseResult<String>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        BaseResult<String> baseResult = checkResponse(result);
        return baseResult.getData();
    }
}
