package com.huoli.ctar.cfesag.client.deligator.flight.coupon;

import com.huoli.ctar.cfesag.external.sys.api.flight.coupon.model.*;

public interface FlightCouponDeligator {

    FlightCouponListResult queryFlightCoupon(FlightCouponQueryParam param);

    FlightCouponManageListResult queryFlightCouponManage(FlightCouponManageQueryParam param);

    String delete(FlightCouponManageDeleteParam param);

    String upload(FlightCouponManageUploadParam param);
}
