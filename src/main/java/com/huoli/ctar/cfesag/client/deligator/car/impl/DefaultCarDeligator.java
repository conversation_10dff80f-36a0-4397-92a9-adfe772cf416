package com.huoli.ctar.cfesag.client.deligator.car.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.car.CarDeligator;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.cfesag.external.sys.api.car.model.*;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CarCorpOrderRecord;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.GatewayParam;
import com.huoli.ctar.common.utils.DisplayUtil;
import com.huoli.ctar.common.utils.ObjectToMapUtils;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class DefaultCarDeligator extends BaseDeligator implements CarDeligator {

	public DefaultCarDeligator(RestTemplate restTemplate, String cfesagHost) {
		super(restTemplate, cfesagHost, log);
	}

	@Override
	public PageWrapper<List<CarOrderVO>> loadCarOrders(CarOrderQueryParams carOrderQueryParams) {
		try {
			StringBuilder urlBuilder = new StringBuilder();
			urlBuilder.append(this.getCfesagHost());
			urlBuilder.append("/external/sys/api/car/order/queryEnterpriseOrder");

			Map<String, Object> map = new HashMap<>();
			if (!StringUtils.isBlank(carOrderQueryParams.getStartTime()) && !StringUtils.isBlank(carOrderQueryParams.getEndTime())) {
				map.put("startTime", carOrderQueryParams.getStartTime());
				map.put("endTime", carOrderQueryParams.getEndTime());
			}
			if (!StringUtils.isBlank(carOrderQueryParams.getOrderId())) {
				map.put("orderId", carOrderQueryParams.getOrderId());
			}
			if (Objects.nonNull(carOrderQueryParams.getPhoneId())) {
				map.put("phoneId", carOrderQueryParams.getPhoneId());
			}
			if (!StringUtils.isBlank(carOrderQueryParams.getUserPhone())) {
				map.put("userPhone", carOrderQueryParams.getUserPhone());
			}
			if (!StringUtils.isBlank(carOrderQueryParams.getCustomPhone())) {
				map.put("customPhone", carOrderQueryParams.getCustomPhone());
			}

			if (Objects.nonNull(carOrderQueryParams.getPageIndex())) {
				map.put("pageIndex", carOrderQueryParams.getPageIndex());
			}

			if (Objects.nonNull(carOrderQueryParams.getPageSize())) {
				map.put("pageSize", carOrderQueryParams.getPageSize());
			}

			String params = buildUrlParams(map);
			if (!CollectionUtils.isEmpty(map)) {
				urlBuilder.append(String.format("?%s", params));
			}


			String url = urlBuilder.toString();
			ResponseEntity<BaseResult<PageWrapper<List<CarOrderVO>>>> response = execute(url, HttpMethod.GET, null, map, new ParameterizedTypeReference<BaseResult<PageWrapper<List<CarOrderVO>>>>() {
			});
			final BaseResult<PageWrapper<List<CarOrderVO>>> result = checkResponse(response);
			return result.getData();
		} catch (RestClientException e) {
			throw new CfesagUnavailableException(e.getMessage(), e);
		}
	}

	@Override
	public CarOrderDetailVO queryCarOrderDetailByOrderId(String orderId) {
		try {
			String url = String.format("%s%s?orderId=%s", this.getCfesagHost(), "/external/sys/api/car/order/queryOrderDetail", StringUtils.isEmpty(orderId) ? "" : orderId);
			ResponseEntity<BaseResult<CarOrderDetailVO>> exchangeResult = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<CarOrderDetailVO>>() {
			});
			BaseResult<CarOrderDetailVO> result = checkResponse(exchangeResult);
			return result.getData();
		} catch (RestClientException e) {
			throw new CfesagUnavailableException(e.getMessage(), e);
		}
	}

	@Override
	public String transferOrderId(String orderId) {
		try {
			String url = String.format("%s%s?orderId=%s", this.getCfesagHost(), "/external/sys/api/car/order/queryIdByParam", StringUtils.isEmpty(orderId) ? "" : orderId);
			ResponseEntity<BaseResult<Map<String, String>>> exchangeResult = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<Map<String, String>>>() {
			});
			BaseResult<Map<String, String>> result = checkResponse(exchangeResult);
			return result.getData() == null ? null : result.getData().get("result");
		} catch (RestClientException e) {
			throw new CfesagUnavailableException(e.getMessage(), e);
		}
	}

	@Override
	public List<CarTransFlowRecord> loadCarTransFlowRecords(final String corpId,
															final String orderId,
															final String ticketOrderId,
															final String carOrderId,
															final Long transTimeFrom,
															final Long transTimeTo) {
		ResponseEntity<BaseResult<List<CarTransFlowRecord>>> response;
		try {
			final String url = String.format(this.getCfesagHost() + "/external/sys/api/car/carTransFlowRecords?corpId=%s&orderId=%s&ticketOrderId=%s&carOrderId=%s&transTimeFrom=%s&transTimeTo=%s",
					DisplayUtil.display(corpId),
					DisplayUtil.display(orderId),
					DisplayUtil.display(ticketOrderId),
					DisplayUtil.display(carOrderId),
					Objects.isNull(transTimeFrom) ? "" : String.valueOf(transTimeFrom),
					Objects.isNull(transTimeTo) ? "" : String.valueOf(transTimeTo));
			if (log.isInfoEnabled()) {
				log.info(String.format("url: %s, httpMethod: %s",
						url, HttpMethod.GET.name()));
			}

			response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<List<CarTransFlowRecord>>>() {
			});
		} catch (RestClientException e) {
			throw new CfesagUnavailableException(e.getMessage(), e);
		}
		final BaseResult<List<CarTransFlowRecord>> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public String queryCarTmcUrl(GatewayParam param) {
		Map<String, String> queryParams = param.forGetData();
		String url = String.format("%s/external/sys/api/car/queryTmcUrl?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<String>>() {
		});
		final BaseResult<String> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public PageWrapper<List<CarRealTimeOrderVO>> queryCarRealTimeOrderInfo(CarRealTimeOrderQueryParam carRealTimeOrderQueryParam) {
		Map<String, Object> queryParams = carRealTimeOrderQueryParam.forGetData();
		String url = String.format("%s/external/sys/api/car/queryRealTimeOrderInfo?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<PageWrapper<List<CarRealTimeOrderVO>>>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<PageWrapper<List<CarRealTimeOrderVO>>>>() {
		});
		final BaseResult<PageWrapper<List<CarRealTimeOrderVO>>> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public CreateCarOrderResult createCarOrder(String postBody) {
		final HttpHeaders httpHeaders = constructCommonHeaders();
		final HttpEntity<String> httpEntity = new HttpEntity<>(postBody, httpHeaders);
		String url = String.format("%s/external/sys/api/car/createOrder", this.getCfesagHost());
		ResponseEntity<BaseResult<CreateCarOrderResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CreateCarOrderResult>>() {
		});
		final BaseResult<CreateCarOrderResult> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public CreateCarOrderStrongResult createCarOrder(CarCreateOrderParam carCreateOrderParam) {
		final HttpHeaders httpHeaders = constructCommonHeaders();
		final HttpEntity<CarCreateOrderParam> httpEntity = new HttpEntity<>(carCreateOrderParam, httpHeaders);
		String url = String.format("%s/external/sys/api/car/createProductOrder", this.getCfesagHost());
		ResponseEntity<BaseResult<CreateCarOrderStrongResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CreateCarOrderStrongResult>>() {
		});
		final BaseResult<CreateCarOrderStrongResult> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public CarUserTripsResult moreUserTrips(CarUserTripParams userTripParams) {
		Map<String, Object> queryParams = null;
		try {
			queryParams = ObjectToMapUtils.objectToMap(userTripParams);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		String url = String.format("%s/external/sys/api/car/queryTripList?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<CarUserTripsResult>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<CarUserTripsResult>>() {
		});
		final BaseResult<CarUserTripsResult> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public CarQueryMerchandiseResult queryMerchandise(CarQueryMerchandiseParam carQueryMerchandiseParam) {
		Map<String, Object> queryParams = null;
		try {
			queryParams = ObjectToMapUtils.objectToMap(carQueryMerchandiseParam);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		String url = String.format("%s/external/sys/api/car/queryMerchandise?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<CarQueryMerchandiseResult>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<CarQueryMerchandiseResult>>() {
		});
		final BaseResult<CarQueryMerchandiseResult> result = checkResponse(response);
		return result.getData();
	}


	@Override
	public CarQueryMerchandiseV3Result queryMerchandiseV3(CarQueryMerchandiseParam carQueryMerchandiseParam) {
		Map<String, Object> queryParams = null;
		try {
			queryParams = ObjectToMapUtils.objectToMap(carQueryMerchandiseParam);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		String url = String.format("%s/external/sys/api/car/queryMerchandiseV3?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<CarQueryMerchandiseV3Result>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<CarQueryMerchandiseV3Result>>() {
		});
		final BaseResult<CarQueryMerchandiseV3Result> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public CarQueryMerchandiseDetailResult queryMerchandiseDetail(CarQueryMerchandiseDetailParam carQueryMerchandiseDetailParam) {
		Map<String, Object> queryParams = null;
		try {
			queryParams = ObjectToMapUtils.objectToMap(carQueryMerchandiseDetailParam);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		String url = String.format("%s/external/sys/api/car/queryMerchandiseDetail?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<CarQueryMerchandiseDetailResult>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<CarQueryMerchandiseDetailResult>>() {
		});
		final BaseResult<CarQueryMerchandiseDetailResult> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public CarQueryMerchandisePriceDetailResult queryMerchandisePriceDetail(CarQueryMerchandisePriceDetailParam carQueryMerchandisePriceDetailParam) {
		Map<String, Object> queryParams = null;
		try {
			queryParams = ObjectToMapUtils.objectToMap(carQueryMerchandisePriceDetailParam);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		String url = String.format("%s/external/sys/api/car/queryMerchandisePriceDetail?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<CarQueryMerchandisePriceDetailResult>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<CarQueryMerchandisePriceDetailResult>>() {
		});
		final BaseResult<CarQueryMerchandisePriceDetailResult> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public PageWrapper<List<CarOrderListVO>> queryOrderList(CarQueryOrderListParam carQueryOrderListParam) {
		Map<String, Object> queryParams = null;
		try {
			queryParams = ObjectToMapUtils.objectToMap(carQueryOrderListParam);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		String url = String.format("%s/external/sys/api/car/queryOrderList?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<PageWrapper<List<CarOrderListVO>>>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<PageWrapper<List<CarOrderListVO>>>>() {
		});
		final BaseResult<PageWrapper<List<CarOrderListVO>>> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public CarQueryOrderDetailResult queryOrderDetail(CarQueryOrderDetailParam carQueryOrderDetailParam) {
		Map<String, Object> queryParams = null;
		try {
			queryParams = ObjectToMapUtils.objectToMap(carQueryOrderDetailParam);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		String url = String.format("%s/external/sys/api/car/queryOrderDetail?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<CarQueryOrderDetailResult>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<CarQueryOrderDetailResult>>() {
		});
		final BaseResult<CarQueryOrderDetailResult> result = checkResponse(response);
		return result.getData();
	}

	/*
	 * 查询专车业务方企业订单消费数据
	 */
	@Override
	public CorpOrderRecordsWrapper<CarCorpOrderRecord> queryCarCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam) {
		final HttpHeaders httpHeaders = constructCommonHeaders();
		final HttpEntity<CorpOrderRecordsQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
		final String url = String.format("%s/external/sys/api/car/corpOrderRecords/query", this.getCfesagHost());
		ResponseEntity<BaseResult<CorpOrderRecordsWrapper<CarCorpOrderRecord>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CorpOrderRecordsWrapper<CarCorpOrderRecord>>>() {
		});
		final BaseResult<CorpOrderRecordsWrapper<CarCorpOrderRecord>> result = checkResponse(response);
		return result.getData();
	}

    @Override
    public String cancelOrder(CarCancelOrderParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CarCancelOrderParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = String.format("%s/external/sys/api/car/cancelOrder", this.getCfesagHost());
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

	@Override
	public List<HLServiceVO> queryHLServices(CarQueryHLServiceParam carQueryHLServiceParam) {
		Map<String, Object> queryParams = null;
		try {
			queryParams = ObjectToMapUtils.objectToMap(carQueryHLServiceParam);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		String url = String.format("%s/external/sys/api/car/hlService?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<List<HLServiceVO>>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<List<HLServiceVO>>>() {
		});
		final BaseResult<List<HLServiceVO>> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public CarUserTripsResult queryTripList(CarTripListParam carTripListParam) {
		Map<String, Object> queryParams = null;
		try {
			queryParams = ObjectToMapUtils.objectToMap(carTripListParam);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		String url = String.format("%s/external/sys/api/car/tripList?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<CarUserTripsResult>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<CarUserTripsResult>>() {
		});
		final BaseResult<CarUserTripsResult> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public CarQueryCompleteOrderLatelyResult queryCompleteOrderLately(CarQueryCompleteOrderLatelyParam carQueryCompleteOrderLatelyParam) {
		Map<String, Object> queryParams = null;
		try {
			queryParams = ObjectToMapUtils.objectToMap(carQueryCompleteOrderLatelyParam);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		String url = String.format("%s/external/sys/api/car/queryCompleteOrderLately?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<CarQueryCompleteOrderLatelyResult>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<CarQueryCompleteOrderLatelyResult>>() {
		});
		final BaseResult<CarQueryCompleteOrderLatelyResult> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public CarQueryProductNoticeResult queryProductNotice(CarQueryProductNoticeParam carQueryProductNoticeParam) {
		Map<String, Object> queryParams = null;
		try {
			queryParams = ObjectToMapUtils.objectToMap(carQueryProductNoticeParam);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		String url = String.format("%s/external/sys/api/car/queryProductNotice?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<CarQueryProductNoticeResult>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<CarQueryProductNoticeResult>>() {
		});
		final BaseResult<CarQueryProductNoticeResult> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public CarQueryProdsResult queryCarProds(CarQueryProdsParam param){
		final HttpHeaders httpHeaders = constructCommonHeaders();
		final HttpEntity<CarQueryProdsParam> httpEntity = new HttpEntity<>(param, httpHeaders);
		final String url = String.format("%s/external/sys/api/car/queryCarProds", this.getCfesagHost());
		ResponseEntity<BaseResult<CarQueryProdsResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CarQueryProdsResult>>() {
		});
		final BaseResult<CarQueryProdsResult> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public CarBatchQueryHlServiceResult batchQueryHlService(CarBatchQueryHlServiceParam param) {
		Map<String, Object> queryParams = null;
		try {
			queryParams = ObjectToMapUtils.objectToMap(param);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		String url = String.format("%s/external/sys/api/car/batchQueryHlService?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<CarBatchQueryHlServiceResult>> response = execute(url, HttpMethod.GET, null, queryParams, new ParameterizedTypeReference<BaseResult<CarBatchQueryHlServiceResult>>() {
		});
		final BaseResult<CarBatchQueryHlServiceResult> result = checkResponse(response);
		return result.getData();
	}


	@Override
	public CarCitylResult queryCity(CarCitylParam param){
		final HttpHeaders httpHeaders = constructCommonHeaders();
		final HttpEntity<CarCitylParam> httpEntity = new HttpEntity<>(param, httpHeaders);
		final String url = String.format("%s/external/sys/api/car/queryCity", this.getCfesagHost());
		ResponseEntity<BaseResult<CarCitylResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CarCitylResult>>() {
		});
		final BaseResult<CarCitylResult> result = checkResponse(response);
		return result.getData();
	}

	@Override
	public CarOrderInfoResult queryOrderInfo(CarQueryOrderDetailParam carQueryOrderDetailParam) {
		Map<String, Object> queryParams = null;
		try {
			queryParams = ObjectToMapUtils.objectToMap(carQueryOrderDetailParam);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		String url = String.format("%s/external/sys/api/car/queryOrderInfo?%s", this.getCfesagHost(), buildUrlParams(queryParams));
		ResponseEntity<BaseResult<CarOrderInfoResult>> response = execute(url, HttpMethod.POST, null, queryParams, new ParameterizedTypeReference<BaseResult<CarOrderInfoResult>>() {
		});
		final BaseResult<CarOrderInfoResult> result = checkResponse(response);
		return result.getData();
	}

	@Override
    public String createShortUrl(ShortUrlParam param){
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ShortUrlParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = String.format("%s/external/sys/api/car/create/url", this.getCfesagHost());
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
}
