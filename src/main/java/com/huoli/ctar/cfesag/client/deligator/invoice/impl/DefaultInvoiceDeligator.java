package com.huoli.ctar.cfesag.client.deligator.invoice.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.invoice.InvoiceDeligator;
import com.huoli.ctar.cfesag.external.sys.api.invoice.model.*;
import com.huoli.ctar.cfesag.external.sys.api.post.service.model.ItineraryCancelParam;
import com.huoli.ctar.cfesag.external.sys.api.post.service.model.ItineraryCancelResult;
import com.huoli.ctar.cfesag.external.sys.api.post.service.model.PostConfirmParam;
import com.huoli.ctar.cfesag.external.sys.api.post.service.model.TicketConfirmResult;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class DefaultInvoiceDeligator extends BaseDeligator implements InvoiceDeligator {

    public DefaultInvoiceDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public InvoiceBatchDto invoiceSaves(InvoiceBatchRequest invoiceBatchRequest) {
        HttpHeaders headers = constructCommonHeaders();
        HttpEntity<InvoiceBatchRequest> httpEntity = new HttpEntity<>(invoiceBatchRequest, headers);
        String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/invoice/biz-support/invoice/saves");
        ResponseEntity<BaseResult<InvoiceBatchDto>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<InvoiceBatchDto>>() {
        });
        BaseResult<InvoiceBatchDto> baseResult = checkResponse(result);
        return baseResult.getData();
    }

    @Override
    public InvoiceBatchDto.InvoiceDto queryInvoiceDetail(Long phoneId, Long invoiceId, Integer source) {
        Map<String, Object> map = new HashMap<>();
        if (Objects.nonNull(phoneId)) {
            map.put("phoneId", phoneId);
        }
        if (Objects.nonNull(invoiceId)) {
            map.put("invoiceId", invoiceId);
        }

        if (Objects.nonNull(source)) {
            map.put("source", source);
        }

        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append(this.getCfesagHost());
        urlBuilder.append("/external/sys/api/invoice/biz-support/invoice/queryDetail");

        String params = buildUrlParams(map);
        if (!CollectionUtils.isEmpty(map)) {
            urlBuilder.append(String.format("?%s", params));
        }


        String url = urlBuilder.toString();
        ResponseEntity<BaseResult<InvoiceBatchDto.InvoiceDto>> response = execute(url, HttpMethod.GET, null, map, new ParameterizedTypeReference<BaseResult<InvoiceBatchDto.InvoiceDto>>() {
        });
        final BaseResult<InvoiceBatchDto.InvoiceDto> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String cancelInvoice(Long invoiceId) {
        String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/invoice/biz-support/invoice/" + invoiceId + "/cancel");

        ResponseEntity<BaseResult<String>> result = execute(url, HttpMethod.POST, null, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        BaseResult<String> baseResult = checkResponse(result);
        return baseResult.getData();
    }

    @Override
    public List<EleInvoDto> queryInvoice(QueryInvoiceRequest queryInvoiceRequest) {
        Map<String, Object> map = new HashMap<>();
        if (Objects.nonNull(queryInvoiceRequest.getId())) {
            map.put("invoiceId", queryInvoiceRequest.getId());
        }
        if (Objects.nonNull(queryInvoiceRequest.getPhoneid())) {
            map.put("phoneId", queryInvoiceRequest.getPhoneid());
        }
        if (Objects.nonNull(queryInvoiceRequest.getEmail())) {
            map.put("email", queryInvoiceRequest.getEmail());
        }
        if (Objects.nonNull(queryInvoiceRequest.getOrderId())) {
            map.put("orderId", queryInvoiceRequest.getOrderId());
        }
        if (Objects.nonNull(queryInvoiceRequest.getInvoiceItemCode())) {
            map.put("invoiceItemCode", queryInvoiceRequest.getInvoiceItemCode());
        }

        if (Objects.nonNull(queryInvoiceRequest.getSource())) {
            map.put("source", queryInvoiceRequest.getSource());
        }

        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append(this.getCfesagHost());
        urlBuilder.append("/external/sys/api/invoice/biz-support/invoice/queryInvoice");

        String params = buildUrlParams(map);
        if (!CollectionUtils.isEmpty(map)) {
            urlBuilder.append(String.format("?%s", params));
        }


        String url = urlBuilder.toString();
        ResponseEntity<BaseResult<List<EleInvoDto>>> response = execute(url, HttpMethod.GET, null, map, new ParameterizedTypeReference<BaseResult<List<EleInvoDto>>>() {
        });

        final BaseResult<List<EleInvoDto>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public TicketConfirmResult itinerarySaves(PostConfirmParam confirmParam) {
        HttpHeaders headers = constructCommonHeaders();
        HttpEntity<PostConfirmParam> httpEntity = new HttpEntity<>(confirmParam, headers);
        String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/post/service/corp/electronic-trip/apply-confirm");
        ResponseEntity<BaseResult<TicketConfirmResult>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TicketConfirmResult>>() {
        });
        BaseResult<TicketConfirmResult> baseResult = checkResponse(result);
        return baseResult.getData();
    }

    @Override
    public ItineraryCancelResult cancelItinerary(ItineraryCancelParam cancelParam) {
        HttpHeaders headers = constructCommonHeaders();
        HttpEntity<ItineraryCancelParam> httpEntity = new HttpEntity<>(cancelParam, headers);
        String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/post/service/back-side/reversal/electronic-trip");
        ResponseEntity<BaseResult<ItineraryCancelResult>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<ItineraryCancelResult>>() {
        });
        BaseResult<ItineraryCancelResult> baseResult = checkResponse(result);
        return baseResult.getData();
    }

    @Override
    public String getItineraryFileUrl(String name, String detailId, String fileType) {
        HttpHeaders headers = constructCommonHeaders();
        Map<String, String> map = new HashMap<>();
        if(Objects.nonNull(name)) {
            map.put("name", name);
        }
        if(Objects.nonNull(detailId)) {
            map.put("detailId", detailId);
        }
        if(Objects.nonNull(fileType)) {
            map.put("fileType", fileType);
        }
        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(map, headers);
        String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/post/service/back-side/electronic-trip/download");
        ResponseEntity<BaseResult<String>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        BaseResult<String> baseResult = checkResponse(result);
        return baseResult.getData();
    }

    @Override
    public InvoiceInsConfirmResult insuranceSaves(InvoiceInsConfirmParam param) {
        HttpHeaders headers = constructCommonHeaders();
        HttpEntity<InvoiceInsConfirmParam> httpEntity = new HttpEntity<>(param, headers);
        String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/invoice/insurance/saves");
        ResponseEntity<BaseResult<InvoiceInsConfirmResult>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<InvoiceInsConfirmResult>>() {
        });
        BaseResult<InvoiceInsConfirmResult> baseResult = checkResponse(result);
        return baseResult.getData();
    }

    @Override
    public InvoiceInsQueryResult insuranceQuery(String orderId) {
        HttpHeaders headers = constructCommonHeaders();
        Map<String, String> map = new HashMap<>();
        map.put("orderId", orderId);
        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(map, headers);
        String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/invoice/insurance/query");
        ResponseEntity<BaseResult<InvoiceInsQueryResult>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<InvoiceInsQueryResult>>() {
        });
        BaseResult<InvoiceInsQueryResult> baseResult = checkResponse(result);
        return baseResult.getData();
    }
}
