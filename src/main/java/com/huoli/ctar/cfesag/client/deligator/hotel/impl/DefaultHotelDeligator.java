package com.huoli.ctar.cfesag.client.deligator.hotel.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.hotel.HotelDeligator;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.HotelCorpOrderRecord;
import com.huoli.ctar.cfesag.external.sys.api.common.model.TmcOrderFormDataParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.HotelResaleOrderConsumption;
import com.huoli.ctar.cfesag.external.sys.api.hotel.model.*;
import com.huoli.ctar.common.utils.DisplayUtil;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;
import com.huoli.ctar.core.infra.util.TraceContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Slf4j
public class DefaultHotelDeligator extends BaseDeligator implements HotelDeligator {
    //cityId目前只有4位
    private static final String HOTEL_CITIES_CACHE_KEY = "tmc.deligator.hotel.cities";
    private static final String HOTEL_CITY_MAP_CACHE_KEY = "tmc.deligator.hotel.city.map";

    //酒店城市(cityId4位和8位)
    private static final String TMC_ALL_HOTEL_CITIES_CACHE_KEY = "tmc.all.deligator.hotel.cities";
    private static final String TMC_ALL_HOTEL_CITY_MAP_CACHE_KEY = "tmc.all.deligator.hotel.city.map";

    private static final String TMC_HOTEL_PROVINCES_CACHE_KEY = "tmc.deligator.hotel.provinces";

    public DefaultHotelDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public List<HotelProvinceDto> loadHotelCityList() {
        return loadHotelCityList(null);
    }

    @Override
    public List<HotelProvinceDto> loadTmcHotelCityList() {
        return loadHotelCityList(GlobalConstant.ALL);
    }

    @Override
    public HotelProvinceVO loadHotelProvinces() {
        final String hotelProvincesUniqueKey = TMC_HOTEL_PROVINCES_CACHE_KEY;
        HotelProvinceVO hotelProvince = (HotelProvinceVO)getCacheValue(hotelProvincesUniqueKey);
        if (Objects.isNull(hotelProvince)) {
            synchronized (hotelProvincesUniqueKey) {
                hotelProvince = (HotelProvinceVO) getCacheValue(hotelProvincesUniqueKey);
                if (Objects.isNull(hotelProvince)) {
                    String url = String.format("%s%s?st=36", this.getCfesagHost(), "/external/sys/api/hotel/province/menu");
                    ResponseEntity<BaseResult<HotelProvinceVO>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<HotelProvinceVO>>() {
                    });
                    BaseResult<HotelProvinceVO> result = checkResponse(response);
                    hotelProvince = Objects.isNull(result.getData()) ? null : result.getData();
                    cacheValue(hotelProvincesUniqueKey, hotelProvince, 4 * 60 * 60);
                }
            }
        }
        return hotelProvince;
    }

    private List<HotelProvinceDto> loadHotelCityList(final String all) {
        final String hotelCityUniqueKey = StringUtils.isNotBlank(all) && StringUtils.equals(GlobalConstant.ALL, all) ? TMC_ALL_HOTEL_CITIES_CACHE_KEY : HOTEL_CITIES_CACHE_KEY;
        final String hotelCityUrl = StringUtils.isNotBlank(all) && StringUtils.equals(GlobalConstant.ALL, all) ? "/external/sys/api/hotel/tmc/menu" : "/external/sys/api/hotel/menu";
        List<HotelProvinceDto> cities = (List<HotelProvinceDto>) getCacheValue(hotelCityUniqueKey);
        if (Objects.isNull(cities)) {
            synchronized (hotelCityUniqueKey) {
                cities = (List<HotelProvinceDto>) getCacheValue(hotelCityUniqueKey);
                if (Objects.isNull(cities)) {
                    String url = String.format("%s%s?st=13", this.getCfesagHost(), hotelCityUrl);
                    ResponseEntity<BaseResult<List<HotelProvinceDto>>> exchangeResult = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<List<HotelProvinceDto>>>() {
                    }, false);
                    BaseResult<List<HotelProvinceDto>> result = checkResponse(exchangeResult);
                    cities = CollectionUtils.isEmpty(result.getData()) ? Collections.emptyList() : result.getData();
                    cacheValue(hotelCityUniqueKey, cities, 4 * 60 * 60);
                }
            }
        }
        return cities;
    }

    @Override
    public HotelCityDto queryHotelCityByCityName(String cityName) {
        return queryHotelCityByCityName(cityName, null);
    }

    private HotelCityDto queryHotelCityByCityName(String cityName, final String all) {
        if (StringUtils.isBlank(cityName)) {
            return null;
        }
        final String hotelCityUniqueKey = StringUtils.isNotBlank(all) && StringUtils.equals(all, GlobalConstant.ALL) ? TMC_ALL_HOTEL_CITY_MAP_CACHE_KEY : HOTEL_CITY_MAP_CACHE_KEY;

        Map<String, HotelCityDto> cityMap = (Map<String, HotelCityDto>) getCacheValue(hotelCityUniqueKey);
        if (Objects.isNull(cityMap)) {
            List<HotelProvinceDto> provinceDtos = StringUtils.isNotBlank(all) && StringUtils.equals(all, GlobalConstant.ALL) ? loadTmcHotelCityList() : loadHotelCityList();
            if (CollectionUtils.isEmpty(provinceDtos)) {
                return null;
            }
            synchronized (hotelCityUniqueKey) {
                cityMap = (Map<String, HotelCityDto>) getCacheValue(hotelCityUniqueKey);
                if (Objects.isNull(cityMap)) {
                    Map<String, HotelCityDto> tmpCityMap = new HashMap<>();
                    provinceDtos.forEach(provinceDto -> {
                        if (CollectionUtils.isEmpty(provinceDto.getCitys())) {
                            return;
                        }
                        provinceDto.getCitys().forEach(city -> {
                            if (StringUtils.isNotBlank(all) && StringUtils.equals(all, GlobalConstant.ALL)) {
                                tmpCityMap.put(StringUtils.substringBefore(city.getCityName(), "("), city);
                            } else {
                                tmpCityMap.put(city.getCityName(), city);
                            }
                        });
                    });
                    cityMap = tmpCityMap;
                    cacheValue(hotelCityUniqueKey, cityMap, 4 * 60 * 60);
                }
            }
        }
        cityName = cityName.trim();
        return CollectionUtils.isEmpty(cityMap) ? null : cityMap.get(cityName);
    }


    @Override
    public HotelCityDto queryHotelCityByCityNameFuzzyMatching(String cityName) {
        if (StringUtils.isBlank(cityName)) {
            return null;
        }
        cityName = cityName.trim();
        HotelCityDto city = queryHotelCityByCityName(cityName);
        if (Objects.nonNull(city)) {
            return city;
        }

        if (cityName.endsWith("市")) {
            return queryHotelCityByCityName(cityName.substring(0, cityName.length() - 1));
        } else {
            return queryHotelCityByCityName(String.format("%s市", cityName));
        }
    }

    @Override
    public HotelCityDto queryTmcHotelCityByCityNameFuzzyMatching(String cityName) {
        if (StringUtils.isBlank(cityName)) {
            return null;
        }
        cityName = cityName.trim();
        HotelCityDto city = queryHotelCityByCityName(cityName, GlobalConstant.ALL);
        if (Objects.nonNull(city)) {
            return city;
        }

        if (cityName.endsWith("市")) {
            return queryHotelCityByCityName(cityName.substring(0, cityName.length() - 1), GlobalConstant.ALL);
        } else {
            return queryHotelCityByCityName(String.format("%s市", cityName), GlobalConstant.ALL);
        }
    }

    @Override
    public HotelOrderDetailVO queryHotelOrderDetailByOrderId(String orderId) {
        String url = String.format("%s%s?hotelOrderId=%s", this.getCfesagHost(), "/external/sys/api/hotel/hljd_order_new/rest/newHotelOrder/queryOrderById", StringUtils.isEmpty(orderId) ? "" : orderId);
        ResponseEntity<BaseResult<HotelOrderDetailVO>> exchangeResult = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<HotelOrderDetailVO>>() {
        });

        BaseResult<HotelOrderDetailVO> result = checkResponse(exchangeResult);
        return result.getData();
    }

    @Override
    public List<HotelTransFlowRecord> loadHotelTransFlowRecords(final String corpId,
                                                                final String orderId,
                                                                final Long transTimeFrom,
                                                                final Long transTimeTo) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/hotel/hotelTransFlowRecords?corpId=%s&orderId=%s&transTimeFrom=%s&transTimeTo=%s",
                DisplayUtil.display(corpId),
                DisplayUtil.display(orderId),
                Objects.isNull(transTimeFrom) ? "" : String.valueOf(transTimeFrom),
                Objects.isNull(transTimeTo) ? "" : String.valueOf(transTimeTo));
        ResponseEntity<BaseResult<List<HotelTransFlowRecord>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<List<HotelTransFlowRecord>>>() {
        });
        final BaseResult<List<HotelTransFlowRecord>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String queryHotelTmcUrl(HotelHandleUrlQueryParam param) {
        Map<String, String> paramMap = param.forGetData();
        String url = String.format("%s/external/sys/api/hotel/queryTmcUrl?%s", this.getCfesagHost(), buildUrlParams(paramMap));
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.GET, null, paramMap, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<HotelRealTimeOrderDetailVO>> queryHotelRealTimeOrderInfo(HotelRealTimeOrderQueryParam queryParam) {
        Map<String, Object> queryParamMap = queryParam.forGetData();
        String url = String.format("%s/external/sys/api/hotel/queryRealTimeOrderInfo?%s", this.getCfesagHost(), buildUrlParams(queryParamMap));
        ResponseEntity<BaseResult<PageWrapper<List<HotelRealTimeOrderDetailVO>>>> response = execute(url, HttpMethod.GET, null, queryParamMap, new ParameterizedTypeReference<BaseResult<PageWrapper<List<HotelRealTimeOrderDetailVO>>>>() {
        });
        final BaseResult<PageWrapper<List<HotelRealTimeOrderDetailVO>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CreateHotelResult createHotelOrder(String orderFormData) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("orderFormData", orderFormData);

        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(params, headers);

        String url = String.format("%s/external/sys/api/hotel/createOrder", this.getCfesagHost());
        ResponseEntity<BaseResult<CreateHotelResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CreateHotelResult>>() {
        });
        final BaseResult<CreateHotelResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CreateHotelResult tmcCreateHotelOrder(String orderFormData) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("orderFormData", orderFormData);

        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(params, headers);

        String url = String.format("%s/external/sys/api/hotel/tmcCreateOrder", this.getCfesagHost());
        ResponseEntity<BaseResult<CreateHotelResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CreateHotelResult>>() {
        });
        final BaseResult<CreateHotelResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public HotelOrderListVO queryOrderList(HotelOrderListQueryParam param) {
        Map<String, Object> queryParamMap = param.forGetData();
        String url = String.format("%s/external/sys/api/hotel/hljd_order_new/rest/newHotelOrder/queryOrderList?%s", this.getCfesagHost(), buildUrlParams(queryParamMap));
        ResponseEntity<BaseResult<HotelOrderListVO>> response = execute(url, HttpMethod.GET, null, queryParamMap, new ParameterizedTypeReference<BaseResult<HotelOrderListVO>>() {
        });
        final BaseResult<HotelOrderListVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public OrderDetailVO handleHotelOrder(HotelOrderHandleParam param) {
        Map<String, Object> queryParamMap = param.forGetData();
        String url = String.format("%s/external/sys/api/hotel/hljd_order_new/rest/newHotelOrder/handleHotelOrder?%s", this.getCfesagHost(), buildUrlParams(queryParamMap));
        ResponseEntity<BaseResult<OrderDetailVO>> response = execute(url, HttpMethod.GET, null, queryParamMap, new ParameterizedTypeReference<BaseResult<OrderDetailVO>>() {
        });
        final BaseResult<OrderDetailVO> result = checkResponse(response);
        return result.getData();
    }

    /*
     * 查询酒店业务方企业订单消费数据
     */
    public CorpOrderRecordsWrapper<HotelCorpOrderRecord> queryHotelCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpOrderRecordsQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/hotel/corpOrderRecords/query", this.getCfesagHost());
        ResponseEntity<BaseResult<CorpOrderRecordsWrapper<HotelCorpOrderRecord>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CorpOrderRecordsWrapper<HotelCorpOrderRecord>>>() {
        });
        final BaseResult<CorpOrderRecordsWrapper<HotelCorpOrderRecord>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public BaseResult<HotelOrderRespVO> hotelTmcCreateOrder(TmcCreateOrderReq param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<TmcCreateOrderReq> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = String.format("%s/external/sys/api/hotel/hljd_order_new/rest/newHotelOrder/create", this.getCfesagHost());
        ResponseEntity<BaseResult<HotelOrderRespVO>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<HotelOrderRespVO>>() {
        });
        return checkResponseNotCheckCode(response);
    }

    @Override
    public HotelOrderSyncResp tmcSyncOrderStatus(HotelOrderSyncReq orderSyncReq) {
        Map<String, Object> queryParamMap = orderSyncReq.forGetData();
        String url = String.format("%s/external/sys/api/hotel/hljd_order_new/rest/newHotelOrder/syncOrderStatus?%s", this.getCfesagHost(), buildUrlParams(queryParamMap));
        ResponseEntity<BaseResult<HotelOrderSyncResp>> response = execute(url, HttpMethod.GET, null, queryParamMap, new ParameterizedTypeReference<BaseResult<HotelOrderSyncResp>>() {
        });
        final BaseResult<HotelOrderSyncResp> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public HotelCorpResaleOrderVO queryHotelCorpResaleOrderInfo(HotelCorpResaleOrderParam hotelCorpResaleOrderParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<HotelCorpResaleOrderParam> httpEntity = new HttpEntity<>(hotelCorpResaleOrderParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/hotel/hljd_order_new/rest/newHotelOrder/queryHotelCorpResaleOrderInfo", this.getCfesagHost());
        ResponseEntity<BaseResult<HotelCorpResaleOrderVO>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<HotelCorpResaleOrderVO>>() {
        });
        final BaseResult<HotelCorpResaleOrderVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public TmcHotelOrderFormData parseHotelOrderFormData(TmcOrderFormDataParam formDataParam) {
        final HttpHeaders headers = constructCommonHeaders();
        final HttpEntity<TmcOrderFormDataParam> httpEntity = new HttpEntity<>(formDataParam, headers);

        String url = String.format("%s/external/sys/api//hotel/orderFormData/parse", this.getCfesagHost());
        ResponseEntity<BaseResult<TmcHotelOrderFormData>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TmcHotelOrderFormData>>() {
        });
        final BaseResult<TmcHotelOrderFormData> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CorpResaleOrderRecsWrapper<HotelResaleOrderConsumption> queryHotelResaleCorpOrderRecs(CorpResaleOrderRecsQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpResaleOrderRecsQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/hotel/corpResaleOrderRecs/query", this.getCfesagHost());
        ResponseEntity<BaseResult<CorpResaleOrderRecsWrapper<HotelResaleOrderConsumption>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CorpResaleOrderRecsWrapper<HotelResaleOrderConsumption>>>() {
        });
        final BaseResult<CorpResaleOrderRecsWrapper<HotelResaleOrderConsumption>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public HotelDetailVO hotelDetail(String hotelCode) {
        final List<String> queryParams = new ArrayList<>();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(hotelCode)) {
            queryParams.add(String.format("hotelCode=%s", hotelCode));
        }

        final String url = String.format(this.getCfesagHost() + "/external/sys/api/hotel/hljd_order_new/rest/newhotel/detail?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : org.apache.commons.lang3.StringUtils.join(queryParams, "&"));
        ResponseEntity<BaseResult<HotelDetailVO>> response =
                execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<HotelDetailVO>>() {
                });
        final BaseResult<HotelDetailVO> result = checkResponse(response);
        return result.getData();
    }


    @Override
    public TmcHotelCityData tmcHotelCityData(TmcHotelCityParam tmcHotelCityParam) {
        final HttpHeaders headers = constructCommonHeaders();
        final HttpEntity<TmcHotelCityParam> httpEntity = new HttpEntity<>(tmcHotelCityParam, headers);
        String url = String.format("%s/external/sys/api/hotel/menu_tmc", this.getCfesagHost());
        ResponseEntity<BaseResult<TmcHotelCityData>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TmcHotelCityData>>() {
        });
        final BaseResult<TmcHotelCityData> result = checkResponse(response);
        return result.getData();
    }


}
