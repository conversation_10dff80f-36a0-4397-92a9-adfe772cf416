package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.InternationalFlightTicketOrderCenterDeligator;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.IftResaleOrderConsumption;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;


public class DefaultInternationalFlightTicketOrderCenterDeligator extends BaseDeligator implements InternationalFlightTicketOrderCenterDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultInternationalFlightTicketOrderCenterDeligator.class);

    public DefaultInternationalFlightTicketOrderCenterDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public CorpResaleOrderRecsWrapper<IftResaleOrderConsumption> queryIftCorpResaleOrderRecs(CorpResaleOrderRecsQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpResaleOrderRecsQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/international/flight/corpResaleOrderRecords/query", this.getCfesagHost());
        ResponseEntity<BaseResult<CorpResaleOrderRecsWrapper<IftResaleOrderConsumption>>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<CorpResaleOrderRecsWrapper<IftResaleOrderConsumption>>>() {});
        final BaseResult<CorpResaleOrderRecsWrapper<IftResaleOrderConsumption>> result = checkResponse(response);
        return result.getData();
    }
}
