package com.huoli.ctar.cfesag.client.callback;

import com.huoli.ctar.cfesag.external.sys.api.pushcenter.model.PushWeChatMessageRequest;
import com.huoli.ctar.core.infra.constant.AppName;

import java.util.List;

public interface WeChatMsgPushProviderCallback {
    String getMsgTemplateId();

    List<PushWeChatMessageRequest.MsgTemplateParam> getMsgTemplateParams();

    default AppName getAppName() {
        return AppName.HBGJ;
    }

    /**
     * h5跳转地址
     * h5和小程序跳转地址同时存在，优先小程序地址
     *
     * @return
     */
    default String getUrl() {
        return null;
    }

    /**
     * 小程序跳转地址
     * h5和小程序跳转地址同时存在，优先小程序地址
     *
     * @return
     */
    default String getRedirectUrl() {
        return null;
    }

    List<String> getReceivers();
}
