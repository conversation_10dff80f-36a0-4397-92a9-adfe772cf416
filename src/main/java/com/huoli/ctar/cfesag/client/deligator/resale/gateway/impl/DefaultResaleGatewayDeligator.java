package com.huoli.ctar.cfesag.client.deligator.resale.gateway.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.gateway.ResaleGatewayDeligator;
import com.huoli.ctar.cfesag.external.sys.api.resale.gateway.model.ApiClient;
import com.huoli.ctar.cfesag.external.sys.api.resale.gateway.model.ApiClientRegisterRequest;
import com.huoli.ctar.cfesag.external.sys.api.resale.gateway.model.ApiClientUpdateRequest;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

@Slf4j
public class DefaultResaleGatewayDeligator extends BaseDeligator implements ResaleGatewayDeligator {

    public DefaultResaleGatewayDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public ApiClient register(ApiClientRegisterRequest apiClientRegisterRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ApiClientRegisterRequest> httpEntity = new HttpEntity<>(apiClientRegisterRequest, httpHeaders);

        ResponseEntity<BaseResult<ApiClient>> response = execute(this.getCfesagHost() + "/external/sys/api/resale/gateway/oauth/register", HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<ApiClient>>() {
        });
        final BaseResult<ApiClient> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String updateApiClient(ApiClientUpdateRequest apiClientUpdateRequest, String openId) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ApiClientUpdateRequest> httpEntity = new HttpEntity<>(apiClientUpdateRequest, httpHeaders);
        ResponseEntity<BaseResult<String>> response = execute(String.format(getCfesagHost() + "/external/sys/api/resale/gateway/oauth/clients/%s", openId),
                HttpMethod.PUT, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
                });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public ApiClient getApiClientByOpenId(String openId) {
        ResponseEntity<BaseResult<ApiClient>> response = execute(String.format(getCfesagHost() + "/external/sys/api/resale/gateway/oauth/clients/%s", openId),
                HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<ApiClient>>() {
                });

        final BaseResult<ApiClient> result = checkResponse(response);
        return result.getData();
    }
}
