package com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.InternationalFlightTicketDeligator;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.IftCorpOrderRecord;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.common.model.PageFlightTicketResult;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.common.model.TmcFlightTicketCommonSearchParams;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.common.model.TmcFlightTicketOrderMobileVO;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.international.model.*;
import com.huoli.ctar.common.utils.DisplayUtil;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.List;

public class DefaultInternationalFlightTicketDeligator extends BaseDeligator implements InternationalFlightTicketDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultInternationalFlightTicketDeligator.class);

    public DefaultInternationalFlightTicketDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public PageWrapper<List<InternationalFlightTicketOrder>> loadInternationalTicketOrders(InternationalTicketOrderQueryParams internationalTicketOrderQueryParams) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<InternationalTicketOrderQueryParams> httpEntity = new HttpEntity<>(internationalTicketOrderQueryParams, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/international/flight/tob/api/internationalTicketOrders";
        ResponseEntity<BaseResult<PageWrapper<List<InternationalFlightTicketOrder>>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<InternationalFlightTicketOrder>>>>() {
        });
        final BaseResult<PageWrapper<List<InternationalFlightTicketOrder>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public InternationalFlightTicketTransFlowRecord getInternationalFlightTicketTransFlowRecord(final String orderId) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/international/flight/internationalFlightTicketTransFlowRecords/%s",
                DisplayUtil.display(orderId));

        ResponseEntity<BaseResult<InternationalFlightTicketTransFlowRecord>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<InternationalFlightTicketTransFlowRecord>>() {
        });
        final BaseResult<InternationalFlightTicketTransFlowRecord> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<InternationalFlightTicketTransFlowRecord>> getInternationalFlightTicketTransFlowList(InternationalFlightTicketTransFlowRecordQueryParam internationalFlightTicketTransFlowRecordQueryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<InternationalFlightTicketTransFlowRecordQueryParam> httpEntity = new HttpEntity<>(internationalFlightTicketTransFlowRecordQueryParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/international/flight/list/internationalFlightTicketTransFlowRecords";
        ResponseEntity<BaseResult<PageWrapper<List<InternationalFlightTicketTransFlowRecord>>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<InternationalFlightTicketTransFlowRecord>>>>() {
        });
        final BaseResult<PageWrapper<List<InternationalFlightTicketTransFlowRecord>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<InFlightTicketRealTimeOrderVO>> queryInFlightRealTimeOrderInfo(InFlightTicketRealTimeOrderQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<InFlightTicketRealTimeOrderQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/international/flight/queryRealTimeOrderInfo", this.getCfesagHost());
        ResponseEntity<BaseResult<PageWrapper<List<InFlightTicketRealTimeOrderVO>>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<InFlightTicketRealTimeOrderVO>>>>() {
        });
        final BaseResult<PageWrapper<List<InFlightTicketRealTimeOrderVO>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public InFlightTicketRealTimeOrderDetailVO queryInFlightRealTimeOrderDetail(String orderId) {
        final String url = String.format("%s/external/sys/api/international/flight/queryRealTimeOrderDetail?orderId=%s", this.getCfesagHost(), orderId);
        ResponseEntity<BaseResult<InFlightTicketRealTimeOrderDetailVO>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<InFlightTicketRealTimeOrderDetailVO>>() {
        });
        final BaseResult<InFlightTicketRealTimeOrderDetailVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<InFlightTicketRealTimeOrderReportVO>> queryInFlightRealTimeOrderInfoForReport(Long corpId, String startTime, String endTime, Integer pageNo, Integer pageSize) {
        InFlightTicketRealTimeOrderQueryParam realTimeOrderQueryParam = new InFlightTicketRealTimeOrderQueryParam();
        realTimeOrderQueryParam.setCorpId(corpId);
        realTimeOrderQueryParam.setStartTime(startTime);
        realTimeOrderQueryParam.setEndTime(endTime);
        realTimeOrderQueryParam.setPageNo(pageNo);
        realTimeOrderQueryParam.setPageSize(pageSize);

        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<InFlightTicketRealTimeOrderQueryParam> httpEntity = new HttpEntity<>(realTimeOrderQueryParam, httpHeaders);
        ResponseEntity<BaseResult<PageWrapper<List<InFlightTicketRealTimeOrderReportVO>>>> response;
        try {
            final String url = String.format("%s/external/sys/api/international/flight/queryRealTimeOrderInfoForReport", this.getCfesagHost());
            response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<InFlightTicketRealTimeOrderReportVO>>>>() {
            });
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
        final BaseResult<PageWrapper<List<InFlightTicketRealTimeOrderReportVO>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public InternationalFlightTicketOrderTripInfo queryInFlightTicketOrderTripInfo(String orderId, String payOrderId) {
        final String url = String.format("%s/external/sys/api/international/flight/queryOrderTripInfo?orderId=%s&payOrderId=%s", this.getCfesagHost(), DisplayUtil.display(orderId), DisplayUtil.display(payOrderId));
        ResponseEntity<BaseResult<InternationalFlightTicketOrderTripInfo>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<InternationalFlightTicketOrderTripInfo>>() {
        });
        final BaseResult<InternationalFlightTicketOrderTripInfo> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public InFlightTicketOrderCreateResult createInFlightTicketOrder(String postBody) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<String> httpEntity = new HttpEntity<>(postBody, httpHeaders);
        String url = String.format("%s/external/sys/api/international/flight/createOrder", this.getCfesagHost());
        ResponseEntity<BaseResult<InFlightTicketOrderCreateResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<InFlightTicketOrderCreateResult>>() {
        });
        final BaseResult<InFlightTicketOrderCreateResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public IntFlightOrderAppDetailResultVo queryAppOrderDetail(String orderId, String phoneId, String p){
        final String url = String.format("%s/external/sys/api/international/flight/queryAppOrderDetail?orderId=%s&phoneId=%s&p=%s", this.getCfesagHost(), DisplayUtil.display(orderId), DisplayUtil.display(phoneId), DisplayUtil.display(p));
        ResponseEntity<BaseResult<IntFlightOrderAppDetailResultVo>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<IntFlightOrderAppDetailResultVo>>() {
        });
        final BaseResult<IntFlightOrderAppDetailResultVo> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public IntFlightOrderStatusProcessResultVo queryIntOrderStatusProcess(String orderId, String phoneId) {
        final String url = String.format("%s/external/sys/api/international/flight/queryOrderStatusProcess?orderId=%s&phoneId=%s", this.getCfesagHost(), DisplayUtil.display(orderId), DisplayUtil.display(phoneId));
        ResponseEntity<BaseResult<IntFlightOrderStatusProcessResultVo>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<IntFlightOrderStatusProcessResultVo>>() {
        });
        final BaseResult<IntFlightOrderStatusProcessResultVo> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public BaseResult<IntFlightJourneyBookSureVO> queryJourneyBookSureOrderInfo(InFlightJourneyBookSureQueryParam queryParam) {
        final String url = String.format("%s/external/sys/api/international/flight/queryJourneyBookSureOrderInfo?%s", this.getCfesagHost(),buildUrlParams(queryParam.forGetData()));
        ResponseEntity<BaseResult<IntFlightJourneyBookSureVO>>response = execute(url, HttpMethod.GET, null,queryParam.forGetData(), new ParameterizedTypeReference<BaseResult<IntFlightJourneyBookSureVO>>() {
        });
        final BaseResult<IntFlightJourneyBookSureVO> result = checkResponse(response);
        return result;
    }

    @Override
    public IntFlightOrderPriceDetailResultVo queryOrderPriceDetail(String orderId, String phoneId) {
        final String url = String.format("%s/external/sys/api/international/flight/queryOrderPriceDetail?orderId=%s&phoneId=%s", this.getCfesagHost(), DisplayUtil.display(orderId), DisplayUtil.display(phoneId));
        ResponseEntity<BaseResult<IntFlightOrderPriceDetailResultVo>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<IntFlightOrderPriceDetailResultVo>>() {
        });
        final BaseResult<IntFlightOrderPriceDetailResultVo> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageFlightTicketResult<List<TmcFlightTicketOrderMobileVO>> queryTmcIntFlightTicketOrders(String userId,
                                                                                                    String orderId,
                                                                                                    String passengerName,
                                                                                                    String depCity,
                                                                                                    String arrCity,
                                                                                                    String flyNo,
                                                                                                    String groupType,
                                                                                                    Boolean isGetAll,
                                                                                                    Integer maxCount,
                                                                                                    Boolean isNeedPage,
                                                                                                    Integer pageSize,
                                                                                                    Integer pageNum,
                                                                                                    String p,
                                                                                                    String effectiveOrderFlag,
                                                                                                    String bizOrderFlag
    ) {
        TmcFlightTicketCommonSearchParams tmcFlightTicketCommonSearchParams = new TmcFlightTicketCommonSearchParams();
        tmcFlightTicketCommonSearchParams.setUserId(userId);
        tmcFlightTicketCommonSearchParams.setOrderId(orderId);
        tmcFlightTicketCommonSearchParams.setDepCity(depCity);
        tmcFlightTicketCommonSearchParams.setArrCity(arrCity);
        tmcFlightTicketCommonSearchParams.setPassengerName(passengerName);
        tmcFlightTicketCommonSearchParams.setFlyNo(flyNo);
        tmcFlightTicketCommonSearchParams.setGroupType(groupType);
        tmcFlightTicketCommonSearchParams.setIsGetAll(isGetAll);
        tmcFlightTicketCommonSearchParams.setMaxCount(maxCount);
        tmcFlightTicketCommonSearchParams.setIsNeedPage(isNeedPage);
        tmcFlightTicketCommonSearchParams.setPageSize(pageSize);
        tmcFlightTicketCommonSearchParams.setPageNum(pageNum);
        tmcFlightTicketCommonSearchParams.setP(p);
        tmcFlightTicketCommonSearchParams.setEffectiveOrderFlag(effectiveOrderFlag);
        tmcFlightTicketCommonSearchParams.setBizOrderFlag(bizOrderFlag);
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<TmcFlightTicketCommonSearchParams> httpEntity = new HttpEntity<>(tmcFlightTicketCommonSearchParams, httpHeaders);
        final String url = String.format("%s/external/sys/api/international/flight/tmc/flightTicketOrders", this.getCfesagHost());
        ResponseEntity<BaseResult<PageFlightTicketResult<List<TmcFlightTicketOrderMobileVO>>>> response =
                execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageFlightTicketResult<List<TmcFlightTicketOrderMobileVO>>>>() {
        });
        final BaseResult<PageFlightTicketResult<List<TmcFlightTicketOrderMobileVO>>> result = checkResponse(response);
        return result.getData();
    }

    /*
     * 查询国际机票业务方企业订单消费数据
     */
    @Override
    public CorpOrderRecordsWrapper<IftCorpOrderRecord> queryIftCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpOrderRecordsQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/international/flight/corpOrderRecords/query", this.getCfesagHost());
        ResponseEntity<BaseResult<CorpOrderRecordsWrapper<IftCorpOrderRecord>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CorpOrderRecordsWrapper<IftCorpOrderRecord>>>() {
        });
        final BaseResult<CorpOrderRecordsWrapper<IftCorpOrderRecord>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String cancelOrder(IftCancelOrderParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<IftCancelOrderParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = String.format("%s/external/sys/api/international/flight/cancelOrder", this.getCfesagHost());
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
}
