package com.huoli.ctar.cfesag.client.deligator.coupon.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.coupon.CouponDeligator;
import com.huoli.ctar.cfesag.external.sys.api.points.model.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Slf4j
public class DefaultCouponDeligator extends BaseDeligator implements CouponDeligator {

    public DefaultCouponDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public UserPointsInfoDTO loadUserAccountInfo(BonusPointsRequest pointsRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<BonusPointsRequest> httpEntity = new HttpEntity<>(pointsRequest, httpHeaders);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/coupon/loadUserAccountInfo");

        ResponseEntity<BaseResult<UserPointsInfoDTO>> exchangeResult = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<UserPointsInfoDTO>>() {}, false);
        BaseResult<UserPointsInfoDTO> result = checkResponse(exchangeResult);
        return result.getData();
    }

    @Override
    public UserPointsInfoDTO loadUserPointsInfo(BonusPointsRequest pointsRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<BonusPointsRequest> httpEntity = new HttpEntity<>(pointsRequest, httpHeaders);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/coupon/loadUserPointsInfo");

        ResponseEntity<BaseResult<UserPointsInfoDTO>> exchangeResult = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<UserPointsInfoDTO>>() {}, false);
        BaseResult<UserPointsInfoDTO> result = checkResponse(exchangeResult);
        return result.getData();
    }

    @Override
    public String consumptionPoints(ConsumptionPointsRequest pointsRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ConsumptionPointsRequest> httpEntity = new HttpEntity<>(pointsRequest, httpHeaders);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/coupon/consumptionPoints");

        ResponseEntity<BaseResult<String>> exchangeResult = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<String>>() {}, false);
        BaseResult<String> result = checkResponse(exchangeResult);
        return result.getData();
    }

    @Override
    public List<BonusCouponsInfoDTO> exchangeCoupons(ExchangeCouponsRequest exchangeCouponsRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ExchangeCouponsRequest> httpEntity = new HttpEntity<>(exchangeCouponsRequest, httpHeaders);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/coupon/exchangeCoupons");

        ResponseEntity<BaseResult<List<BonusCouponsInfoDTO>>> exchangeResult = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<List<BonusCouponsInfoDTO>>>() {}, false);
        BaseResult<List<BonusCouponsInfoDTO>> result = checkResponse(exchangeResult);
        return result.getData();
    }

    @Override
    public GtgjExchangeCouponsResult exchangeGtgjCoupons(ExchangeCouponsRequest exchangeCouponsRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ExchangeCouponsRequest> httpEntity = new HttpEntity<>(exchangeCouponsRequest, httpHeaders);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/coupon/gtgj/exchangeCoupons");

        ResponseEntity<BaseResult<GtgjExchangeCouponsResult>> exchangeResult = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<GtgjExchangeCouponsResult>>() {}, false);
        BaseResult<GtgjExchangeCouponsResult> result = checkResponse(exchangeResult);
        return result.getData();
    }

    @Override
    public UpgradeMemberMailResult upgradeMemberMail(UpgradeMemberMailRequest upgradeMemberMailRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<UpgradeMemberMailRequest> httpEntity = new HttpEntity<>(upgradeMemberMailRequest, httpHeaders);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/memberMail/upgrade");

        ResponseEntity<BaseResult<UpgradeMemberMailResult>> exchangeResult = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<UpgradeMemberMailResult>>() {}, false);
        BaseResult<UpgradeMemberMailResult> result = checkResponse(exchangeResult);
        return result.getData();
    }

    @Override
    public String giveUserBonusPoints(GiveBonusPointsRequest pointsRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<GiveBonusPointsRequest> httpEntity = new HttpEntity<>(pointsRequest, httpHeaders);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/coupon/giveUserBonusPoints");

        ResponseEntity<BaseResult<String>> exchangeResult = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<String>>() {}, false);
        BaseResult<String> result = checkResponse(exchangeResult);
        return result.getData();
    }
}
