package com.huoli.ctar.cfesag.client.deligator.pnr.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.pnr.PnrDeligator;
import com.huoli.ctar.cfesag.external.sys.api.pnr.PnrInfo;
import com.huoli.ctar.cfesag.external.sys.api.pnr.PnrInfoQueryParam;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

@Slf4j
public class DefaultPnrDeligator extends BaseDeligator implements PnrDeligator {

    public DefaultPnrDeligator(final RestTemplate restTemplate, final String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    /*
     * 查询旅游业务方企业订单消费数据
     */
    public PnrInfo queryPnrInfo(final PnrInfoQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PnrInfoQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/eterm/book/queryPnr", this.getCfesagHost());
        final ResponseEntity<BaseResult<PnrInfo>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PnrInfo>>() {
        });
        final BaseResult<PnrInfo> result = checkResponse(response);
        return result.getData();
    }
}
