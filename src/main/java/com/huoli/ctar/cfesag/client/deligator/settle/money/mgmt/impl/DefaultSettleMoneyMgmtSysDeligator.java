package com.huoli.ctar.cfesag.client.deligator.settle.money.mgmt.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.settle.money.mgmt.SettleMoneyMgmtSysDeligator;
import com.huoli.ctar.cfesag.external.sys.api.settle.money.mgmt.CorpSettleBillExternalVO;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;


public class DefaultSettleMoneyMgmtSysDeligator
        extends BaseDeligator
        implements SettleMoneyMgmtSysDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultSettleMoneyMgmtSysDeligator.class);

    public DefaultSettleMoneyMgmtSysDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public void syncCorpSettleBill(final CorpSettleBillExternalVO corpSettleBill) {
        final HttpHeaders headers = constructCommonHeaders();
        HttpEntity<CorpSettleBillExternalVO> httpEntity = new HttpEntity<>(corpSettleBill, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/settle/money/mgmt/corpSettleBills/sync");
        final ResponseEntity<BaseResult<String>> responseData = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {});
        checkResponse(responseData);
    }

    @Override
    public void removeCorpSettleBill(final Long settleBillId) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/settle/money/mgmt/corpSettleBills/%s", settleBillId);
        final ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.DELETE, null, new ParameterizedTypeReference<BaseResult<String>>() {});
        checkResponse(response);
    }
}
