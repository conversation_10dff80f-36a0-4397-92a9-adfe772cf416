package com.huoli.ctar.cfesag.client.deligator.car;

import com.huoli.ctar.cfesag.external.sys.api.car.model.*;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CarCorpOrderRecord;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.GatewayParam;
import com.huoli.ctar.core.infra.model.PageWrapper;

import java.util.List;

public interface CarDeligator {
    PageWrapper<List<CarOrderVO>> loadCarOrders(CarOrderQueryParams carOrderQueryParams);

    CarOrderDetailVO queryCarOrderDetailByOrderId(String orderId);

    String transferOrderId(String orderId);

    List<CarTransFlowRecord> loadCarTransFlowRecords(final String corpId,
                                                     final String orderId,
                                                     final String ticketOrderId,
                                                     final String carOrderId,
                                                     final Long transTimeFrom,
                                                     final Long transTimeTo);

    String queryCarTmcUrl(GatewayParam param);

    PageWrapper<List<CarRealTimeOrderVO>> queryCarRealTimeOrderInfo(CarRealTimeOrderQueryParam carRealTimeOrderQueryParam);

    CreateCarOrderResult createCarOrder(String postBody);

    CreateCarOrderStrongResult createCarOrder(CarCreateOrderParam carCreateOrderParam);

    CarUserTripsResult moreUserTrips(CarUserTripParams userTripParams);

    CarQueryMerchandiseResult queryMerchandise(CarQueryMerchandiseParam carQueryMerchandiseParam);

    CarQueryMerchandiseV3Result queryMerchandiseV3(CarQueryMerchandiseParam carQueryMerchandiseParam);

    CarQueryMerchandiseDetailResult queryMerchandiseDetail(CarQueryMerchandiseDetailParam carQueryMerchandiseParam);

    CarQueryMerchandisePriceDetailResult queryMerchandisePriceDetail(CarQueryMerchandisePriceDetailParam carQueryMerchandiseParam);

    PageWrapper<List<CarOrderListVO>> queryOrderList(CarQueryOrderListParam carQueryOrderListParam);

    CarQueryOrderDetailResult queryOrderDetail(CarQueryOrderDetailParam carQueryOrderDetailParam);

    CorpOrderRecordsWrapper<CarCorpOrderRecord> queryCarCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam);

    String cancelOrder(CarCancelOrderParam param);

    List<HLServiceVO> queryHLServices(CarQueryHLServiceParam carQueryHLServiceParam);

    CarUserTripsResult queryTripList(CarTripListParam carTripListParam);

    CarQueryCompleteOrderLatelyResult queryCompleteOrderLately(CarQueryCompleteOrderLatelyParam carQueryCompleteOrderLatelyParam);

    CarQueryProductNoticeResult queryProductNotice(CarQueryProductNoticeParam carQueryProductNoticeParam);

    CarQueryProdsResult queryCarProds(CarQueryProdsParam param);

    CarBatchQueryHlServiceResult batchQueryHlService(CarBatchQueryHlServiceParam param);

    CarCitylResult queryCity(CarCitylParam carCitylParam);

    CarOrderInfoResult queryOrderInfo(CarQueryOrderDetailParam carQueryOrderDetailParam);

    String createShortUrl(ShortUrlParam param);
}
