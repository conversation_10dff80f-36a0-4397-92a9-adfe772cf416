package com.huoli.ctar.cfesag.client.deligator.usercenter;

import com.huoli.ctar.cfesag.external.sys.api.usercenter.model.*;

import java.util.List;

public interface UserCenterDeligator {
    String getUserId(final Long phoneId);

    PassengerData loadPassengers(final Long phoneId);

    PassengerVO createPassenger(Long phoneId, CreatePassengerParam createPassengerParam);

    boolean deletePassenger(Long phoneId, DeletePassengerParam passenger);

    Long queryPhoneIdByPhone(String phone);

    Long registerForBusiness(String phone);

    void updatePhone(UpdatePhoneRequest updatePhoneRequest);

    void addTags(UserTagParam userTagParam);

    void deleteTags(UserTagParam userTagParam);

    List<String> queryTags(UserTagParam userTagParam);

    Long registerPlatformUser(RegisterPlatformUserRequest registerPlatformUserRequest);

    Long registerPlatformUserByTpUnionId(final RegisterPlatformUserByUnionIdRequest registerPlatformUserByUnionIdRequest);

    void unbindTp(final UnbindTpRequest unbindTpRequest);

    void bindTp(final BindTpRequest bindTpRequest);

    void updatePlatformUserPhone(final UpdatePlatformUserPhoneRequest updatePlatformUserPhoneRequest);

    String deletePlatformUser(final DeletePlatformUserRequest deletePlatformUserRequest);

    AuthCodeResult getAuthByPhoneId(String phoneId);

    AuthCodeResult getAuthByPhoneIdTemp(String phoneId);

    UserBasicInfo queryUserBaseInfo(final Long phoneId);

    UserInvoiceInfo queryUserInvoiceInfo(final Long phoneId);

    UserPostInfo queryUserPostInfo(final Long phoneId);

    String getUserLatestPhoneId(final String phoneId);
}
