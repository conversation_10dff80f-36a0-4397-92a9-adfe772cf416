package com.huoli.ctar.cfesag.client.deligator.usercenter.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.usercenter.UserCenterDeligator;
import com.huoli.ctar.cfesag.external.sys.api.usercenter.model.*;
import com.huoli.ctar.common.utils.JsonUtil;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.util.TraceContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Objects;

public class DefaultUserCenterDeligator extends BaseDeligator implements UserCenterDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultUserCenterDeligator.class);

    public DefaultUserCenterDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String getUserId(Long phoneId) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/userCenter/users/%d/userId", phoneId);

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PassengerData loadPassengers(Long phoneId) {
        final String url = String.format("%s/external/sys/api/userCenter/user/passenger/query?phoneId=%s", this.getCfesagHost(), phoneId);

        ResponseEntity<BaseResult<PassengerData>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<PassengerData>>() {
        });
        final BaseResult<PassengerData> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PassengerVO createPassenger(Long phoneId, CreatePassengerParam createPassengerParam) {
        return updatePassenger(phoneId, JsonUtil.toJson(createPassengerParam), "add");
    }

    @Override
    public boolean deletePassenger(Long phoneId, DeletePassengerParam passenger) {
        updatePassenger(phoneId, JsonUtil.toJson(passenger), "delete");
        return true;
    }

    private PassengerVO updatePassenger(final Long phoneId, final String info, final String operation) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("phoneId", Objects.nonNull(phoneId) ? phoneId.toString() : "");
        params.add("info", info);
        params.add("operation", operation);

        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(params, headers);

        String url = String.format("%s/external/sys/api/userCenter/user/passenger/update", this.getCfesagHost());
        ResponseEntity<BaseResult<PassengerVO>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PassengerVO>>() {
        });
        final BaseResult<PassengerVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public Long queryPhoneIdByPhone(String phone) {
        final String url = String.format("%s/external/sys/api/userCenter/user/queryPhoneId?phone=%s", this.getCfesagHost(), phone);

        ResponseEntity<BaseResult<Long>> response = execute(url, HttpMethod.POST, null, new ParameterizedTypeReference<BaseResult<Long>>() {
        });
        final BaseResult<Long> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public Long registerForBusiness(String phone) {
        final String url = String.format("%s/external/sys/api/userCenter/user/register?phone=%s", this.getCfesagHost(), phone);

        ResponseEntity<BaseResult<Long>> response = execute(url, HttpMethod.POST, null, new ParameterizedTypeReference<BaseResult<Long>>() {
        });
        final BaseResult<Long> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public void updatePhone(UpdatePhoneRequest updatePhoneRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<UpdatePhoneRequest> httpEntity = new HttpEntity<>(updatePhoneRequest, httpHeaders);

        final String url = String.format("%s/external/sys/api/userCenter/user/updatePhone", this.getCfesagHost());
        ResponseEntity<BaseResult<Object>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<Object>>() {
        });
        checkResponse(response);
    }

    @Override
    public void addTags(UserTagParam userTagParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<UserTagParam> httpEntity = new HttpEntity<>(userTagParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/userCenter/user/tags/add", this.getCfesagHost());
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        checkResponse(response);
    }

    @Override
    public void deleteTags(UserTagParam userTagParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<UserTagParam> httpEntity = new HttpEntity<>(userTagParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/userCenter/user/tags/delete", this.getCfesagHost());
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        checkResponse(response);
    }

    @Override
    public List<String> queryTags(final UserTagParam userTagParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<UserTagParam> httpEntity = new HttpEntity<>(userTagParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/userCenter/user/tags/query", this.getCfesagHost());

        ResponseEntity<BaseResult<List<String>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<String>>>() {
        });
        final BaseResult<List<String>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public Long registerPlatformUser(final RegisterPlatformUserRequest registerPlatformUserRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final String url = String.format("%s/external/sys/api/userCenter/user/registerPlatformUser", this.getCfesagHost());
        final HttpEntity<RegisterPlatformUserRequest> httpEntity = new HttpEntity<>(registerPlatformUserRequest, httpHeaders);

        ResponseEntity<BaseResult<Long>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<Long>>() {
        });
        final BaseResult<Long> result = checkResponse(response);
        return result.getData();
    }


    @Override
    public Long registerPlatformUserByTpUnionId(final RegisterPlatformUserByUnionIdRequest registerPlatformUserByUnionIdRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final String url = String.format("%s/external/sys/api/userCenter/user/registerPlatformUserByTpUnionId", this.getCfesagHost());
        final HttpEntity<RegisterPlatformUserByUnionIdRequest> httpEntity = new HttpEntity<>(registerPlatformUserByUnionIdRequest, httpHeaders);

        ResponseEntity<BaseResult<Long>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<Long>>() {
        });
        final BaseResult<Long> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public void unbindTp(UnbindTpRequest unbindTpRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final String url = String.format("%s/external/sys/api/userCenter/user/unbindTp", this.getCfesagHost());
        final HttpEntity<UnbindTpRequest> httpEntity = new HttpEntity<>(unbindTpRequest, httpHeaders);

        ResponseEntity<BaseResult<Long>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<Long>>() {
        });
        checkResponse(response);
    }

    @Override
    public void bindTp(BindTpRequest bindTpRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final String url = String.format("%s/external/sys/api/userCenter/user/bindTp", this.getCfesagHost());
        final HttpEntity<BindTpRequest> httpEntity = new HttpEntity<>(bindTpRequest, httpHeaders);

        ResponseEntity<BaseResult<Long>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<Long>>() {
        });
        checkResponse(response);
    }

    @Override
    public void updatePlatformUserPhone(final UpdatePlatformUserPhoneRequest updatePlatformUserPhoneRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final String url = String.format("%s/external/sys/api/userCenter/user/updatePlatformUserPhone", this.getCfesagHost());
        final HttpEntity<UpdatePlatformUserPhoneRequest> httpEntity = new HttpEntity<>(updatePlatformUserPhoneRequest, httpHeaders);

        ResponseEntity<BaseResult<Long>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<Long>>() {
        });
        checkResponse(response);
    }

    @Override
    public String deletePlatformUser(final DeletePlatformUserRequest deletePlatformUserRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final String url = String.format("%s/external/sys/api/userCenter/user/deletePlatformUser", this.getCfesagHost());
        final HttpEntity<DeletePlatformUserRequest> httpEntity = new HttpEntity<>(deletePlatformUserRequest, httpHeaders);

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public AuthCodeResult getAuthByPhoneId(String phoneId) {
        final String url = String.format("%s/external/sys/api/userCenter/user/queryAuth?phoneId=%s", this.getCfesagHost(), phoneId);

        ResponseEntity<BaseResult<AuthCodeResult>> response = execute(url, HttpMethod.POST, null, new ParameterizedTypeReference<BaseResult<AuthCodeResult>>() {
        });
        final BaseResult<AuthCodeResult> result = checkResponse(response);

        return result.getData();
    }

    @Override
    public AuthCodeResult getAuthByPhoneIdTemp(String phoneId) {
        final String url = String.format("%s/external/sys/api/userCenter/user/authByPhoneIdTemp?phoneId=%s", this.getCfesagHost(), phoneId);

        ResponseEntity<BaseResult<AuthCodeResult>> response = execute(url, HttpMethod.POST, null, new ParameterizedTypeReference<BaseResult<AuthCodeResult>>() {
        });
        final BaseResult<AuthCodeResult> result = checkResponse(response);

        return result.getData();
    }

    @Override
    public UserBasicInfo queryUserBaseInfo(Long phoneId) {
        final String url = String.format("%s/external/sys/api/userCenter/user/baseInfo/query?phoneId=%s", this.getCfesagHost(), phoneId);
        ResponseEntity<BaseResult<UserBasicInfo>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<UserBasicInfo>>() {
        });
        final BaseResult<UserBasicInfo> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public UserInvoiceInfo queryUserInvoiceInfo(Long phoneId) {
        final String url = String.format("%s/external/sys/api/userCenter/user/invoice/query?phoneId=%s", this.getCfesagHost(), phoneId);
        ResponseEntity<BaseResult<UserInvoiceInfo>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<UserInvoiceInfo>>() {
        });
        final BaseResult<UserInvoiceInfo> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public UserPostInfo queryUserPostInfo(Long phoneId) {
        final String url = String.format("%s/external/sys/api/userCenter/user/post/query?phoneId=%s", this.getCfesagHost(), phoneId);
        ResponseEntity<BaseResult<UserPostInfo>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<UserPostInfo>>() {
        });
        final BaseResult<UserPostInfo> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String getUserLatestPhoneId(final String phoneId) {
        final String url = String.format("%s/external/sys/api/userCenter/user/latestPhoneId/retrieve?phoneId=%s", this.getCfesagHost(), phoneId);
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
}
