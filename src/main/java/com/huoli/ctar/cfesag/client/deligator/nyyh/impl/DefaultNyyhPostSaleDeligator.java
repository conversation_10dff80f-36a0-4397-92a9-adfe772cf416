package com.huoli.ctar.cfesag.client.deligator.nyyh.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.nyyh.NyyhPostSaleDeligator;
import com.huoli.ctar.cfesag.client.exception.CfesagInvokeException;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.cfesag.external.sys.api.nyyh.model.NyyhPostSaleProcessParam;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.util.TraceContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;

@Slf4j
public class DefaultNyyhPostSaleDeligator extends BaseDeligator implements NyyhPostSaleDeligator {

    public DefaultNyyhPostSaleDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String reconciliationDataSendBack(NyyhPostSaleProcessParam param) {
        return reconciliationDataBaseSendBack(param,"nyyh");
    }

	public String reconciliationDataBaseSendBack(NyyhPostSaleProcessParam param,String preCorp) {
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
			headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());

			StringBuilder urlBuilder = new StringBuilder();
			urlBuilder.append(this.getCfesagHost());
			urlBuilder.append("/external/sys/api");
			if(!"nyyh".equals(preCorp)){
				urlBuilder.append("/new");
			}
			urlBuilder.append("/").append(preCorp).append("/postSale/reconciliation/sendback");

			HttpEntity<NyyhPostSaleProcessParam> httpEntity = new HttpEntity<>(param,headers);
			String url = urlBuilder.toString();
			log.info("农行={},请求url={}",preCorp,url);
			ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<String>>() {
			});
			BaseResult<String> result = checkResponse(response);
			return String.valueOf(result.getCode());
		} catch (RestClientException e) {
			throw new CfesagUnavailableException(e.getMessage(), e);
		}
	}

    @Override
    public String invoiceDataSendBack(NyyhPostSaleProcessParam param) {
        return invoiceDataBaseSendBack(param,"nyyh");
    }

	public String invoiceDataBaseSendBack(NyyhPostSaleProcessParam param,String preCorp) {
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
			headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());

			StringBuilder urlBuilder = new StringBuilder();
			urlBuilder.append(this.getCfesagHost());
			urlBuilder.append("/external/sys/api");
			if(!"nyyh".equals(preCorp)){
				urlBuilder.append("/new");
			}
			urlBuilder.append("/").append(preCorp).append("/postSale/invoice/sendback");

			HttpEntity<NyyhPostSaleProcessParam> httpEntity = new HttpEntity<>(param,headers);
			String url = urlBuilder.toString();
			log.info("农行={},请求url={}",preCorp,url);
			ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<String>>() {
			});
			BaseResult<String> result = checkResponse(response);
			return String.valueOf(result.getCode());
		} catch (RestClientException e) {
			throw new CfesagUnavailableException(e.getMessage(), e);
		}
	}


    @Override
    public String waybillDataSendBack(NyyhPostSaleProcessParam param) {
		return waybillDataBaseSendBack(param,"nyyh");
    }

	@Override
	public String reconciliationDataNylcSendBack(NyyhPostSaleProcessParam param)
	{
		return reconciliationDataBaseSendBack(param,param.getAppId());
	}

	@Override
	public String invoiceDataNyclSendBack(NyyhPostSaleProcessParam param)
	{
		return invoiceDataBaseSendBack(param,param.getAppId());
	}

	@Override
	public String waybillDataNyclSendBack(NyyhPostSaleProcessParam param)
	{
		return waybillDataBaseSendBack(param,param.getAppId());
	}

	private String waybillDataBaseSendBack(NyyhPostSaleProcessParam param, String preCorp)
	{
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
			headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());

			StringBuilder urlBuilder = new StringBuilder();
			urlBuilder.append(this.getCfesagHost());
			urlBuilder.append("/external/sys/api");
			if(!"nyyh".equals(preCorp)){
				urlBuilder.append("/new");
			}
			urlBuilder.append("/").append(preCorp).append("/postSale/waybill/sendback");

			HttpEntity<NyyhPostSaleProcessParam> httpEntity = new HttpEntity<>(param,headers);
			String url = urlBuilder.toString();
			log.info("农行={},请求url={}",preCorp,url);
			ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<String>>() {
			});
			BaseResult<String> result = checkResponse(response);
			return String.valueOf(result.getCode());
		} catch (RestClientException e) {
			throw new CfesagUnavailableException(e.getMessage(), e);
		}
	}

	@Override
    protected <T> BaseResult<T> checkResponse(ResponseEntity<BaseResult<T>> response) {
        final HttpStatus.Series series = HttpStatus.Series.valueOf(response.getStatusCode());
        if (HttpStatus.Series.CLIENT_ERROR == series || HttpStatus.Series.SERVER_ERROR == series) {
            if (Objects.nonNull(response.getBody())) {
                log.info("------{}", response.getBody().getMsg());
                throw new CfesagInvokeException(response.getBody().getCode(), response.getBody().getMsg(), response.getBody().getButtons(), response.getBody().getTitle());
            } else {
                throw new CfesagInvokeException(response.getStatusCode().getReasonPhrase());
            }
        }
        if (Objects.isNull(response.getBody())) {
            throw new CfesagInvokeException("网关服务器内部错误");
        }
        if (200 != response.getBody().getCode()) {
            throw new CfesagInvokeException(response.getBody().getCode(), response.getBody().getMsg(), response.getBody().getButtons(), response.getBody().getTitle());
        }
        return response.getBody();
    }
}
