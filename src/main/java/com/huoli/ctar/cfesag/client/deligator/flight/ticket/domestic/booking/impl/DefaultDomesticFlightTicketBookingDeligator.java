package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.DomesticFlightTicketBookingDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.booking.model.DomesticFlightTicketRefundRecord;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Objects;

public class DefaultDomesticFlightTicketBookingDeligator extends BaseDeligator implements DomesticFlightTicketBookingDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultDomesticFlightTicketBookingDeligator.class);

    public DefaultDomesticFlightTicketBookingDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public List<DomesticFlightTicketRefundRecord> loadDomesticFlightTicketRefundRecords(final Long transTimeFrom, final Long transTimeTo) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/domesticFlightTicket/booking/refundRecords?transTimeFrom=%s&transTimeTo=%s",
                Objects.isNull(transTimeFrom) ? "" : String.valueOf(transTimeFrom),
                Objects.isNull(transTimeTo) ? "" : String.valueOf(transTimeTo));
        ResponseEntity<BaseResult<List<DomesticFlightTicketRefundRecord>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<List<DomesticFlightTicketRefundRecord>>>() {
        });
        final BaseResult<List<DomesticFlightTicketRefundRecord>> result = checkResponse(response);
        return result.getData();
    }
}
