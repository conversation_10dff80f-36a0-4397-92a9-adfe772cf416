package com.huoli.ctar.cfesag.client.deligator.resale.info;

import com.huoli.ctar.cfesag.external.sys.api.resale.ticket.domestic.model.DftOrderBoardingInfo;
import com.huoli.ctar.cfesag.external.sys.api.resale.ticket.domestic.model.DftResaleInfoParam;
import com.huoli.ctar.cfesag.external.sys.api.resale.ticket.domestic.model.DftResaleInfoVO;

import java.util.List;

public interface ResaleInfoDeligator {

    List<DftResaleInfoVO> queryDFTResaleInfo(DftResaleInfoParam dftResaleInfoParam);

    void doBoardingNotification(final DftOrderBoardingInfo boardingInfo);
}
