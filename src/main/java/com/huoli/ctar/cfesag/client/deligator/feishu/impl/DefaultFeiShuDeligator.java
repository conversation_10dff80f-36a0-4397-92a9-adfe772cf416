package com.huoli.ctar.cfesag.client.deligator.feishu.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.feishu.FeiShuDeligator;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.cfesag.external.sys.api.feishu.*;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.util.TraceContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2021-06-16 16:44
 */
@Slf4j
public class DefaultFeiShuDeligator extends BaseDeligator implements FeiShuDeligator {
    public DefaultFeiShuDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public TokenResult queryAppAccessToken(AppTokenParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        HttpEntity<AppTokenParam> httpEntity = new HttpEntity<>(param, headers);
        String url = String.format("%s/external/sys/api/feishu/app_access_token/", this.getCfesagHost());
        ResponseEntity<BaseResult<TokenResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TokenResult>>() {
        });
        final BaseResult<TokenResult> result = checkResponseNotCheckCode(response);
        return result.getData();
    }

    @Override
    public TokenResult queryTenantAccessToken(TenantTokenParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        HttpEntity<TenantTokenParam> httpEntity = new HttpEntity<>(param, headers);
        String url = String.format("%s/external/sys/api/feishu/tenant_access_token/", this.getCfesagHost());
        ResponseEntity<BaseResult<TokenResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TokenResult>>() {
        });
        final BaseResult<TokenResult> result = checkResponseNotCheckCode(response);
        return result.getData();
    }

    @Override
    public TokenResult appTicketResend(TicketResendParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());
        HttpEntity<TicketResendParam> httpEntity = new HttpEntity<>(param, headers);
        String url = String.format("%s/external/sys/api/feishu/app_ticket/resend/", this.getCfesagHost());
        ResponseEntity<BaseResult<TokenResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TokenResult>>() {
        });
        final BaseResult<TokenResult> result = checkResponseNotCheckCode(response);
        return result.getData();
    }


    @Override
    public FeishuPageWrapper<FeiShuDepartInfo> loadDepartInfos(final FeiShuDepartQueryParam departQueryParam) {
        try {
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api/feishu/contact/v3/departments");

            Map<String, Object> map = new HashMap<>();
            if (!StringUtils.isBlank(departQueryParam.getUserIdType())) {
                map.put("userIdType", departQueryParam.getUserIdType());
            }
            if (Objects.nonNull(departQueryParam.getDepartmentIdType())) {
                map.put("departmentIdType", departQueryParam.getDepartmentIdType());
            }
            if (!StringUtils.isBlank(departQueryParam.getParentDepartmentId())) {
                map.put("parentDepartmentId", departQueryParam.getParentDepartmentId());
            }
            if (!StringUtils.isBlank(departQueryParam.getPageToken())) {
                map.put("pageToken", departQueryParam.getPageToken());
            }

            if (Objects.nonNull(departQueryParam.getPageSize())) {
                map.put("pageSize", departQueryParam.getPageSize());
            }

            String params = buildUrlParams(map);
            if (!CollectionUtils.isEmpty(map)) {
                urlBuilder.append(String.format("?%s", params));
            }

            HttpHeaders headers = constructFeiShuCommonHeaders(departQueryParam.getAuthorization());
            HttpEntity<TicketResendParam> httpEntity = new HttpEntity<>(headers);

            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<FeishuPageWrapper<FeiShuDepartInfo>>> response = execute(url, HttpMethod.GET, httpEntity, map, new ParameterizedTypeReference<BaseResult<FeishuPageWrapper<FeiShuDepartInfo>>>() {
            });
            final BaseResult<FeishuPageWrapper<FeiShuDepartInfo>> result = checkResponse(response);
            return result.getData();
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }

    }

    @Override
    public FeishuPageWrapper<FeiShuUserInfo> loadUserInfos(final FeiShuUserQueryParam userQueryParam) {
        try {
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api/feishu/contact/v3/users");

            Map<String, Object> map = new HashMap<>();

            if (!StringUtils.isBlank(userQueryParam.getUserIdType())) {
                map.put("userIdType", userQueryParam.getUserIdType());
            }
            if (Objects.nonNull(userQueryParam.getDepartmentIdType())) {
                map.put("departmentIdType", userQueryParam.getDepartmentIdType());
            }
            if (!StringUtils.isBlank(userQueryParam.getDepartmentId())) {
                map.put("departmentId", userQueryParam.getDepartmentId());
            }
            if (!StringUtils.isBlank(userQueryParam.getPageToken())) {
                map.put("pageToken", userQueryParam.getPageToken());
            }

            if (Objects.nonNull(userQueryParam.getPageSize())) {
                map.put("pageSize", userQueryParam.getPageSize());
            }

            String params = buildUrlParams(map);
            if (!CollectionUtils.isEmpty(map)) {
                urlBuilder.append(String.format("?%s", params));
            }

            HttpHeaders headers = constructFeiShuCommonHeaders(userQueryParam.getAuthorization());
            HttpEntity<TicketResendParam> httpEntity = new HttpEntity<>(headers);

            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<FeishuPageWrapper<FeiShuUserInfo>>> response = execute(url, HttpMethod.GET, httpEntity, map, new ParameterizedTypeReference<BaseResult<FeishuPageWrapper<FeiShuUserInfo>>>() {
            });
            final BaseResult<FeishuPageWrapper<FeiShuUserInfo>> result = checkResponse(response);
            return result.getData();
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }

    @Override
    public FeiShuUserBasicInfo getUserInfo(final String authorization,
                                           final String userId,
                                           final String userIdType) {
        final HttpHeaders httpHeaders = constructFeiShuCommonHeaders(authorization);
        HttpEntity httpEntity = new HttpEntity<>(httpHeaders);
        String url = String.format("%s/external/sys/api/feishu/contact/v3/users/%s?userIdType=%s", this.getCfesagHost(), userId, userIdType);
        ResponseEntity<BaseResult<FeiShuUserBasicInfo>> response = execute(url, HttpMethod.GET, httpEntity, new ParameterizedTypeReference<BaseResult<FeiShuUserBasicInfo>>() {
        });
        final BaseResult<FeiShuUserBasicInfo> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public FeiShuSingleUserSendMsgResult sendFeiShuUserMsg(final String authorization,
                                                           final String receiveIdType,
                                                           final FeiShuSingleUserSendMsgRequest requestParam) {
        final HttpHeaders httpHeaders = constructFeiShuCommonHeaders(authorization);
        HttpEntity<FeiShuSingleUserSendMsgRequest> httpEntity = new HttpEntity<>(requestParam, httpHeaders);
        String url = String.format("%s/external/sys/api/feishu/singleUserMsg/send?receiveIdType=%s", this.getCfesagHost(), StringUtils.isNotBlank(receiveIdType) ? receiveIdType : "");
        ResponseEntity<BaseResult<FeiShuSingleUserSendMsgResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FeiShuSingleUserSendMsgResult>>() {
        });
        final BaseResult<FeiShuSingleUserSendMsgResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public FeiShuBatchSendMsgResult batchSendFeiShuMsg(final String authorization,
                                                       final FeiShuBatchSendMsgRequest requestParam) {
        final HttpHeaders httpHeaders = constructFeiShuCommonHeaders(authorization);
        HttpEntity<FeiShuBatchSendMsgRequest> httpEntity = new HttpEntity<>(requestParam, httpHeaders);
        String url = String.format("%s/external/sys/api/feishu/batchTextMsg/send", this.getCfesagHost());
        ResponseEntity<BaseResult<FeiShuBatchSendMsgResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FeiShuBatchSendMsgResult>>() {
        });
        final BaseResult<FeiShuBatchSendMsgResult> result = checkResponse(response);
        return result.getData();
    }


    protected HttpHeaders constructFeiShuCommonHeaders(final String authorization) {
        final HttpHeaders headers = constructCommonHeaders();
        headers.set("FeiShu-Authorization", String.format("Bearer %s", authorization));
        return headers;
    }

    @Override
    public ExternalTokenResult tmcTenantToken(TenantAccessTokenParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        HttpEntity<TenantAccessTokenParam> httpEntity = new HttpEntity<>(param, headers);
        String url = String.format("%s/external/sys/api/feishu/tenant_token/query", this.getCfesagHost());
        ResponseEntity<BaseResult<ExternalTokenResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<ExternalTokenResult>>() {
        });
        final BaseResult<ExternalTokenResult> result = checkResponseNotCheckCode(response);
        return result.getData();
    }

    @Override
    public FeiShuCardMsgResult sendFeiShuCardMessage(final String authorization,
                                                     final FeiShuCardMsgParam cardMsgParam) {
        final HttpHeaders httpHeaders = constructFeiShuCommonHeaders(authorization);
        HttpEntity<FeiShuCardMsgParam> httpEntity = new HttpEntity<>(cardMsgParam, httpHeaders);
        String url = String.format("%s/external/sys/api/feishu/cardMessage/send", this.getCfesagHost());
        ResponseEntity<BaseResult<FeiShuCardMsgResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FeiShuCardMsgResult>>() {
        });
        final BaseResult<FeiShuCardMsgResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public FeiShuTenant getTenant(final String authorization) {
        final HttpHeaders httpHeaders = constructFeiShuCommonHeaders(authorization);
        HttpEntity httpEntity = new HttpEntity<>(httpHeaders);
        String url = String.format("%s/external/sys/api/feishu/tenant/v2/tenant/query", this.getCfesagHost());
        ResponseEntity<BaseResult<FeiShuTenant>> response = execute(url, HttpMethod.GET, httpEntity, new ParameterizedTypeReference<BaseResult<FeiShuTenant>>() {
        });
        final BaseResult<FeiShuTenant> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public FeiShuReplyMsgResult replyRobotMessage(final String authorization,
                                                  final String messageId,
                                                  final FeiShuReplyMessageParam replyMessageParam){
            final HttpHeaders httpHeaders = constructFeiShuCommonHeaders(authorization);
            HttpEntity<FeiShuReplyMessageParam> httpEntity = new HttpEntity<>(replyMessageParam, httpHeaders);
            String url = String.format("%s/external/sys/api/feishu/robot/reply/message/%s", this.getCfesagHost(), messageId);
            ResponseEntity<BaseResult<FeiShuReplyMsgResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FeiShuReplyMsgResult>>() {
            });
            final BaseResult<FeiShuReplyMsgResult> result = checkResponse(response);
            return result.getData();
    }

    @Override
    public FeiShuLoginUserInfo getFeiShuLoginUserInfo(final String authorization,
                                                      final FeiShuLoginParam feiShuLoginParam) {

        final HttpHeaders httpHeaders = constructFeiShuCommonHeaders(authorization);
        HttpEntity<FeiShuLoginParam> httpEntity = new HttpEntity<>(feiShuLoginParam, httpHeaders);
        String url = String.format("%s/external/sys/api/feishu/authen/v1/access_token", this.getCfesagHost());
        ResponseEntity<BaseResult<FeiShuLoginUserInfo>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FeiShuLoginUserInfo>>() {
        });
        final BaseResult<FeiShuLoginUserInfo> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public ExternalTokenResult tmcAppToken(AppAccessTokenParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());


        HttpEntity<AppAccessTokenParam> httpEntity = new HttpEntity<>(param, headers);
        String url = String.format("%s/external/sys/api/feishu/app_token/query", this.getCfesagHost());
        ResponseEntity<BaseResult<ExternalTokenResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<ExternalTokenResult>>() {
        });
        final BaseResult<ExternalTokenResult> result = checkResponseNotCheckCode(response);
        return result.getData();
    }
}
