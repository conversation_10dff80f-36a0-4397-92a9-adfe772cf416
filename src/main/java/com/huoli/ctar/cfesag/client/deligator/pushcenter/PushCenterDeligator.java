package com.huoli.ctar.cfesag.client.deligator.pushcenter;

import com.huoli.ctar.cfesag.external.sys.api.pushcenter.model.PushMessageRequest;
import com.huoli.ctar.cfesag.external.sys.api.pushcenter.model.PushTemplateMessageRequest;
import com.huoli.ctar.cfesag.external.sys.api.pushcenter.model.PushWeChatMessageRequest;
import com.huoli.ctar.cfesag.external.sys.api.pushcenter.model.SendSmsRequest;

public interface PushCenterDeligator {
    String pushTemplateBizMsg(final PushTemplateMessageRequest templateMessageRequest);

    String pushMsg(final PushMessageRequest pushMessageRequest);

    String pushWeChatMsg(final PushWeChatMessageRequest pushWeChatMessageRequest);

    String sendSms(final SendSmsRequest sendSmsRequest);
}
