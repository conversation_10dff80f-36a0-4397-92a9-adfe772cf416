package com.huoli.ctar.cfesag.client.deligator.caihailing;

import com.huoli.ctar.cfesag.external.sys.api.car.model.CarRealTimeOrderQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.carhailing.model.CarhailingSelectInitModel;
import com.huoli.ctar.cfesag.external.sys.api.carhailing.model.CreateHailingOrderParam;
import com.huoli.ctar.cfesag.external.sys.api.carhailing.model.QueryOrderStatusModel;
import com.huoli.ctar.cfesag.external.sys.api.carhailing.vo.*;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CarCorpOrderRecord;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.core.infra.model.PageWrapper;

import java.util.List;

public interface CarHailingDeligator {

    /**
     * 下单
     * @param
     * @return
     */
    CreateCarHailingResultVO createCarhailingOrder(CreateHailingOrderParam createCarhailingParam);

    /**
     * 派单
     * @param
     * @return
     */
    String dispatcherTmcOrder(String orderid);


    /**
     * 货架
     * @param
     * @return
     */
    CarsSelectInitResultVO carhailingSelectInit(CarhailingSelectInitModel carhailingSelectInitParam);



    /**
     * 订单详情
     * @param
     * @return
     */
    QueryOrderStatusResultVO queryCarhailingOrderStatus(QueryOrderStatusModel queryOrderStatusParam);


    /**
     * 告警
     * @param
     * @return
     */
    String warnningToPhonesByWechat(String content,String phones);


    /**
     * 打车全部城市列表
     *
     * @param
     * @return
     */
    CarHailingServiceCityVO queryCarhailingServiceCitys();

    /**
     * 打车对账
     * @param
     * @return
     */
    List<CarHailingOrderStatusVO> queryCorpOrderReconciliation(Long startTime, Long endTime, Long corpId, String orderId);


    CarOrderSycnVO carHailingOrderToTmcOrder(String orderId);


    List<CarOrderSycnVO> carHailingOrderListQuery(String phoneid,String source);

    CorpOrderRecordsWrapper<CarCorpOrderRecord> queryCarHailingCorpOrderTransRecords(CorpOrderRecordsQueryParam params);

    PageWrapper<List<CarHailingOrderDetail>> queryCarHailingRealTimeOrderInfo(CarRealTimeOrderQueryParam carRealTimeOrderQueryParam);


}
