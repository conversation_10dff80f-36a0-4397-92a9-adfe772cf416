package com.huoli.ctar.cfesag.client.deligator.resale.gateway;

import com.huoli.ctar.cfesag.external.sys.api.resale.gateway.model.ApiClient;
import com.huoli.ctar.cfesag.external.sys.api.resale.gateway.model.ApiClientRegisterRequest;
import com.huoli.ctar.cfesag.external.sys.api.resale.gateway.model.ApiClientUpdateRequest;

public interface ResaleGatewayDeligator {
    ApiClient register(final ApiClientRegisterRequest apiClientRegisterRequest);
    String updateApiClient(final ApiClientUpdateRequest apiClientUpdateRequest, final String openId);
    ApiClient getApiClientByOpenId(String openId);
}
