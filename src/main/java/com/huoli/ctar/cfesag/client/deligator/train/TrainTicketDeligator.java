package com.huoli.ctar.cfesag.client.deligator.train;

import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.TrainCorpOrderRecord;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.TrainResaleOrderConsumption;
import com.huoli.ctar.cfesag.external.sys.api.train.model.*;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.GetGtUserAccountParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.GetGtUserAccountResult;
import com.huoli.ctar.core.infra.model.PageWrapper;

import java.util.List;

public interface TrainTicketDeligator {
    PageWrapper<List<TrainTicketOrder>> loadTrainTicketOrders(final String phoneId,
                                                              final String orderId,
                                                              final String orderStatus,
                                                              final String passengerName,
                                                              final String cardNo,
                                                              final Long startTime,
                                                              final Long endTime,
                                                              final Integer pageNo,
                                                              final Integer pageSize);

    PageWrapper<List<DistTrainTicketTransFlowRecord>> loadDistTrainTicketTransFlowRecords(final String distId,
                                                                                          final String loginUserName,
                                                                                          final String phoneId,
                                                                                          final String orderId,
                                                                                          final String transType,
                                                                                          final String transChannel,
                                                                                          final String consumeType,
                                                                                          final Long transTimeFrom,
                                                                                          final Long transTimeTo,
                                                                                          final Integer pageNo,
                                                                                          final Integer pageSize);

    String insertDistTransFlow(List<DistTrainTradeLine> distTradeLines);

    String getConfigs(String key);

    String sm4DecodeBySource(String content,String source);

    List<TrainTicketTransFlowRecord> loadTrainTicketTransFlowRecords(final String corpId,
                                                                     final String orderId,
                                                                     final Long transTimeFrom,
                                                                     final Long transTimeTo);

    List<DelayCareTransFlowRecord> loadDelayCareTransFlowRecords(final String corpId,
                                                                 final Long transTimeFrom,
                                                                 final Long transTimeTo);

    CorpTrainTicketOrderDetail getCorpTrainTicketOrderDetail(final String orderId, final String payOrderId);

    CorpTrainTicketMainSubOrderDetail getCorpTrainTicketMainSubOrderDetail(final String orderId);

    String getTrainTMCUrl(TrainTicketHandleUrlQueryParam param);

    PageWrapper<List<TrainTicketRealTimeOrderVO>> queryTrainRealTimeOrderInfo(TrainTicketRealTimeOrderQueryParam queryParam);

    TrainTicketRealTimeOrderDetailVO queryTrainRealTimeOrderDetailByOrderId(String orderId);

    List<TrainTicketGrabOrderDetailVO> queryGrabOrderByParams(final String id,
                                                              final String orderId,
                                                              final String phoneId,
                                                              final String phone,
                                                              final String erpId);

    String setExpired(final String id);

    PlatformBuyOrderCreate createTrainTicketOrder(final String orderFormData);

    List<TrainGrabOrderStatisticsVO> queryTrainGrabOrderStatistics(final String phoneId,
                                                                   final String startDate,
                                                                   final String endDate);

    SearchApiCountTrainResult queryTrainCountApi(SearchApiCountTrainParam searchApiCountTrainParam);

    QueryOrderListResultVO queryOrderList(TrainTicketOrderListQueryParam param);

    OrderDetailResultVO queryOrderDetail(TrainTicketQueryDetailParam param);

    TrainZwdRateResultVO queryTrainZwdRate(String trainNo, String departStation, String arriveStation);

    QueryTrainScheduleDetailVO queryTrainScheduleShareDetail(QueryTrainScheduleDetailParam param);

    CorpOrderRecordsWrapper<TrainCorpOrderRecord> queryTrainCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam);

    String cancelOrder(TrainCancelOrderParam param);
    
    GetPayUrlResultVO getPayUrlSpV2(GetPayUrlParam param);
    
    EndorseRequestResultVO endorseRequest(EndorseRequestParam param);
    
    HelpPayInstPurchaseCart instPurchased(InstPurchasedParam instPurchasedParam);
    
    String endorseConfirm(EndorseConfirmParam param);
    
    GetEndorsePayUrlResultVO getEndorsePayUrl(GetEndorsePayUrlParam param);

    GetGtUserAccountResult getGtUserAccount(GetGtUserAccountParam param);

    TrainOrderConsumeInfoResultVO queryOrderConsumeInfo(TrainOrderConsumeInfoParam param);

    TrainCorpResaleOrderVO queryTrainCorpResaleOrderInfo(TrainCorpResaleOrderQueryParam trainCorpResaleOrderQueryParam);

    CorpResaleOrderRecsWrapper<TrainResaleOrderConsumption> queryTrainResaleCorpOrderRecs(final CorpResaleOrderRecsQueryParam queryParam);

    String queryTrainResaleOrderSubOrderId(String passengerName,String orderID);

    TrainOrderSensitiveInfoVO queryOrderSensitiveInfo(TrainOrderSensitiveQueryParam trainOrderSensitiveQueryParam);
}
