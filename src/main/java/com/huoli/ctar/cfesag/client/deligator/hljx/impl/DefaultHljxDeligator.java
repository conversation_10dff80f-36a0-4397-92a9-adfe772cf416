package com.huoli.ctar.cfesag.client.deligator.hljx.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.hljx.HljxDeligator;
import com.huoli.ctar.cfesag.external.sys.api.hljx.vo.TmcHLCommoditiesRequest;
import com.huoli.ctar.cfesag.external.sys.api.hljx.vo.TmcHLCommoditiesWrapper;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultHljxDeligator extends BaseDeligator implements HljxDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultHljxDeligator.class);

    public DefaultHljxDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public TmcHLCommoditiesWrapper loadHlCommodities(TmcHLCommoditiesRequest request) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<TmcHLCommoditiesRequest> httpEntity = new HttpEntity<>(request, httpHeaders);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/hljx/commodities");
        ResponseEntity<BaseResult<TmcHLCommoditiesWrapper>> exchangeResult = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<TmcHLCommoditiesWrapper>>() {});
        BaseResult<TmcHLCommoditiesWrapper> result = checkResponse(exchangeResult);
        return result.getData();
    }
}
