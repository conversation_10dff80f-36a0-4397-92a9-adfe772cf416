package com.huoli.ctar.cfesag.client.deligator.caihailing.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.caihailing.CarHailingDeligator;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.cfesag.external.sys.api.car.model.CarRealTimeOrderQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.carhailing.model.CarhailingSelectInitModel;
import com.huoli.ctar.cfesag.external.sys.api.carhailing.model.CreateHailingOrderParam;
import com.huoli.ctar.cfesag.external.sys.api.carhailing.model.QueryOrderStatusModel;
import com.huoli.ctar.cfesag.external.sys.api.carhailing.vo.*;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CarCorpOrderRecord;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class DefaultCarHailingDeligator extends BaseDeligator implements CarHailingDeligator {

    public DefaultCarHailingDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }


    @Override
    public CreateCarHailingResultVO createCarhailingOrder(CreateHailingOrderParam createCarhailingParam) {
        try {
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/carhailing/order/createOrder");

            HttpEntity<CreateHailingOrderParam> httpEntity = new HttpEntity<>(createCarhailingParam,constructCommonHeaders());
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<CreateCarHailingResultVO>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<CreateCarHailingResultVO>>() {
            });
            
            return checkResponse(response).getData();
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }

    @Override
    public String dispatcherTmcOrder(String orderid) {
        try {
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/carhailing/order/dispatcherTmcOrder?");

            Map<String, String> map = new HashMap<>();
            map.put("orderid",orderid);
            urlBuilder.append(buildUrlParams(map));
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.GET, null, map,  new ParameterizedTypeReference<BaseResult<String>>() {
            });
            
            return checkResponse(response).getData();
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }

    protected String buildUrlParams(Map<String, ?> values) {
        if (CollectionUtils.isEmpty(values)) {
            return null;
        }
        StringBuilder params = new StringBuilder();
        values.keySet().forEach(param -> {
            if (params.length() > 0) {
                params.append("&");
            }
            params.append(String.format("%s={%s}", param, param));
        });
        return params.toString();
    }

    @Override
    public CarsSelectInitResultVO carhailingSelectInit(CarhailingSelectInitModel carhailingSelectInitParam) {
        try {
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/carhailing/order/carSelectInit");

            HttpEntity<CarhailingSelectInitModel> httpEntity = new HttpEntity<>(carhailingSelectInitParam,constructCommonHeaders());
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<CarsSelectInitResultVO>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<CarsSelectInitResultVO>>() {
            });
            
            return checkResponse(response).getData();
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }

    @Override
    public QueryOrderStatusResultVO queryCarhailingOrderStatus(QueryOrderStatusModel queryOrderStatusParam) {
        try {
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/carhailing/order/queryOrderStatus");

            HttpEntity<QueryOrderStatusModel> httpEntity = new HttpEntity<>(queryOrderStatusParam,constructCommonHeaders());
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<QueryOrderStatusResultVO>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<QueryOrderStatusResultVO>>() {
            });
            
            return checkResponse(response).getData();
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }

    @Override
    public String warnningToPhonesByWechat(String content, String phones) {
        try {
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/carhailing/order/warnning?");

            Map<String,Object> map =new HashedMap();
            map.put("content", content);
            map.put("phones", phones);
            urlBuilder.append(buildUrlParams(map));
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.GET, null, map, new ParameterizedTypeReference<BaseResult<String>>() {
            });
            
            return checkResponse(response).getData();
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }

    @Override
    public CarHailingServiceCityVO queryCarhailingServiceCitys() {
        try {
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/carhailing/serviceCity");

            Map<String,Object> map =new HashedMap();
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<CarHailingServiceCityVO>> response = execute(url, HttpMethod.GET, null, map, new ParameterizedTypeReference<BaseResult<CarHailingServiceCityVO>>() {
            });
            
            return checkResponse(response).getData();
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }

    @Override
    public List<CarHailingOrderStatusVO> queryCorpOrderReconciliation(Long startTime, Long endTime, Long corpId,String orderId) {
        try {
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/carhailing/order/reconciliation?");

            Map<String, String> map = new HashMap<>();
            map.put("corpId",String.valueOf(corpId));
            if (Objects.nonNull(startTime)) {
                map.put("startTime", String.valueOf(startTime));
            }
            if (Objects.nonNull(endTime)) {
                map.put("endTime", String.valueOf(endTime));
            }
            if (Objects.nonNull(orderId)) {
                map.put("orderId", orderId);
            }
            urlBuilder.append(buildUrlParams(map));
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<List<CarHailingOrderStatusVO>>> response = execute(url, HttpMethod.GET, null, map,  new ParameterizedTypeReference<BaseResult<List<CarHailingOrderStatusVO>>>() {
            });
            
            return checkResponse(response).getData();
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }

    @Override
    public CarOrderSycnVO carHailingOrderToTmcOrder(String orderId) {
        try {
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/carhailing/order/toTmcOrder?");

            Map<String, String> map = new HashMap<>();
            map.put("orderId",String.valueOf(orderId));
            urlBuilder.append(buildUrlParams(map));
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<CarOrderSycnVO>> response = execute(url, HttpMethod.GET, null, map,  new ParameterizedTypeReference<BaseResult<CarOrderSycnVO>>() {
            });
            
            return checkResponse(response).getData();
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }

    @Override
    public List<CarOrderSycnVO> carHailingOrderListQuery(String phoneid, String source) {
        try {
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/carhailing/orderList/query?");

            Map<String, String> map = new HashMap<>();
            map.put("phoneid",phoneid);
            map.put("source",source);
            urlBuilder.append(buildUrlParams(map));
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<List<CarOrderSycnVO>>> response = execute(url, HttpMethod.GET, null, map,  new ParameterizedTypeReference<BaseResult<List<CarOrderSycnVO>>>() {
            });

            return checkResponse(response).getData();
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }

    @Override
    public CorpOrderRecordsWrapper<CarCorpOrderRecord> queryCarHailingCorpOrderTransRecords(CorpOrderRecordsQueryParam params) {
        try {
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/carhailing/corpOrderRecords/query");

            HttpEntity<CorpOrderRecordsQueryParam> httpEntity = new HttpEntity<>(params,constructCommonHeaders());
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<CorpOrderRecordsWrapper<CarCorpOrderRecord>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CorpOrderRecordsWrapper<CarCorpOrderRecord>>>() {
            });

            return checkResponse(response).getData();
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }

    @Override
    public PageWrapper<List<CarHailingOrderDetail>> queryCarHailingRealTimeOrderInfo(CarRealTimeOrderQueryParam params) {
        try {
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/carhailing/queryRealTimeOrderInfo");

            HttpEntity<CarRealTimeOrderQueryParam> httpEntity = new HttpEntity<>(params,constructCommonHeaders());
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<PageWrapper<List<CarHailingOrderDetail>>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<CarHailingOrderDetail>>>>() {
            });

            return checkResponse(response).getData();
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }
}
