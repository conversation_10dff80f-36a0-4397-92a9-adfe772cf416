package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.grabbing.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.grabbing.FlightTicketGrabbingDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.grabbing.GrabbingInfo;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultFlightTicketGrabbingDeligator extends BaseDeligator implements FlightTicketGrabbingDeligator {
    private static final Logger log = LoggerFactory.getLogger(DefaultFlightTicketGrabbingDeligator.class);

    public DefaultFlightTicketGrabbingDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }


    @Override
    public GrabbingInfo queryFlightTicketGrabFeeInfoByOrderId(String orderId) {
        final String url = String.format("%s/external/sys/api/flightTicketGrabbing/queryGrabFee?orderId=%s", this.getCfesagHost(), orderId);
        ResponseEntity<BaseResult<GrabbingInfo>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<GrabbingInfo>>() {
        });
        final BaseResult<GrabbingInfo> result = checkResponse(response);
        return result.getData();
    }
}
