package com.huoli.ctar.cfesag.client.deligator.oyh;


import com.huoli.ctar.cfesag.external.sys.api.oyh.model.OYHDftOrderMsg;
import com.huoli.ctar.cfesag.external.sys.api.oyh.model.YuHongNofifResult;
import com.huoli.ctar.cfesag.external.sys.api.oyh.model.bill.OYHSettlementBill;
import com.huoli.ctar.core.infra.model.BaseResult;

public interface YuHongFlightOrderDeligator{

    YuHongNofifResult yuHongflightOrderNotif(OYHDftOrderMsg msg);

    BaseResult<String> syncOYHSettlementBill(OYHSettlementBill bill);
}
