package com.huoli.ctar.cfesag.client.deligator.wechat.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.wechat.WechatDeligator;
import com.huoli.ctar.cfesag.external.sys.api.wechat.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-11-19 15:48
 */
@Slf4j
public class DefaultWechatDeligator extends BaseDeligator implements WechatDeligator {

    public DefaultWechatDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public SuiteAccessTokenResult getSuiteAccessToken(SuiteTokenParam suiteTokenParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity<SuiteTokenParam> httpEntity = new HttpEntity<>(suiteTokenParam, httpHeaders);
        String url = String.format("%s/external/sys/api/wechat/suite_access_token", this.getCfesagHost());
        ResponseEntity<BaseResult<SuiteAccessTokenResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<SuiteAccessTokenResult>>() {
        });
        final BaseResult<SuiteAccessTokenResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PreAuthCodeResult getPreAuthCode(String suiteAccessToken) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity httpEntity = new HttpEntity<>(httpHeaders);
        String url = String.format("%s/external/sys/api/wechat/pre_auth_code?suiteAccessToken=%s", this.getCfesagHost(), suiteAccessToken);
        ResponseEntity<BaseResult<PreAuthCodeResult>> response = execute(url, HttpMethod.GET, httpEntity, new ParameterizedTypeReference<BaseResult<PreAuthCodeResult>>() {
        });
        final BaseResult<PreAuthCodeResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CorpPermanentCodeResult getPermanentCode(String suiteAccessToken, PermanentCodeParam permanentCodeParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity<PermanentCodeParam> httpEntity = new HttpEntity<>(permanentCodeParam, httpHeaders);
        String url = String.format("%s/external/sys/api/wechat/permanent_code?suiteAccessToken=%s", this.getCfesagHost(), suiteAccessToken);
        ResponseEntity<BaseResult<CorpPermanentCodeResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CorpPermanentCodeResult>>() {
        });
        final BaseResult<CorpPermanentCodeResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CorpAccessTokenResult getCorpToken(final String suiteAccessToken,
                                              final CorpTokenParam corpTokenParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity<CorpTokenParam> httpEntity = new HttpEntity<>(corpTokenParam, httpHeaders);
        String url = String.format("%s/external/sys/api/wechat/corp_token?suiteAccessToken=%s", this.getCfesagHost(), suiteAccessToken);
        ResponseEntity<BaseResult<CorpAccessTokenResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CorpAccessTokenResult>>() {
        });
        final BaseResult<CorpAccessTokenResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public WechatBaseResult setSessionInfo(final String suiteAccessToken,
                                           final SessionInfoParam sessionInfoParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity<SessionInfoParam> httpEntity = new HttpEntity<>(sessionInfoParam, httpHeaders);
        String url = String.format("%s/external/sys/api/wechat/set_session_info?suiteAccessToken=%s", this.getCfesagHost(), suiteAccessToken);
        ResponseEntity<BaseResult<WechatBaseResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<WechatBaseResult>>() {
        });
        final BaseResult<WechatBaseResult> result = checkResponse(response);
        return result.getData();
    }
}
