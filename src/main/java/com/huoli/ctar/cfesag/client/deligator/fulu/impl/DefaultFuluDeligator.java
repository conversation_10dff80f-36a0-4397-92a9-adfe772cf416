package com.huoli.ctar.cfesag.client.deligator.fulu.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.fulu.FuluDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.booking.impl.DefaultResaleBookingDeligator;
import com.huoli.ctar.cfesag.external.sys.api.fulu.model.model.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

public class DefaultFuluDeligator extends BaseDeligator implements FuluDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultFuluDeligator.class);

    public DefaultFuluDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public FuluCreateDirectOrderResult createDirectOrder(FuluCreateDirectOrderParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<FuluCreateDirectOrderParam> httpEntity = new HttpEntity<>(param, httpHeaders);

        final String url = this.getCfesagHost() + "/external/sys/api/fulu/createDirectOrder";
        ResponseEntity<BaseResult<FuluCreateDirectOrderResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FuluCreateDirectOrderResult>>() {
        }, true);
        final BaseResult<FuluCreateDirectOrderResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public FuluCreateCarmiOrderResult createCarmiOrder(FuluCreateCarmiOrderParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<FuluCreateCarmiOrderParam> httpEntity = new HttpEntity<>(param, httpHeaders);

        final String url = this.getCfesagHost() + "/external/sys/api/fulu/createCarmiOrder";
        ResponseEntity<BaseResult<FuluCreateCarmiOrderResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FuluCreateCarmiOrderResult>>() {
        }, true);
        final BaseResult<FuluCreateCarmiOrderResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public FuluGoodsDetailResult queryGoodsDetail(FuluGoodsParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<FuluGoodsParam> httpEntity = new HttpEntity<>(param, httpHeaders);

        final String url = this.getCfesagHost() + "/external/sys/api/fulu/queryGoodsDetail";
        ResponseEntity<BaseResult<FuluGoodsDetailResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FuluGoodsDetailResult>>() {
        }, true);
        final BaseResult<FuluGoodsDetailResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<FuluGoodsBaseInfoResult> queryFuluAllGoods() {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity httpEntity = new HttpEntity<>(null, httpHeaders);

        final String url = this.getCfesagHost() + "/external/sys/api/fulu/queryAllGoods";
        ResponseEntity<BaseResult<List<FuluGoodsBaseInfoResult>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<FuluGoodsBaseInfoResult>>>() {
        }, true);
        final BaseResult<List<FuluGoodsBaseInfoResult>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public FuluOrderResult queryFuluOrderDetail(FuluOrderParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<FuluOrderParam> httpEntity = new HttpEntity<>(param, httpHeaders);

        final String url = this.getCfesagHost() + "/external/sys/api/fulu/queryOrderDetail";
        ResponseEntity<BaseResult<FuluOrderResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FuluOrderResult>>() {
        }, true);
        final BaseResult<FuluOrderResult> result = checkResponse(response);
        return result.getData();
    }
}
