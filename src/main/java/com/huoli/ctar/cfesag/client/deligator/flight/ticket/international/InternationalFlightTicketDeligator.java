package com.huoli.ctar.cfesag.client.deligator.flight.ticket.international;

import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.IftCorpOrderRecord;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.common.model.PageFlightTicketResult;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.common.model.TmcFlightTicketOrderMobileVO;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.international.model.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;

import java.util.List;

public interface InternationalFlightTicketDeligator {

    PageWrapper<List<InternationalFlightTicketOrder>> loadInternationalTicketOrders(InternationalTicketOrderQueryParams internationalTicketOrderQueryParams);

    InternationalFlightTicketTransFlowRecord getInternationalFlightTicketTransFlowRecord(final String orderId);

    PageWrapper<List<InternationalFlightTicketTransFlowRecord>> getInternationalFlightTicketTransFlowList(InternationalFlightTicketTransFlowRecordQueryParam internationalFlightTicketTransFlowRecordQueryParam);

    PageWrapper<List<InFlightTicketRealTimeOrderVO>> queryInFlightRealTimeOrderInfo(InFlightTicketRealTimeOrderQueryParam queryParam);

    InFlightTicketRealTimeOrderDetailVO queryInFlightRealTimeOrderDetail(String orderId);

    PageWrapper<List<InFlightTicketRealTimeOrderReportVO>> queryInFlightRealTimeOrderInfoForReport(Long corpId, String startTime, String endTime, Integer pageNo, Integer pageSize);

    InternationalFlightTicketOrderTripInfo queryInFlightTicketOrderTripInfo(String orderId, String payOrderId);

    InFlightTicketOrderCreateResult createInFlightTicketOrder(String postBody);

    IntFlightOrderAppDetailResultVo queryAppOrderDetail(String orderId, String phoneId, String p);

    IntFlightOrderStatusProcessResultVo queryIntOrderStatusProcess(String orderId, String phoneId);

    BaseResult<IntFlightJourneyBookSureVO> queryJourneyBookSureOrderInfo(InFlightJourneyBookSureQueryParam queryParam);

    IntFlightOrderPriceDetailResultVo queryOrderPriceDetail(String orderId, String phoneId);

    PageFlightTicketResult<List<TmcFlightTicketOrderMobileVO>> queryTmcIntFlightTicketOrders(String userId,
                                                                                             String orderId,
                                                                                             String passengerName,
                                                                                             String depCity,
                                                                                             String arrCity,
                                                                                             String flyNo,
                                                                                             String groupType,
                                                                                             Boolean isGetAll,
                                                                                             Integer maxCount,
                                                                                             Boolean isNeedPage,
                                                                                             Integer pageSize,
                                                                                             Integer pageNum,
                                                                                             String p,
                                                                                             String effectiveOrderFlag,
                                                                                             String bizOrderFlag);

    CorpOrderRecordsWrapper<IftCorpOrderRecord> queryIftCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam);

    String cancelOrder(IftCancelOrderParam param);
}
