package com.huoli.ctar.cfesag.client.deligator.payment.account;

import com.huoli.ctar.cfesag.external.sys.api.payment.account.model.*;

import java.util.List;

public interface PaymentAccountSysDeligator {
    String accountRechargeBalance(final AccountRechargeRequest requestParam);

    CouponListResult queryUserCouponList(final CouponListParam param);

    List<MemberDetailResult> queryMemberDetail(final MemberDetailRequest memberDetailRequest);

    List<ConsumeDetailResult> queryConsumeDetail(final ConsumeDetailRequest consumeDetailRequest);

    String userReChargeBalance(final RechargeBalanceRequest rechargeBalanceRequest);
}
