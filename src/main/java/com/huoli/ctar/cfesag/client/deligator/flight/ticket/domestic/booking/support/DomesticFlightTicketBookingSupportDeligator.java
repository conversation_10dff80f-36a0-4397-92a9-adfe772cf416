package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.support;

import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.booking.support.model.DomesticFlightTicketTransFlowRecord;

import java.util.List;

public interface DomesticFlightTicketBookingSupportDeligator {

    List<DomesticFlightTicketTransFlowRecord> loadDomesticFlightTicketTransFlowRecords(final String corpId, final String orderId, final Long transTimeFrom, final Long transTimeTo);

}
