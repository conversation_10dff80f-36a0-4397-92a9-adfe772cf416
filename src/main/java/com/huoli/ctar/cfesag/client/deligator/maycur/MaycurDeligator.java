package com.huoli.ctar.cfesag.client.deligator.maycur;

import com.huoli.ctar.cfesag.external.sys.api.maycur.model.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface MaycurDeligator {

    String authorize(final MaycurAuthRequest maycurAuthRequest);

    String pushFlightTicketOrderInfo(@RequestBody final List<MaycurOrder> orders, final String accessToken);

    String companyAuthorized(MaycurCorpInfoParam maycurCorpInfoParam);


    MaycurTrvlPlatformToken platformAuthLogin();

    String notifyPlatformCorpOpenStatus(final MaycurCorpCreateResultParam corpCreateResultParam,
                                        final String tokenId);
}
