package com.huoli.ctar.cfesag.client.deligator.zhongtai.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.zhongtai.HuoliZhongtaiDeligator;
import com.huoli.ctar.cfesag.external.sys.api.zhongtai.vo.MerchantInfoQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.zhongtai.vo.MerchantInfoZhongtaiVO;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultHuoliZhongtaiDeligator
        extends BaseDeligator
        implements HuoliZhongtaiDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultHuoliZhongtaiDeligator.class);

    public DefaultHuoliZhongtaiDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public MerchantInfoZhongtaiVO queryMerchantInfo(final MerchantInfoQueryParam queryParam) {
        final HttpHeaders headers = constructCommonHeaders();
        HttpEntity<MerchantInfoQueryParam> httpEntity = new HttpEntity<>(queryParam, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/huoli/zhongtai/merchantInfo/query");
        final ResponseEntity<BaseResult<MerchantInfoZhongtaiVO>> responseData = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<MerchantInfoZhongtaiVO>>() {});
        final  BaseResult<MerchantInfoZhongtaiVO> result = checkResponse(responseData);
        return result.getData();
    }
}
