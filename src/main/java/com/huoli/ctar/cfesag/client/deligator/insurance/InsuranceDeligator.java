package com.huoli.ctar.cfesag.client.deligator.insurance;

import com.huoli.ctar.cfesag.external.sys.api.insurance.model.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface InsuranceDeligator {

    List<XInsuranceOrderInfo> queryInsuranceOrderInfos(final XInsuranceOrderQueryReq queryReq);

    List<InsuranceTransFlowRecord> loadInsuranceTransFlowRecords(final String corpId,
                                                                 final Long transTimeFrom,
                                                                 final Long transTimeTo);

    InsuranceCorpOrderVO queryInsuranceCorpOrder(InsuranceCorpOrderParam insuranceCorpOrderParam);

    List<DftInsureProductCanby> queryFlightAttachProduct(DftInsureProductParam param);

    DftnsureProductResult queryCorpAttachProduct(CorpInsureParam param);
}
