package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.channel.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.channel.DomesticFlightTicketChannelDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.channel.model.UatpInfoCreateRequest;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.channel.model.UatpInfoUpdateRequest;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultDomesticFlightTicketChannelDeligator extends BaseDeligator implements DomesticFlightTicketChannelDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultDomesticFlightTicketChannelDeligator.class);

    public DefaultDomesticFlightTicketChannelDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String createUatpInfo(final UatpInfoCreateRequest uatpInfoCreateRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<UatpInfoCreateRequest> httpEntity = new HttpEntity<>(uatpInfoCreateRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/domesticFlightTicket/channel/uatpInfos/add";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String updateUatpInfo(final UatpInfoUpdateRequest uatpInfoUpdateRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<UatpInfoUpdateRequest> httpEntity = new HttpEntity<>(uatpInfoUpdateRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/domesticFlightTicket/channel/uatpInfos/update";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.PUT, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
}
