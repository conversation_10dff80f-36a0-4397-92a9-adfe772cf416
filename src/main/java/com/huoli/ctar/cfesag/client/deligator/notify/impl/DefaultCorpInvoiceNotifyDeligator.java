package com.huoli.ctar.cfesag.client.deligator.notify.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.notify.CorpInvoiceNotifyDeligator;
import com.huoli.ctar.cfesag.external.sys.api.notify.model.CooperatedInvoiceApplyMsg;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

@Slf4j
public class DefaultCorpInvoiceNotifyDeligator extends BaseDeligator implements CorpInvoiceNotifyDeligator {

    public DefaultCorpInvoiceNotifyDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Deprecated
    @Override
    public String corpInvoiceNotify(String corpCode, CooperatedInvoiceApplyMsg cooperatedInvoiceApplyMsg) {
        HttpHeaders headers = constructCommonHeaders();
        HttpEntity<CooperatedInvoiceApplyMsg> httpEntity = new HttpEntity<>(cooperatedInvoiceApplyMsg, headers);
        String url = String.format("%s%s", this.getCfesagHost(), String.format("/external/sys/api/%s/invoice/notify", corpCode));
        ResponseEntity<BaseResult<String>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        BaseResult<String> baseResult = checkResponse(result);
        return baseResult.getData();
    }
}
