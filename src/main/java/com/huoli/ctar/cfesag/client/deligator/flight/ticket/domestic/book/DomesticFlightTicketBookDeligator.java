package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.book;

import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.book.model.*;


public interface DomesticFlightTicketBookDeligator {
    String queryFlightHandleUrl(DomesticFlightTicketHandleUrlQueryParam param);

    /**
     * 预订舱位详情查询
     *
     * <AUTHOR>
     */
    DftBookDetailResult queryCabinDetail(DftQueryTicketSearchParam param);

    /**
     * tmc行程确认
     * @param param
     * @return
     */
    DftTmcFlightConfirmResult confirmFlight(DftFlightConfirmParam param);

    /**
     * 预订缓存查询
     *
     * <AUTHOR>
     */
    DftPreBookInfo queryBookCache(DftOrderSubmitParam param);

    /**
     * 提交预定
     *
     *
     * <AUTHOR>
     */
    DftOrderSubmitResult submit(DftOrderSubmitParam param);

    /**
     * 创单
     *
     *
     * <AUTHOR>
     */
    DftOrderSubmitResult createOrder(DftOrderSubmitParam param);

    /**
     * 创单
     *
     *
     * <AUTHOR>
     */
    DftOrderSubmitResult createOrderV1(DftOrderSubmitParam param);

    DftPrepayCheckResult corpPrepayCheck(DftOrderSubmitParam param);

    DftFlightListResult queryFlightList(DftFlightListParam param);
}
