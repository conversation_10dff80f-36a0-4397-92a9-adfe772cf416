package com.huoli.ctar.cfesag.client.deligator.post.service.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.post.service.PostServiceDeligator;
import com.huoli.ctar.cfesag.external.sys.api.post.service.model.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultPostServiceDeligator extends BaseDeligator implements PostServiceDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultPostServiceDeligator.class);

    public DefaultPostServiceDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public TicketPostResult getPostInfoV2(String postId) {
        final String url = String.format("%s/external/sys/api/post/service/back-side/post-info/v2?postId=%s", this.getCfesagHost(), postId);

        ResponseEntity<BaseResult<TicketPostResult>> response = execute(url, HttpMethod.GET, null,
                new ParameterizedTypeReference<BaseResult<TicketPostResult>>() {
                });

        final BaseResult<TicketPostResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public void cancelTrip(CancelTripParam cancelTripParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CancelTripParam> httpEntity = new HttpEntity<>(cancelTripParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/post/service/back-side/cancel-trip";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        checkResponse(response);
    }

    @Override
    public void invalidInvoice(InvalidInvoiceParam invalidInvoiceParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<InvalidInvoiceParam> httpEntity = new HttpEntity<>(invalidInvoiceParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/post/service/back-side/invalid-invoice";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        checkResponse(response);
    }

    @Override
    public void revokePackage(RevokePackageParam revokePackageParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<RevokePackageParam> httpEntity = new HttpEntity<>(revokePackageParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/post/service/back-side/revoke-package";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        checkResponse(response);
    }

    @Override
    public void updateEmail(UpdateEmailParam updateEmailParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<UpdateEmailParam> httpEntity = new HttpEntity<>(updateEmailParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/post/service/back-side/update-email";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        checkResponse(response);
    }

    @Override
    public void postHang(HangPostPkgParam hangPostPkgParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<HangPostPkgParam> httpEntity = new HttpEntity<>(hangPostPkgParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/post/service/back-side/hang";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        checkResponse(response);
    }

    @Override
    public void update(UpdatePostPkgParam updatePostPkgParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<UpdatePostPkgParam> httpEntity = new HttpEntity<>(updatePostPkgParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/post/service/back-side/update";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        checkResponse(response);
    }

    @Override
    public void removeDetail(PostCancelParam postCancelParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PostCancelParam> httpEntity = new HttpEntity<>(postCancelParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/post/service/back-side/remove-detail";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        checkResponse(response);
    }

    @Override
    public TicketConfirmResult corpApplyConfirm(PostConfirmParam postConfirmParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PostConfirmParam> httpEntity = new HttpEntity<>(postConfirmParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/post/service/corp/apply-confirm";

        ResponseEntity<BaseResult<TicketConfirmResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TicketConfirmResult>>() {
        });
        final BaseResult<TicketConfirmResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public TicketConfirmResult corpApplyConfirmV2(PostConfirmParam postConfirmParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PostConfirmParam> httpEntity = new HttpEntity<>(postConfirmParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/post/service/corp/apply-confirm/v2";

        ResponseEntity<BaseResult<TicketConfirmResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TicketConfirmResult>>() {
        });
        final BaseResult<TicketConfirmResult> result = checkResponse(response);
        return result.getData();
    }
}
