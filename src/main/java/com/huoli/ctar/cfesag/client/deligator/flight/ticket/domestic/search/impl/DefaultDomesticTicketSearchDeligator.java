package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.search.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.search.DomesticTicketSearchDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.search.model.DftPriceTrendResult;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.search.model.DftQueryPriceTrendParam;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.client.RestTemplate;


public class DefaultDomesticTicketSearchDeligator extends BaseDeligator implements DomesticTicketSearchDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultDomesticTicketSearchDeligator.class);

    public DefaultDomesticTicketSearchDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public DftPriceTrendResult queryPriceTrend(@RequestBody DftQueryPriceTrendParam param){
        HttpHeaders httpHeaders = this.constructCommonHeaders();
        HttpEntity<DftQueryPriceTrendParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        String url = String.format("%s/external/sys/api/domesticTicket/search/flight/pricetrend", this.getCfesagHost());
        ResponseEntity<BaseResult<DftPriceTrendResult>> response = this.execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DftPriceTrendResult>>() {
        });
        BaseResult<DftPriceTrendResult> result = this.checkResponse(response);
        return result.getData();
    }
}
