package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter;

import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.IftResaleOrderConsumption;

public interface InternationalFlightTicketOrderCenterDeligator {

    CorpResaleOrderRecsWrapper<IftResaleOrderConsumption> queryIftCorpResaleOrderRecs(final CorpResaleOrderRecsQueryParam queryParam);
}
