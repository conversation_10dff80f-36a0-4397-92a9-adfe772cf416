package com.huoli.ctar.cfesag.client.deligator.post.service;

import com.huoli.ctar.cfesag.external.sys.api.post.service.model.*;

public interface PostServiceDeligator {

    TicketPostResult getPostInfoV2(String postId);

    void cancelTrip(CancelTripParam cancelTripParam);

    void invalidInvoice(InvalidInvoiceParam invalidInvoiceParam);

    void revokePackage(RevokePackageParam revokePackageParam);

    void updateEmail(UpdateEmailParam updateEmailParam);

    void postHang(HangPostPkgParam hangPostPkgParam);

    void update(UpdatePostPkgParam updatePostPkgParam);

    void removeDetail(PostCancelParam postCancelParam);

    TicketConfirmResult corpApplyConfirm(PostConfirmParam postConfirmParam);

    TicketConfirmResult corpApplyConfirmV2(PostConfirmParam postConfirmParam);

}

