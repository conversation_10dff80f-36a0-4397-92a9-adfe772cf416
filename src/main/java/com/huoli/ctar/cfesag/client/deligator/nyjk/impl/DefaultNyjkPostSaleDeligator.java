package com.huoli.ctar.cfesag.client.deligator.nyjk.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.nyyh.impl.DefaultNyyhPostSaleDeligator;
import com.huoli.ctar.cfesag.client.exception.CfesagInvokeException;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.cfesag.client.deligator.nyjk.NyjkPostSaleDeligator;
import com.huoli.ctar.cfesag.external.sys.api.invoice.model.InvoiceBatchDto;
import com.huoli.ctar.cfesag.external.sys.api.nyyh.model.NyyhPostSaleProcessParam;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.util.TraceContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;

@Slf4j
public class DefaultNyjkPostSaleDeligator extends BaseDeligator implements NyjkPostSaleDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultNyyhPostSaleDeligator.class);

    public DefaultNyjkPostSaleDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String nyjkCallback(NyyhPostSaleProcessParam param) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());

            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/nyjk/postSale/reconciliation/sendback");

            HttpEntity<NyyhPostSaleProcessParam> httpEntity = new HttpEntity<>(param,headers);
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<String>>() {
            });
            BaseResult<String> result = checkResponse(response);
            return String.valueOf(result.getCode());
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }
    protected <T> BaseResult<T> checkResponse(ResponseEntity<BaseResult<T>> response) {
        final HttpStatus.Series series = HttpStatus.Series.valueOf(response.getStatusCode());
        if (HttpStatus.Series.CLIENT_ERROR == series || HttpStatus.Series.SERVER_ERROR == series) {
            if (Objects.nonNull(response.getBody())) {
                log.info("------{}", response.getBody().getMsg());
                throw new CfesagInvokeException(response.getBody().getMsg());
            } else {
                throw new CfesagInvokeException(response.getStatusCode().getReasonPhrase());
            }
        }
        if (Objects.isNull(response.getBody())) {
            throw new CfesagInvokeException("网关服务器内部错误");
        }
        return response.getBody();
    }
}
