package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter;

import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpResaleOrderIdsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.DftCorpOrderRecord;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.DftResaleOrderConsumption;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.common.model.PageFlightTicketResult;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.common.model.TmcFlightTicketOrderMobileVO;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.ordercenter.model.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Date;
import java.util.List;

public interface DomesticFlightTicketOrderCenterDeligator {

    CorpDomesticFlightTicketOrderDetail getCorpDomesticFlightTicketOrderDetail(final String orderId);

    DomesticFlightOrderDetail getDomesticFlightTicketOrder(String orderId);

    PayRefundResult manuallyRefund(PayParam payParam);

    SurplusQueryResult orderSurplusQuery(SurplusQuery surplusQuery);

    PageWrapper<List<DomesticFlightRealTimeOrderVO>> queryDomesticFlightRealTimeOrderInfo(DomesticFlightRealTimeOrderQueryParam realTimeOrderQueryParam);

    DomesticFlightRealTimeOrderDetailVO queryDomesticFlightRealTimeOrderInfoByOrderId(String orderId);

    PageWrapper<List<DomesticFlightRealTimeOrderReportVO>> queryDomesticFlightRealTimeOrderInfoForReport(Long corpId, Date startTime, Date endTime, Integer pageNo, Integer pageSize);

    DomesticFlightTicketBookResult createDomesticFlightOrder(String orderFormData);

    DftOrderInfo queryDomesticFlightOrderMsgByOrderId(String orderId);

    PageFlightTicketResult<List<TmcFlightTicketOrderMobileVO>> queryTmcDomesticFlightTicketOrders(final String userId,
                                                                                                  final String orderId,
                                                                                                  final String passengerName,
                                                                                                  final String depCity,
                                                                                                  final String arrCity,
                                                                                                  final String flyNo,
                                                                                                  final String groupType,
                                                                                                  final Boolean isGetAll,
                                                                                                  final Integer maxCount,
                                                                                                  final Boolean isNeedPage,
                                                                                                  final Integer pageSize,
                                                                                                  final Integer pageNum,
                                                                                                  final String p,
                                                                                                  final String effectiveOrderFlag,
                                                                                                  final String bizOrderFlag);


    CorpOrderRecordsWrapper<DftCorpOrderRecord> queryDftCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam);

    CorpResaleOrderRecsWrapper<DftResaleOrderConsumption> queryDftCorpResaleOrderRecs(final CorpResaleOrderRecsQueryParam queryParam);

    DftCorpResaleOrderVO queryDFTTicketCorpResaleOrder(DftCorpResaleOrderQueryParam dftCorpResaleOrderQueryParam);

    /**
     * 验仓验价
     *
     * <AUTHOR>
     * @see <a href="http://192.168.3.248/order-center/order-doc/blob/master/book/items/%E9%AA%8C%E4%BB%B7.md"></a>
     */
    BaseResult<DftTicketVerifyResult> pricing(@RequestBody DftTicketVerifyParam param);
    /**
     * 验仓验价
     *
     * <AUTHOR>
     * @see <a href="https://git.133ec.com/flight-ticket/order/order-doc/blob/master/book/items/%E9%AA%8C%E4%BB%B7.md"></a>
     */
    DftTicketVerifyResult pricingV1(@RequestBody DftTicketVerifyParam param);
    /**
     * 提交订单
     *
     * <AUTHOR>
     * @see <a href="http://192.168.3.248/order-center/order-doc/blob/master/book/items/%E4%B8%8B%E5%8D%95.md"></a>
     */
    BaseResult<DftBookResult> book(@RequestBody DftBookParam param);
    /**
     * 提交订单
     *
     * <AUTHOR>
     * @see <a href="https://git.133ec.com/flight-ticket/order/order-doc/blob/master/book/items/%E4%B8%8B%E5%8D%95.md#productdetail"></a>
     */
    DftBookResult bookV1(@RequestBody DftBookParam param);

    /**
     * 查询填单页报销凭证信息填充信息v2
     *
     * <AUTHOR>
     * @see <a href="http://192.168.3.248/order-center/order-doc/blob/master/ticket/client/items/204811.md"></a>
     */
    DftVoucherFillResult queryVoucherFill2(DftVoucherFillParam param);

    String cancelOrder(DftCancelOrderParam param);

    List<DftOrderDetailInfoVO> queryDftOrderDetailInfo(DftOrderDetailParam param);

    DftOrderDetailResult getDftTmcOrderDetailInfo(String orderId);

    SeatStatusResult queryOrderSeatStatusV2(SubmitVerifyParam param);
    
    SeatStatusResult queryOrderSeatStatus(String orderid);

    DftOrderDetailInfoVO searchDftOrderInnerOrderDetail(String orderId);

    DftOrderRerouteResult clientRerouteSubmit(DftOrderRerouteParam param);

    DftOrderRefundResult clientRefund(DftOrderRefundParam param);

    CorpResaleOrderIdsWrapper queryDftCorpOrderIds(CorpOrderRecordsQueryParam queryParam);

    CorpResaleOrderIdsWrapper queryDftCorpResaleOrderIds(CorpResaleOrderRecsQueryParam queryParam);
}
