package com.huoli.ctar.cfesag.client.deligator.tools.busapi;


import com.huoli.ctar.cfesag.external.sys.api.tools.busapi.model.PostInfoRequest;
import com.huoli.ctar.cfesag.external.sys.api.tools.busapi.model.WorkOrderRequest;
import com.huoli.ctar.cfesag.external.sys.api.tools.model.TktReceiptInfo;
import com.huoli.ctar.cfesag.external.sys.api.tools.model.TktReceiptInfoQueryParam;

import java.util.List;

public interface BusApiDeligator {

    String savePostNew(PostInfoRequest postInfo);

    String createWorkOrder(WorkOrderRequest workOrderRequest);

    List<TktReceiptInfo> queryTktReceiptInfos(final List<String> ticketNos);
}
