package com.huoli.ctar.cfesag.client.deligator.insurance.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.insurance.InsuranceDeligator;
import com.huoli.ctar.cfesag.external.sys.api.insurance.model.*;
import com.huoli.ctar.common.utils.DisplayUtil;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Objects;

public class DefaultInsuranceDeligator extends BaseDeligator implements InsuranceDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultInsuranceDeligator.class);

    public DefaultInsuranceDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public List<XInsuranceOrderInfo> queryInsuranceOrderInfos(final XInsuranceOrderQueryReq queryReq) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<XInsuranceOrderQueryReq> httpEntity = new HttpEntity<>(queryReq, httpHeaders);
        final String url = String.format("%s/external/sys/api/insurance/x/orderInfos/query", this.getCfesagHost());
        ResponseEntity<BaseResult<List<XInsuranceOrderInfo>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<XInsuranceOrderInfo>>>() {});
        final BaseResult<List<XInsuranceOrderInfo>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<InsuranceTransFlowRecord> loadInsuranceTransFlowRecords(final String corpId,
                                                                        final Long transTimeFrom,
                                                                        final Long transTimeTo) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/insurance/insuranceTransFlowRecords?corpId=%s&transTimeFrom=%s&transTimeTo=%s",
                DisplayUtil.display(corpId),
                Objects.isNull(transTimeFrom) ? "" : String.valueOf(transTimeFrom),
                Objects.isNull(transTimeTo) ? "" : String.valueOf(transTimeTo));
        ResponseEntity<BaseResult<List<InsuranceTransFlowRecord>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<List<InsuranceTransFlowRecord>>>() {
        });
        final BaseResult<List<InsuranceTransFlowRecord>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public InsuranceCorpOrderVO queryInsuranceCorpOrder(InsuranceCorpOrderParam insuranceCorpOrderParam) {

        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<InsuranceCorpOrderParam> httpEntity = new HttpEntity<>(insuranceCorpOrderParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/insurance/queryInsuranceCorpOrder", this.getCfesagHost());
        ResponseEntity<BaseResult<InsuranceCorpOrderVO>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<InsuranceCorpOrderVO>>() {
        });
        final BaseResult<InsuranceCorpOrderVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<DftInsureProductCanby> queryFlightAttachProduct(DftInsureProductParam param){
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftInsureProductParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = String.format("%s/external/sys/api/insurance/flight/queryAttachProduct", this.getCfesagHost());
        ResponseEntity<BaseResult<List<DftInsureProductCanby>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<DftInsureProductCanby>>>() {
        });
        final BaseResult<List<DftInsureProductCanby>> result = checkResponseNotCheckCode(response);
        return result.getData();
    }

    @Override
    public DftnsureProductResult queryCorpAttachProduct(@RequestBody CorpInsureParam param){
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpInsureParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = String.format("%s/external/sys/api/insurance/flight/corp/attach", this.getCfesagHost());
        ResponseEntity<BaseResult<DftnsureProductResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DftnsureProductResult>>() {
        });
        final BaseResult<DftnsureProductResult> result = checkResponseNotCheckCode(response);
        return result.getData();
    }
}
