package com.huoli.ctar.cfesag.client.deligator.train;

import java.util.List;

import com.huoli.ctar.cfesag.external.sys.api.train.model.account.QPMemberActiveParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.QPMemberActiveResult;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.CancelQPOrderParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.CreateQPOrderParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.CreateQPOrderResult;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.GetQPOrderDetailParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.GetQPOrderDetailResult;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.GetQPOrderIdResult;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.GetSuccessOrderResultItem;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.QPPaymentDetailParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.QPPaymentDetailResult;

/**
 * Created by ZL on 2020/08/19
 */
public interface TrainQPDeligator {
    
    QPMemberActiveResult isActivedMember(QPMemberActiveParam param);
    
    GetQPOrderIdResult getQPOrderId(final String phoneid);
    
    CreateQPOrderResult createQPOrder(CreateQPOrderParam.QPOrderFormData param);
    
    GetQPOrderDetailResult getQPOrderDetail(GetQPOrderDetailParam param);
    
    String cancelQP(CancelQPOrderParam param);
    
    QPPaymentDetailResult getQPPaymentDetail(QPPaymentDetailParam param);

    List<GetSuccessOrderResultItem> getQPSuccessOrders();
}
