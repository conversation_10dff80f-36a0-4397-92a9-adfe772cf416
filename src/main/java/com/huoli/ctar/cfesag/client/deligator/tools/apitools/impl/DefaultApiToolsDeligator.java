package com.huoli.ctar.cfesag.client.deligator.tools.apitools.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.tools.apitools.ApiToolsDeligator;
import com.huoli.ctar.cfesag.external.sys.api.tools.apitools.model.PostUserAccount;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultApiToolsDeligator extends BaseDeligator implements ApiToolsDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultApiToolsDeligator.class);

    public DefaultApiToolsDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String mergePostUserAccount(PostUserAccount postUserAccount) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PostUserAccount> httpEntity = new HttpEntity<>(postUserAccount, httpHeaders);
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/post/postAction");

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });

        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
}
