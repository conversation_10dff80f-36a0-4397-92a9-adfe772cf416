package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.flightBiz.BizTicketOrderQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.flightBiz.BizTicketOrderResult;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

public class DefaultBizTicketOrderDeligator extends BaseDeligator implements BizTicketOrderDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultBizTicketOrderDeligator.class);

    public DefaultBizTicketOrderDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public List<BizTicketOrderResult> queryInfoByProduct(BizTicketOrderQueryParam queryParam) {
        final String url = String.format("%s/external/sys/api/flight-biz/ticket/order/queryInfoByProduct", this.getCfesagHost());
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity<BizTicketOrderQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        ResponseEntity<BaseResult<List<BizTicketOrderResult>>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<List<BizTicketOrderResult>>>() {
        });
        final BaseResult<List<BizTicketOrderResult>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<BizTicketOrderResult> queryInfoByChannel(BizTicketOrderQueryParam queryParam) {
        final String url = String.format("%s/external/sys/api/flight-biz/ticket/order/queryInfoByChannel", this.getCfesagHost());
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity<BizTicketOrderQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        ResponseEntity<BaseResult<List<BizTicketOrderResult>>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<List<BizTicketOrderResult>>>() {
        });
        final BaseResult<List<BizTicketOrderResult>> result = checkResponse(response);
        return result.getData();
    }
}
