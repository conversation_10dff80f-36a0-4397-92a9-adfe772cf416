package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.DomesticFlightTicketOrderSensitiveDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.ordercenter.model.OrderSensitiveInfoVO;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.ordercenter.model.OrderSensitiveQueryParam;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;


public class DefaultDomesticFlightTicketOrderSensitiveDeligator extends BaseDeligator implements DomesticFlightTicketOrderSensitiveDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultDomesticFlightTicketOrderSensitiveDeligator.class);

    public DefaultDomesticFlightTicketOrderSensitiveDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public OrderSensitiveInfoVO querySensitiveInfo(OrderSensitiveQueryParam orderSensitiveQueryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<OrderSensitiveQueryParam> httpEntity = new HttpEntity<>(orderSensitiveQueryParam, httpHeaders);
        ResponseEntity<BaseResult<OrderSensitiveInfoVO>> response;
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/base/inner/order/sensitive/passenger/query/v1", this.getCfesagHost());
        response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<OrderSensitiveInfoVO>>() {
        });
        final BaseResult<OrderSensitiveInfoVO> result = checkResponse(response);
        return result.getData();
    }
}
