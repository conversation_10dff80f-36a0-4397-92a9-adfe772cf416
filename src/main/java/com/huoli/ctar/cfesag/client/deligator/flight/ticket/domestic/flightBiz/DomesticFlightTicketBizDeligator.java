package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz;

import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.flightBiz.DftChannelInfo;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.flightBiz.FlightBizTicketInfo;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.flightBiz.TicketValidParam;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.flightBiz.TicketValidResult;
import com.huoli.ctar.core.infra.model.BaseResult;

import java.util.List;

public interface DomesticFlightTicketBizDeligator {

    FlightBizTicketInfo queryTicketStatus(final String orderId, final String ticketNo);

    List<DftChannelInfo> queryChannelInfo();

    /**
     * 查询渠道配置
     * @return
     */
    List<DftChannelInfo> queryChannelInfos();

    /**
     * 客票验真
     * @param param
     * @return
     */
    TicketValidResult ticketValid(final TicketValidParam param);
}
