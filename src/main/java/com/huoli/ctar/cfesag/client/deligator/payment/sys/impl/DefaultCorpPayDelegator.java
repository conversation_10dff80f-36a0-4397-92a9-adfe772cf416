package com.huoli.ctar.cfesag.client.deligator.payment.sys.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.sys.CorpPayDelegator;
import com.huoli.ctar.cfesag.external.sys.api.payment.sys.model.CorpPayRequestPlus;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultCorpPayDelegator extends BaseDeligator implements CorpPayDelegator {
    private static final Logger log = LoggerFactory.getLogger(DefaultCorpPayDelegator.class);

    public DefaultCorpPayDelegator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String corpPayPlus(final CorpPayRequestPlus corpPayRequestPlus) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpPayRequestPlus> httpEntity = new HttpEntity<>(corpPayRequestPlus, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/sys/corpPayPlus";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
}
