package com.huoli.ctar.cfesag.client.deligator.hotel;

import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.HotelCorpOrderRecord;
import com.huoli.ctar.cfesag.external.sys.api.common.model.TmcOrderFormDataParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.HotelResaleOrderConsumption;
import com.huoli.ctar.cfesag.external.sys.api.hotel.model.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;

import java.util.List;

public interface HotelDeligator {

    List<HotelProvinceDto> loadHotelCityList();

    List<HotelProvinceDto> loadTmcHotelCityList();

    HotelProvinceVO loadHotelProvinces();

    HotelCityDto queryHotelCityByCityName(final String cityName);

    HotelCityDto queryHotelCityByCityNameFuzzyMatching(final String cityName);

    HotelCityDto queryTmcHotelCityByCityNameFuzzyMatching(String cityName);

    HotelOrderDetailVO queryHotelOrderDetailByOrderId(String orderId);

    List<HotelTransFlowRecord> loadHotelTransFlowRecords(final String corpId,
                                                         final String orderId,
                                                         final Long transTimeFrom,
                                                         final Long transTimeTo);

    String queryHotelTmcUrl(HotelHandleUrlQueryParam param);

    PageWrapper<List<HotelRealTimeOrderDetailVO>> queryHotelRealTimeOrderInfo(HotelRealTimeOrderQueryParam queryParam);

    CreateHotelResult createHotelOrder(String orderFormData);
    
    CreateHotelResult tmcCreateHotelOrder(String orderFormData);

    HotelOrderListVO queryOrderList(HotelOrderListQueryParam param);

    OrderDetailVO handleHotelOrder(HotelOrderHandleParam param);

    CorpOrderRecordsWrapper<HotelCorpOrderRecord> queryHotelCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam);

    BaseResult<HotelOrderRespVO> hotelTmcCreateOrder(TmcCreateOrderReq param);

    HotelOrderSyncResp tmcSyncOrderStatus(HotelOrderSyncReq orderSyncReq);

    HotelDetailVO hotelDetail(String hotelCode);

    HotelCorpResaleOrderVO queryHotelCorpResaleOrderInfo(HotelCorpResaleOrderParam hotelCorpResaleOrderParam);

    TmcHotelOrderFormData parseHotelOrderFormData(TmcOrderFormDataParam formDataParam);

    TmcHotelCityData tmcHotelCityData(TmcHotelCityParam tmcHotelCityParam);

    CorpResaleOrderRecsWrapper<HotelResaleOrderConsumption> queryHotelResaleCorpOrderRecs(final CorpResaleOrderRecsQueryParam queryParam);
}
