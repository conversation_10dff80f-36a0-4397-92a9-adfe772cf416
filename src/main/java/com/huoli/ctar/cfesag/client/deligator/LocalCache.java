package com.huoli.ctar.cfesag.client.deligator;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.Objects;
import java.util.concurrent.*;

public class LocalCache {
    private static final ConcurrentHashMap<String, LocalCacheValue> LOCAL_CACHE_MAP = new ConcurrentHashMap<>();
    private static final ThreadFactory GUAVA_THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("cfesag-client-local-cache-gc-pool-").build();
    private static final ScheduledExecutorService CACHE_GC = Executors.newSingleThreadScheduledExecutor(GUAVA_THREAD_FACTORY);


    private LocalCache() {
    }

    public static void cacheValue(final String key,
                                  final Object value) {
        cacheValue(key, value, -1);
    }

    public static void cacheValue(final String key,
                                  final Object value,
                                  final long expireSeconds) {
        if (expireSeconds > 0L) {
            LOCAL_CACHE_MAP.put(key, LocalCacheValue.expire(value, expireSeconds));
            CACHE_GC.schedule(new LocalCacheScheduleTask(key), expireSeconds, TimeUnit.SECONDS);
        } else {
            LOCAL_CACHE_MAP.put(key, LocalCacheValue.unExpire(value));
        }
    }

    public static Object getCacheValue(final String key) {
        LocalCacheValue localCacheValue = LOCAL_CACHE_MAP.get(key);
        if (Objects.isNull(localCacheValue)) {
            return null;
        }

        if (localCacheValue.expired()) {
            return null;
        }
        return localCacheValue.getValue();
    }

    public static void remove(final String key) {
        LOCAL_CACHE_MAP.remove(key);
    }

    public static class LocalCacheScheduleTask implements Runnable {
        private String cacheKey;

        public LocalCacheScheduleTask(final String cacheKey) {
            this.cacheKey = cacheKey;
        }

        @Override
        public void run() {
            LocalCacheValue localCacheValue = LOCAL_CACHE_MAP.get(cacheKey);
            if (Objects.nonNull(localCacheValue) && localCacheValue.expired()) {
                LOCAL_CACHE_MAP.remove(cacheKey);
            }
        }
    }
}
