package com.huoli.ctar.cfesag.client;

import com.huoli.ctar.cfesag.client.callback.AppMsgPushProviderCallback;
import com.huoli.ctar.cfesag.client.callback.WeChatMsgPushProviderCallback;
import com.huoli.ctar.cfesag.client.deligator.agitech.TripNowApiDeligator;
import com.huoli.ctar.cfesag.client.deligator.basedata.cache.BaseDataCacheDeligator;
import com.huoli.ctar.cfesag.client.deligator.caihailing.CarHailingDeligator;
import com.huoli.ctar.cfesag.client.deligator.car.CarDeligator;
import com.huoli.ctar.cfesag.client.deligator.cosps.ReimbursementReceiptDeligator;
import com.huoli.ctar.cfesag.client.deligator.coupon.CouponDeligator;
import com.huoli.ctar.cfesag.client.deligator.distributor.TrainDistributorDeligator;
import com.huoli.ctar.cfesag.client.deligator.easypnp.EasypnpOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.feishu.FeiShuDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.coupon.FlightCouponDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.dynamics.svc.FlightDynamicsSvcDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.book.DomesticFlightTicketBookDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.DomesticFlightTicketBookingDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.support.DomesticFlightTicketBookingSupportDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.channel.DomesticFlightTicketChannelDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.dynamic.DomesticFlightTicketDynamicDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz.BizTicketOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz.DomesticFlightTicketBizDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.grabbing.FlightTicketGrabbingDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.DomesticFlightTicketOrderCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.DomesticFlightTicketOrderSensitiveDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.InternationalFlightTicketOrderCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.search.DomesticTicketSearchDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.viphall.DomesticFlightTicketVipHallDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.InternationalFlightTicketDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.InternationalFlightTicketSearchDeligator;
import com.huoli.ctar.cfesag.client.deligator.fulu.FuluDeligator;
import com.huoli.ctar.cfesag.client.deligator.hljx.HljxDeligator;
import com.huoli.ctar.cfesag.client.deligator.hotel.HotelDeligator;
import com.huoli.ctar.cfesag.client.deligator.insurance.InsuranceDeligator;
import com.huoli.ctar.cfesag.client.deligator.invoice.InvoiceDeligator;
import com.huoli.ctar.cfesag.client.deligator.mall.MallDeligator;
import com.huoli.ctar.cfesag.client.deligator.maycur.MaycurDeligator;
import com.huoli.ctar.cfesag.client.deligator.notify.CorpInvoiceNotifyDeligator;
import com.huoli.ctar.cfesag.client.deligator.nyjk.NyjkPostSaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.nyyh.NyyhPostSaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.oyh.YuHongFlightOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.oyh.YuHongHotelOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.account.PaymentAccountSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.gateway.PaymentGatewayDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.sys.CorpPayDelegator;
import com.huoli.ctar.cfesag.client.deligator.payment.sys.PaymentSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.pnr.PnrDeligator;
import com.huoli.ctar.cfesag.client.deligator.post.service.PostServiceDeligator;
import com.huoli.ctar.cfesag.client.deligator.pushcenter.PushCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.pushcenter.TrainTicketMsgPushDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.booking.ResaleBookingDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.gateway.ResaleGatewayDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.info.ResaleInfoDeligator;
import com.huoli.ctar.cfesag.client.deligator.settle.money.mgmt.SettleMoneyMgmtSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.sso.SSODeligator;
import com.huoli.ctar.cfesag.client.deligator.tools.apitools.ApiToolsDeligator;
import com.huoli.ctar.cfesag.client.deligator.tools.busapi.BusApiDeligator;
import com.huoli.ctar.cfesag.client.deligator.tourism.TourismDeligator;
import com.huoli.ctar.cfesag.client.deligator.tourism.TourismResaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainQPDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainQueryDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainTicketDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainUnifiedDeligator;
import com.huoli.ctar.cfesag.client.deligator.usercenter.UserCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.vetech.VetechDeligator;
import com.huoli.ctar.cfesag.client.deligator.vetech.invoice.VetechFCDeligator;
import com.huoli.ctar.cfesag.client.deligator.wechat.WechatDeligator;
import com.huoli.ctar.cfesag.client.deligator.xxl.job.admin.JobDeligator;
import com.huoli.ctar.cfesag.client.deligator.yonyou.YonYouTrvlDeligator;
import com.huoli.ctar.cfesag.client.deligator.zhongtai.HuoliZhongtaiDeligator;

public interface CfesagClient extends SSODeligator, ResaleGatewayDeligator, ResaleBookingDeligator,
        PaymentGatewayDeligator, PaymentSysDeligator, BaseDataCacheDeligator, InternationalFlightTicketDeligator, InternationalFlightTicketSearchDeligator,
        TrainTicketDeligator, CarDeligator, HotelDeligator, InvoiceDeligator, PushCenterDeligator, UserCenterDeligator, CouponDeligator, CarHailingDeligator, YuHongFlightOrderDeligator, EasypnpOrderDeligator, YuHongHotelOrderDeligator, TrainDistributorDeligator,
        DomesticFlightTicketBookingSupportDeligator, DomesticFlightTicketBookDeligator, DomesticFlightTicketBookingDeligator, DomesticFlightTicketChannelDeligator,
        DomesticFlightTicketOrderCenterDeligator, DomesticFlightTicketDynamicDeligator, DomesticFlightTicketVipHallDeligator, DomesticFlightTicketBizDeligator, BizTicketOrderDeligator, InsuranceDeligator, BusApiDeligator, ApiToolsDeligator, TrainTicketMsgPushDeligator, PostServiceDeligator,
        MaycurDeligator, FlightTicketGrabbingDeligator, ReimbursementReceiptDeligator, TrainQueryDeligator, TrainUnifiedDeligator, TrainQPDeligator, DomesticTicketSearchDeligator,
        PaymentAccountSysDeligator, CorpPayDelegator, ResaleInfoDeligator, FuluDeligator, HljxDeligator, JobDeligator, TourismDeligator, VetechDeligator, FeiShuDeligator, MallDeligator, PnrDeligator, NyyhPostSaleDeligator, NyjkPostSaleDeligator, WechatDeligator,
        InternationalFlightTicketOrderCenterDeligator, TourismResaleDeligator, YonYouTrvlDeligator, DomesticFlightTicketOrderSensitiveDeligator, CorpInvoiceNotifyDeligator, TripNowApiDeligator,
        HuoliZhongtaiDeligator, SettleMoneyMgmtSysDeligator, FlightCouponDeligator, FlightDynamicsSvcDeligator, VetechFCDeligator {

    String pushGtgjMsg(final AppMsgPushProviderCallback appMsgPushProviderCallback);

    @Deprecated
    String pushHbgjMsg(final AppMsgPushProviderCallback appMsgPushProviderCallback);

    String pushMsg(final AppMsgPushProviderCallback appMsgPushProviderCallback);

    String pushWeChatMsg(final WeChatMsgPushProviderCallback weChatMsgPushProviderCallback);
}
