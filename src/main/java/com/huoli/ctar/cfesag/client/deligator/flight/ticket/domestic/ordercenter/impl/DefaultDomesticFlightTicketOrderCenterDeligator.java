package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.DomesticFlightTicketOrderCenterDeligator;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpResaleOrderIdsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.DftCorpOrderRecord;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.DftResaleOrderConsumption;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.common.model.PageFlightTicketResult;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.common.model.TmcFlightTicketCommonSearchParams;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.common.model.TmcFlightTicketOrderMobileVO;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.ordercenter.model.*;
import com.huoli.ctar.common.utils.DisplayUtil;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.List;

public class DefaultDomesticFlightTicketOrderCenterDeligator extends BaseDeligator implements DomesticFlightTicketOrderCenterDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultDomesticFlightTicketOrderCenterDeligator.class);

    public DefaultDomesticFlightTicketOrderCenterDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public CorpDomesticFlightTicketOrderDetail getCorpDomesticFlightTicketOrderDetail(final String orderId) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/domesticFlightTicket/orderCenter/corpDomesticFlightTicketOrders/%s",
                DisplayUtil.display(orderId));

        ResponseEntity<BaseResult<CorpDomesticFlightTicketOrderDetail>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<CorpDomesticFlightTicketOrderDetail>>() {
        });
        final BaseResult<CorpDomesticFlightTicketOrderDetail> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DomesticFlightOrderDetail getDomesticFlightTicketOrder(final String orderId) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/domesticFlightTicket/orderCenter/domesticFlightTicketOrders/%s",
                DisplayUtil.display(orderId));
        ResponseEntity<BaseResult<DomesticFlightOrderDetail>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<DomesticFlightOrderDetail>>() {
        });
        final BaseResult<DomesticFlightOrderDetail> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PayRefundResult manuallyRefund(PayParam payParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PayParam> httpEntity = new HttpEntity<>(payParam, httpHeaders);
        ResponseEntity<BaseResult<PayRefundResult>> response;
        final String url = this.getCfesagHost() + "/external/sys/api/domesticFlightTicket/orderCenter/operation/inner/resale/appeal/v1";

        response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PayRefundResult>>() {
        });
        final BaseResult<PayRefundResult> result = checkResponse(response);
        return result.getData();
    }


    @Override
    public SurplusQueryResult orderSurplusQuery(SurplusQuery surplusQuery) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<SurplusQuery> httpEntity = new HttpEntity<>(surplusQuery, httpHeaders);
        ResponseEntity<BaseResult<SurplusQueryResult>> response;
        final String url = this.getCfesagHost() + "/external/sys/api/domesticFlightTicket/orderCenter/refund/inner/resale/fee/surplus/v1";
        response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<SurplusQueryResult>>() {
        });
        final BaseResult<SurplusQueryResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<DomesticFlightRealTimeOrderVO>> queryDomesticFlightRealTimeOrderInfo(DomesticFlightRealTimeOrderQueryParam realTimeOrderQueryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DomesticFlightRealTimeOrderQueryParam> httpEntity = new HttpEntity<>(realTimeOrderQueryParam, httpHeaders);
        ResponseEntity<BaseResult<PageWrapper<List<DomesticFlightRealTimeOrderVO>>>> response;
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/queryRealTimeOrderInfo", this.getCfesagHost());
        response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<DomesticFlightRealTimeOrderVO>>>>() {
        });
        final BaseResult<PageWrapper<List<DomesticFlightRealTimeOrderVO>>> result = checkResponse(response);

        return result.getData();
    }

    @Override
    public DomesticFlightRealTimeOrderDetailVO queryDomesticFlightRealTimeOrderInfoByOrderId(String orderId) {
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/queryRealTimeOrderDetail?orderId=%s", this.getCfesagHost(), orderId);
        ResponseEntity<BaseResult<DomesticFlightRealTimeOrderDetailVO>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<DomesticFlightRealTimeOrderDetailVO>>() {
        });
        final BaseResult<DomesticFlightRealTimeOrderDetailVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<DomesticFlightRealTimeOrderReportVO>> queryDomesticFlightRealTimeOrderInfoForReport(Long corpId, Date startTime, Date endTime, Integer pageNo, Integer pageSize) {
        DomesticFlightRealTimeOrderQueryParam realTimeOrderQueryParam = new DomesticFlightRealTimeOrderQueryParam();
        realTimeOrderQueryParam.setCorpId(corpId);
        realTimeOrderQueryParam.setStartTime(startTime);
        realTimeOrderQueryParam.setEndTime(endTime);
        realTimeOrderQueryParam.setPageNo(pageNo);
        realTimeOrderQueryParam.setPageSize(pageSize);

        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DomesticFlightRealTimeOrderQueryParam> httpEntity = new HttpEntity<>(realTimeOrderQueryParam, httpHeaders);
        ResponseEntity<BaseResult<PageWrapper<List<DomesticFlightRealTimeOrderReportVO>>>> response;
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/queryRealTimeOrderInfoForReport", this.getCfesagHost());
        response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<DomesticFlightRealTimeOrderReportVO>>>>() {
        });
        final BaseResult<PageWrapper<List<DomesticFlightRealTimeOrderReportVO>>> result = checkResponse(response);

        return result.getData();
    }

    @Override
    public DomesticFlightTicketBookResult createDomesticFlightOrder(String orderFormData) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<String> httpEntity = new HttpEntity<>(orderFormData, httpHeaders);
        String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/createOrder", this.getCfesagHost());
        ResponseEntity<BaseResult<DomesticFlightTicketBookResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DomesticFlightTicketBookResult>>() {
        });
        final BaseResult<DomesticFlightTicketBookResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftOrderInfo queryDomesticFlightOrderMsgByOrderId(String orderId) {
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/queryOrderMsg?orderId=%s", this.getCfesagHost(), orderId);
        ResponseEntity<BaseResult<DftOrderInfo>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<DftOrderInfo>>() {
        });
        final BaseResult<DftOrderInfo> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageFlightTicketResult<List<TmcFlightTicketOrderMobileVO>> queryTmcDomesticFlightTicketOrders(String userId,
                                                                                                         String orderId,
                                                                                                         String passengerName,
                                                                                                         String depCity,
                                                                                                         String arrCity,
                                                                                                         String flyNo,
                                                                                                         String groupType,
                                                                                                         Boolean isGetAll,
                                                                                                         Integer maxCount,
                                                                                                         Boolean isNeedPage,
                                                                                                         Integer pageSize,
                                                                                                         Integer pageNum,
                                                                                                         String p,
                                                                                                         String effectiveOrderFlag,
                                                                                                         String bizOrderFlag
    ) {
        TmcFlightTicketCommonSearchParams tmcFlightTicketCommonSearchParams = new TmcFlightTicketCommonSearchParams();
        tmcFlightTicketCommonSearchParams.setUserId(userId);
        tmcFlightTicketCommonSearchParams.setOrderId(orderId);
        tmcFlightTicketCommonSearchParams.setDepCity(depCity);
        tmcFlightTicketCommonSearchParams.setArrCity(arrCity);
        tmcFlightTicketCommonSearchParams.setPassengerName(passengerName);
        tmcFlightTicketCommonSearchParams.setFlyNo(flyNo);
        tmcFlightTicketCommonSearchParams.setGroupType(groupType);
        tmcFlightTicketCommonSearchParams.setIsGetAll(isGetAll);
        tmcFlightTicketCommonSearchParams.setMaxCount(maxCount);
        tmcFlightTicketCommonSearchParams.setIsNeedPage(isNeedPage);
        tmcFlightTicketCommonSearchParams.setPageSize(pageSize);
        tmcFlightTicketCommonSearchParams.setPageNum(pageNum);
        tmcFlightTicketCommonSearchParams.setP(p);
        tmcFlightTicketCommonSearchParams.setEffectiveOrderFlag(effectiveOrderFlag);
        tmcFlightTicketCommonSearchParams.setBizOrderFlag(bizOrderFlag);
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<TmcFlightTicketCommonSearchParams> httpEntity = new HttpEntity<>(tmcFlightTicketCommonSearchParams, httpHeaders);
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/tmc/flightTicketOrders", this.getCfesagHost());
        ResponseEntity<BaseResult<PageFlightTicketResult<List<TmcFlightTicketOrderMobileVO>>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageFlightTicketResult<List<TmcFlightTicketOrderMobileVO>>>>() {
        });
        final BaseResult<PageFlightTicketResult<List<TmcFlightTicketOrderMobileVO>>> result = checkResponse(response);
        return result.getData();
    }

    /*
     * 查询国内机票业务方企业订单消费数据
     */
    @Override
    public CorpOrderRecordsWrapper<DftCorpOrderRecord> queryDftCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpOrderRecordsQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/corpOrderRecords/query", this.getCfesagHost());
        ResponseEntity<BaseResult<CorpOrderRecordsWrapper<DftCorpOrderRecord>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CorpOrderRecordsWrapper<DftCorpOrderRecord>>>() {
        });
        final BaseResult<CorpOrderRecordsWrapper<DftCorpOrderRecord>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CorpResaleOrderRecsWrapper<DftResaleOrderConsumption> queryDftCorpResaleOrderRecs(CorpResaleOrderRecsQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpResaleOrderRecsQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/corpResaleOrderRecs/query", this.getCfesagHost());
        ResponseEntity<BaseResult<CorpResaleOrderRecsWrapper<DftResaleOrderConsumption>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CorpResaleOrderRecsWrapper<DftResaleOrderConsumption>>>() {
        });
        final BaseResult<CorpResaleOrderRecsWrapper<DftResaleOrderConsumption>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public BaseResult<DftTicketVerifyResult> pricing(@RequestBody DftTicketVerifyParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftTicketVerifyParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/book/pricing", this.getCfesagHost());
        ResponseEntity<BaseResult<DftTicketVerifyResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DftTicketVerifyResult>>() {
        });
        return checkResponse(response);
    }

    @Override
    public DftTicketVerifyResult pricingV1(@RequestBody DftTicketVerifyParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftTicketVerifyParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/book/pricing", this.getCfesagHost());
        ResponseEntity<BaseResult<DftTicketVerifyResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DftTicketVerifyResult>>() {
        });
        final BaseResult<DftTicketVerifyResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public BaseResult<DftBookResult> book(@RequestBody DftBookParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftBookParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/book/submit/v1", this.getCfesagHost());
        ResponseEntity<BaseResult<DftBookResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DftBookResult>>() {
        });
        return checkResponse(response);
    }

    @Override
    public DftBookResult bookV1(@RequestBody DftBookParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftBookParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/book/submit/v1", this.getCfesagHost());
        ResponseEntity<BaseResult<DftBookResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DftBookResult>>() {
        });
        final BaseResult<DftBookResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftVoucherFillResult queryVoucherFill2(DftVoucherFillParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftVoucherFillParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/book/fill/page/v2", this.getCfesagHost());
        ResponseEntity<BaseResult<DftVoucherFillResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DftVoucherFillResult>>() {
        });
        final BaseResult<DftVoucherFillResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String cancelOrder(DftCancelOrderParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftCancelOrderParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/book/cancelOrder", this.getCfesagHost());
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<DftOrderDetailInfoVO> queryDftOrderDetailInfo(DftOrderDetailParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftOrderDetailParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/search/inner/order/corp/list/v1", this.getCfesagHost());
        ResponseEntity<BaseResult<List<DftOrderDetailInfoVO>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<DftOrderDetailInfoVO>>>() {
        });
        final BaseResult<List<DftOrderDetailInfoVO>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftOrderDetailResult getDftTmcOrderDetailInfo(String orderId){
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/search/client/order/detail/tmc/v1?orderId=%s", this.getCfesagHost(), orderId);
        ResponseEntity<BaseResult<DftOrderDetailResult>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<DftOrderDetailResult>>() {
        });
        final BaseResult<DftOrderDetailResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftCorpResaleOrderVO queryDFTTicketCorpResaleOrder(DftCorpResaleOrderQueryParam dftCorpResaleOrderQueryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftCorpResaleOrderQueryParam> httpEntity = new HttpEntity<>(dftCorpResaleOrderQueryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/tmc/queryDFTTicketCorpResaleOrder", this.getCfesagHost());
        ResponseEntity<BaseResult<DftCorpResaleOrderVO>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DftCorpResaleOrderVO>>() {
        });
        final BaseResult<DftCorpResaleOrderVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public SeatStatusResult queryOrderSeatStatusV2(SubmitVerifyParam param){
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<SubmitVerifyParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/booking/client/book/verify/v2", this.getCfesagHost());
        ResponseEntity<BaseResult<SeatStatusResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<SeatStatusResult>>() {
        });
        final BaseResult<SeatStatusResult> result = checkResponse(response);
        return result.getData();
    }
    
    @Override
    public SeatStatusResult queryOrderSeatStatus(String orderId){
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<String> httpEntity = new HttpEntity<>(orderId, httpHeaders);
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/booking/client/book/verify/v1", this.getCfesagHost());
        ResponseEntity<BaseResult<SeatStatusResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<SeatStatusResult>>() {
        });
        final BaseResult<SeatStatusResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftOrderDetailInfoVO searchDftOrderInnerOrderDetail(String orderId) {
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/search/inner/order/detail/v1?orderId=%s", this.getCfesagHost(), orderId);
        ResponseEntity<BaseResult<DftOrderDetailInfoVO>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<DftOrderDetailInfoVO>>() {
        });
        final BaseResult<DftOrderDetailInfoVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftOrderRerouteResult clientRerouteSubmit(DftOrderRerouteParam param){
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftOrderRerouteParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/reroute/client/submit/v1", this.getCfesagHost());
        ResponseEntity<BaseResult<DftOrderRerouteResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DftOrderRerouteResult>>() {
        });
        final BaseResult<DftOrderRerouteResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftOrderRefundResult clientRefund(DftOrderRefundParam param){
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftOrderRefundParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/refund/client/ticket/submit/v2", this.getCfesagHost());
        ResponseEntity<BaseResult<DftOrderRefundResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DftOrderRefundResult>>() {
        });
        final BaseResult<DftOrderRefundResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CorpResaleOrderIdsWrapper queryDftCorpOrderIds(CorpOrderRecordsQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpOrderRecordsQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/corpOrderIds/query", this.getCfesagHost());
        ResponseEntity<BaseResult<CorpResaleOrderIdsWrapper>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CorpResaleOrderIdsWrapper>>() {
        });
        final BaseResult<CorpResaleOrderIdsWrapper> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CorpResaleOrderIdsWrapper queryDftCorpResaleOrderIds(CorpResaleOrderRecsQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpResaleOrderRecsQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/orderCenter/corpResaleOrderIds/query", this.getCfesagHost());
        ResponseEntity<BaseResult<CorpResaleOrderIdsWrapper>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CorpResaleOrderIdsWrapper>>() {
        });
        final BaseResult<CorpResaleOrderIdsWrapper> result = checkResponse(response);
        return result.getData();
    }
}
