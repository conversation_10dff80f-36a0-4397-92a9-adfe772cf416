package com.huoli.ctar.cfesag.client.config;

import com.huoli.ctar.cfesag.client.deligator.agitech.TripNowApiDeligator;
import com.huoli.ctar.cfesag.client.deligator.agitech.impl.DefaultTripNowApiDeligator;
import com.huoli.ctar.cfesag.client.deligator.basedata.cache.BaseDataCacheDeligator;
import com.huoli.ctar.cfesag.client.deligator.basedata.cache.impl.DefaultBaseDataCacheDeligator;
import com.huoli.ctar.cfesag.client.deligator.caihailing.CarHailingDeligator;
import com.huoli.ctar.cfesag.client.deligator.caihailing.impl.DefaultCarHailingDeligator;
import com.huoli.ctar.cfesag.client.deligator.car.CarDeligator;
import com.huoli.ctar.cfesag.client.deligator.car.impl.DefaultCarDeligator;
import com.huoli.ctar.cfesag.client.deligator.cosps.ReimbursementReceiptDeligator;
import com.huoli.ctar.cfesag.client.deligator.cosps.impl.DefaultReimbursementReceiptDeligator;
import com.huoli.ctar.cfesag.client.deligator.coupon.CouponDeligator;
import com.huoli.ctar.cfesag.client.deligator.coupon.impl.DefaultCouponDeligator;
import com.huoli.ctar.cfesag.client.deligator.distributor.TrainDistributorDeligator;
import com.huoli.ctar.cfesag.client.deligator.distributor.impl.DefaultTrainDistributorDeligator;
import com.huoli.ctar.cfesag.client.deligator.easypnp.EasypnpOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.easypnp.impl.DefaultEasypnpOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.feishu.FeiShuDeligator;
import com.huoli.ctar.cfesag.client.deligator.feishu.impl.DefaultFeiShuDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.coupon.FlightCouponDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.coupon.impl.DefaultFlightCouponDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.dynamics.svc.FlightDynamicsSvcDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.dynamics.svc.impl.DefaultFlightDynamicsSvcDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.book.DomesticFlightTicketBookDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.book.impl.DefaultDomesticFlightTicketBookDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.DomesticFlightTicketBookingDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.impl.DefaultDomesticFlightTicketBookingDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.support.DomesticFlightTicketBookingSupportDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.support.impl.DefaultDomesticFlightTicketBookingSupportDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.channel.DomesticFlightTicketChannelDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.channel.impl.DefaultDomesticFlightTicketChannelDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.dynamic.DomesticFlightTicketDynamicDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.dynamic.impl.DefaultDomesticFlightTicketDynamicDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz.BizTicketOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz.DefaultBizTicketOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz.DefaultDomesticFlightTicketBizDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz.DomesticFlightTicketBizDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.grabbing.FlightTicketGrabbingDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.grabbing.impl.DefaultFlightTicketGrabbingDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.DomesticFlightTicketOrderCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.DomesticFlightTicketOrderSensitiveDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.InternationalFlightTicketOrderCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.impl.DefaultDomesticFlightTicketOrderCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.impl.DefaultDomesticFlightTicketOrderSensitiveDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.impl.DefaultInternationalFlightTicketOrderCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.search.DomesticTicketSearchDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.search.impl.DefaultDomesticTicketSearchDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.viphall.DomesticFlightTicketVipHallDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.viphall.impl.DefaultDomesticFlightTicketVipHallDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.InternationalFlightTicketDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.InternationalFlightTicketSearchDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.impl.DefaultInternationalFlightTicketDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.impl.DefaultInternationalFlightTicketSearchDeligator;
import com.huoli.ctar.cfesag.client.deligator.fulu.impl.DefaultFuluDeligator;
import com.huoli.ctar.cfesag.client.deligator.hljx.HljxDeligator;
import com.huoli.ctar.cfesag.client.deligator.hljx.impl.DefaultHljxDeligator;
import com.huoli.ctar.cfesag.client.deligator.hotel.HotelDeligator;
import com.huoli.ctar.cfesag.client.deligator.hotel.impl.DefaultHotelDeligator;
import com.huoli.ctar.cfesag.client.deligator.insurance.InsuranceDeligator;
import com.huoli.ctar.cfesag.client.deligator.insurance.impl.DefaultInsuranceDeligator;
import com.huoli.ctar.cfesag.client.deligator.invoice.InvoiceDeligator;
import com.huoli.ctar.cfesag.client.deligator.invoice.impl.DefaultInvoiceDeligator;
import com.huoli.ctar.cfesag.client.deligator.mall.MallDeligator;
import com.huoli.ctar.cfesag.client.deligator.mall.impl.DefaultMallDeligator;
import com.huoli.ctar.cfesag.client.deligator.maycur.MaycurDeligator;
import com.huoli.ctar.cfesag.client.deligator.maycur.impl.DefaultMaycurDeligator;
import com.huoli.ctar.cfesag.client.deligator.notify.CorpInvoiceNotifyDeligator;
import com.huoli.ctar.cfesag.client.deligator.notify.impl.DefaultCorpInvoiceNotifyDeligator;
import com.huoli.ctar.cfesag.client.deligator.nyjk.NyjkPostSaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.nyjk.impl.DefaultNyjkPostSaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.nyyh.NyyhPostSaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.nyyh.impl.DefaultNyyhPostSaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.oyh.YuHongFlightOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.oyh.YuHongHotelOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.oyh.impl.DefaultYuHongFlightOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.oyh.impl.DefaultYuHongHotelOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.account.PaymentAccountSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.account.impl.DefaultPaymentAccountSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.gateway.PaymentGatewayDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.gateway.impl.DefaultPaymentGatewayDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.sys.CorpPayDelegator;
import com.huoli.ctar.cfesag.client.deligator.payment.sys.PaymentSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.sys.impl.DefaultCorpPayDelegator;
import com.huoli.ctar.cfesag.client.deligator.payment.sys.impl.DefaultPaymentSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.pnr.PnrDeligator;
import com.huoli.ctar.cfesag.client.deligator.pnr.impl.DefaultPnrDeligator;
import com.huoli.ctar.cfesag.client.deligator.post.service.PostServiceDeligator;
import com.huoli.ctar.cfesag.client.deligator.post.service.impl.DefaultPostServiceDeligator;
import com.huoli.ctar.cfesag.client.deligator.pushcenter.PushCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.pushcenter.TrainTicketMsgPushDeligator;
import com.huoli.ctar.cfesag.client.deligator.pushcenter.impl.DefaultPushCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.pushcenter.impl.DefaultTrainTicketMsgPushDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.booking.ResaleBookingDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.booking.impl.DefaultResaleBookingDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.gateway.ResaleGatewayDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.gateway.impl.DefaultResaleGatewayDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.info.ResaleInfoDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.info.impl.DefaultResaleInfoDeligator;
import com.huoli.ctar.cfesag.client.deligator.settle.money.mgmt.SettleMoneyMgmtSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.settle.money.mgmt.impl.DefaultSettleMoneyMgmtSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.sso.SSODeligator;
import com.huoli.ctar.cfesag.client.deligator.sso.impl.DefaultSSODeligator;
import com.huoli.ctar.cfesag.client.deligator.tools.apitools.ApiToolsDeligator;
import com.huoli.ctar.cfesag.client.deligator.tools.apitools.impl.DefaultApiToolsDeligator;
import com.huoli.ctar.cfesag.client.deligator.tools.busapi.BusApiDeligator;
import com.huoli.ctar.cfesag.client.deligator.tools.busapi.impl.DefaultBusApiDeligator;
import com.huoli.ctar.cfesag.client.deligator.tourism.TourismDeligator;
import com.huoli.ctar.cfesag.client.deligator.tourism.TourismResaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.tourism.impl.DefaultTourismDeligator;
import com.huoli.ctar.cfesag.client.deligator.tourism.impl.DefaultTourismResaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainQPDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainQueryDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainTicketDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainUnifiedDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.impl.DefaultTrainQPDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.impl.DefaultTrainQueryDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.impl.DefaultTrainTicketDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.impl.DefaultTrainUnifiedDeligator;
import com.huoli.ctar.cfesag.client.deligator.usercenter.UserCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.usercenter.impl.DefaultUserCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.vetech.VetechDeligator;
import com.huoli.ctar.cfesag.client.deligator.vetech.impl.DefaultVetechDeligator;
import com.huoli.ctar.cfesag.client.deligator.vetech.invoice.VetechFCDeligator;
import com.huoli.ctar.cfesag.client.deligator.vetech.invoice.impl.DefaultVetechFCDeligator;
import com.huoli.ctar.cfesag.client.deligator.wechat.WechatDeligator;
import com.huoli.ctar.cfesag.client.deligator.wechat.impl.DefaultWechatDeligator;
import com.huoli.ctar.cfesag.client.deligator.xxl.job.admin.JobDeligator;
import com.huoli.ctar.cfesag.client.deligator.xxl.job.admin.impl.DefaultJobDeligator;
import com.huoli.ctar.cfesag.client.deligator.yonyou.YonYouTrvlDeligator;
import com.huoli.ctar.cfesag.client.deligator.yonyou.impl.DefaultYonYouTrvlDeligator;
import com.huoli.ctar.cfesag.client.deligator.zhongtai.HuoliZhongtaiDeligator;
import com.huoli.ctar.cfesag.client.deligator.zhongtai.impl.DefaultHuoliZhongtaiDeligator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.client.RestTemplate;

@Configuration
@Import({RestTemplateConfiguration.class})
@EnableConfigurationProperties(CfesagClientConfigProperties.class)
public class DeligatorConfiguration {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private CfesagClientConfigProperties properties;

    @Bean
    public BaseDataCacheDeligator baseDataCacheDeligator() {
        return new DefaultBaseDataCacheDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public InternationalFlightTicketDeligator internationalFlightTicketDeligator() {
        return new DefaultInternationalFlightTicketDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public InternationalFlightTicketSearchDeligator internationalFlightTicketSearchDeligator() {
        return new DefaultInternationalFlightTicketSearchDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public PaymentGatewayDeligator paymentGatewayDeligator() {
        return new DefaultPaymentGatewayDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public CouponDeligator couponDeligator() {
        return new DefaultCouponDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public PaymentSysDeligator paymentSysDeligator() {
        return new DefaultPaymentSysDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public PaymentAccountSysDeligator paymentAccountSysDeligator() {
        return new DefaultPaymentAccountSysDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public CorpPayDelegator corpPayDelegator() {
        return new DefaultCorpPayDelegator(restTemplate, properties.getHost());
    }

    @Bean
    public ResaleBookingDeligator resaleBookingDeligator() {
        return new DefaultResaleBookingDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public ResaleGatewayDeligator resaleGatewayDeligator() {
        return new DefaultResaleGatewayDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public SSODeligator ssoDeligator() {
        return new DefaultSSODeligator(restTemplate, properties.getHost());
    }

    @Bean
    public TrainTicketDeligator trainTicketDeligator() {
        return new DefaultTrainTicketDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public CarDeligator carDeligator() {
        return new DefaultCarDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public CarHailingDeligator carHailingDeligator() {
        return new DefaultCarHailingDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public TrainDistributorDeligator trainDistributorDeligator() {
        return new DefaultTrainDistributorDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public YuHongFlightOrderDeligator yuHongFlightOrderDeligator() {
        return new DefaultYuHongFlightOrderDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public EasypnpOrderDeligator easypnpOrderDeligator() {
        return new DefaultEasypnpOrderDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public YuHongHotelOrderDeligator yuHongHotelOrderDeligator() {
        return new DefaultYuHongHotelOrderDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public NyyhPostSaleDeligator nyyhPostSaleDeligator() {
        return new DefaultNyyhPostSaleDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public NyjkPostSaleDeligator nyjkPostSaleDeligator() {
        return new DefaultNyjkPostSaleDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public HotelDeligator hotelDeligator() {
        return new DefaultHotelDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public InvoiceDeligator invoiceDeligator() {
        return new DefaultInvoiceDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public UserCenterDeligator userCenterDeligator() {
        return new DefaultUserCenterDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public PushCenterDeligator pushCenterDeligator() {
        return new DefaultPushCenterDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public DomesticFlightTicketBookingSupportDeligator domesticFlightTicketBookingSupportDeligator() {
        return new DefaultDomesticFlightTicketBookingSupportDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public DomesticFlightTicketBookingDeligator domesticFlightTicketBookingDeligator() {
        return new DefaultDomesticFlightTicketBookingDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public DomesticFlightTicketBookDeligator domesticFlightTicketBookDeligator() {
        return new DefaultDomesticFlightTicketBookDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public DomesticFlightTicketBizDeligator domesticFlightTicketBizDeligator() {
        return new DefaultDomesticFlightTicketBizDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public DomesticFlightTicketDynamicDeligator domesticFlightTicketDynamicDeligator() {
        return new DefaultDomesticFlightTicketDynamicDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public DomesticFlightTicketChannelDeligator domesticFlightTicketChannelDeligator() {
        return new DefaultDomesticFlightTicketChannelDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public DomesticFlightTicketOrderCenterDeligator domesticFlightTicketOrderCenterDeligator() {
        return new DefaultDomesticFlightTicketOrderCenterDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public DomesticFlightTicketVipHallDeligator domesticFlightTicketVipHallDeligator() {
        return new DefaultDomesticFlightTicketVipHallDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public DomesticTicketSearchDeligator domesticTicketSearchDeligator() {
        return new DefaultDomesticTicketSearchDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public BizTicketOrderDeligator bizTicketOrderDeligator() {
        return new DefaultBizTicketOrderDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public InsuranceDeligator insuranceDeligator() {
        return new DefaultInsuranceDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public BusApiDeligator busApiDeligator() {
        return new DefaultBusApiDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public ApiToolsDeligator apiToolsDeligator() {
        return new DefaultApiToolsDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public TrainTicketMsgPushDeligator trainTicketMsgPushDeligator() {
        return new DefaultTrainTicketMsgPushDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public PostServiceDeligator postServiceDeligator() {
        return new DefaultPostServiceDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public MaycurDeligator maycurDeligator() {
        return new DefaultMaycurDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public FlightTicketGrabbingDeligator flightTicketGrabbingDeligator() {
        return new DefaultFlightTicketGrabbingDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public ReimbursementReceiptDeligator reimbursementReceiptDeligator() {
        return new DefaultReimbursementReceiptDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public TrainQueryDeligator trainQueryDeligator() {
        return new DefaultTrainQueryDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public TrainUnifiedDeligator trainUnifiedDeligator() {
        return new DefaultTrainUnifiedDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public TrainQPDeligator trainQPDeligator() {
        return new DefaultTrainQPDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public ResaleInfoDeligator resaleInfoDeligator() {
        return new DefaultResaleInfoDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public DefaultFuluDeligator fuluDeligator() {
        return new DefaultFuluDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public HljxDeligator hljxDeligator() {
        return new DefaultHljxDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public JobDeligator jobDeligator() {
        return new DefaultJobDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public TourismDeligator tourismDeligator() {
        return new DefaultTourismDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public VetechDeligator vetechDeligator() {
        return new DefaultVetechDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public FeiShuDeligator feiShuDeligator() {
        return new DefaultFeiShuDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public WechatDeligator wechatDeligator() {
        return new DefaultWechatDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public MallDeligator mallDeligator() {
        return new DefaultMallDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public PnrDeligator pnrDeligator() {
        return new DefaultPnrDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public InternationalFlightTicketOrderCenterDeligator internationalFlightTicketOrderCenterDeligator() {
        return new DefaultInternationalFlightTicketOrderCenterDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public TourismResaleDeligator tourismResaleDeligator() {
        return new DefaultTourismResaleDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public YonYouTrvlDeligator yonYouTrvlDeligator() {
        return new DefaultYonYouTrvlDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public DomesticFlightTicketOrderSensitiveDeligator domesticFlightTicketOrderSensitiveDeligator() {
        return new DefaultDomesticFlightTicketOrderSensitiveDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public CorpInvoiceNotifyDeligator corpInvoiceNotifyDeligator() {
        return new DefaultCorpInvoiceNotifyDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public TripNowApiDeligator tripNowApiDeligator() {
        return new DefaultTripNowApiDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public HuoliZhongtaiDeligator huoliZhongtaiDeligator() {
        return new DefaultHuoliZhongtaiDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public SettleMoneyMgmtSysDeligator settleMoneyMgmtSysDeligator() {
        return new DefaultSettleMoneyMgmtSysDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public FlightCouponDeligator flightCouponDeligator() {
        return new DefaultFlightCouponDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public FlightDynamicsSvcDeligator flightDynamicsSvcDeligator() {
        return new DefaultFlightDynamicsSvcDeligator(restTemplate, properties.getHost());
    }

    @Bean
    public VetechFCDeligator vetechFCDeligator() {
        return new DefaultVetechFCDeligator(restTemplate, properties.getHost());
    }
}
