package com.huoli.ctar.cfesag.client;

import com.huoli.ctar.cfesag.client.callback.AppMsgPushProviderCallback;
import com.huoli.ctar.cfesag.client.callback.WeChatMsgPushProviderCallback;
import com.huoli.ctar.cfesag.client.deligator.agitech.TripNowApiDeligator;
import com.huoli.ctar.cfesag.client.deligator.basedata.cache.BaseDataCacheDeligator;
import com.huoli.ctar.cfesag.client.deligator.caihailing.CarHailingDeligator;
import com.huoli.ctar.cfesag.client.deligator.car.CarDeligator;
import com.huoli.ctar.cfesag.client.deligator.cosps.ReimbursementReceiptDeligator;
import com.huoli.ctar.cfesag.client.deligator.coupon.CouponDeligator;
import com.huoli.ctar.cfesag.client.deligator.distributor.TrainDistributorDeligator;
import com.huoli.ctar.cfesag.client.deligator.easypnp.EasypnpOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.feishu.FeiShuDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.coupon.FlightCouponDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.dynamics.svc.FlightDynamicsSvcDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.book.DomesticFlightTicketBookDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.DomesticFlightTicketBookingDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.support.DomesticFlightTicketBookingSupportDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.channel.DomesticFlightTicketChannelDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.dynamic.DomesticFlightTicketDynamicDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz.BizTicketOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz.DomesticFlightTicketBizDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.grabbing.FlightTicketGrabbingDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.DomesticFlightTicketOrderCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.DomesticFlightTicketOrderSensitiveDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.InternationalFlightTicketOrderCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.search.DomesticTicketSearchDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.viphall.DomesticFlightTicketVipHallDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.InternationalFlightTicketDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.InternationalFlightTicketSearchDeligator;
import com.huoli.ctar.cfesag.client.deligator.fulu.FuluDeligator;
import com.huoli.ctar.cfesag.client.deligator.hljx.HljxDeligator;
import com.huoli.ctar.cfesag.client.deligator.hotel.HotelDeligator;
import com.huoli.ctar.cfesag.client.deligator.insurance.InsuranceDeligator;
import com.huoli.ctar.cfesag.client.deligator.invoice.InvoiceDeligator;
import com.huoli.ctar.cfesag.client.deligator.mall.MallDeligator;
import com.huoli.ctar.cfesag.client.deligator.maycur.MaycurDeligator;
import com.huoli.ctar.cfesag.client.deligator.notify.CorpInvoiceNotifyDeligator;
import com.huoli.ctar.cfesag.client.deligator.nyjk.NyjkPostSaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.nyyh.NyyhPostSaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.oyh.YuHongFlightOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.oyh.YuHongHotelOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.account.PaymentAccountSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.gateway.PaymentGatewayDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.sys.CorpPayDelegator;
import com.huoli.ctar.cfesag.client.deligator.payment.sys.PaymentSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.pnr.PnrDeligator;
import com.huoli.ctar.cfesag.client.deligator.post.service.PostServiceDeligator;
import com.huoli.ctar.cfesag.client.deligator.pushcenter.PushCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.pushcenter.TrainTicketMsgPushDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.booking.ResaleBookingDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.gateway.ResaleGatewayDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.info.ResaleInfoDeligator;
import com.huoli.ctar.cfesag.client.deligator.settle.money.mgmt.SettleMoneyMgmtSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.sso.SSODeligator;
import com.huoli.ctar.cfesag.client.deligator.tools.apitools.ApiToolsDeligator;
import com.huoli.ctar.cfesag.client.deligator.tools.busapi.BusApiDeligator;
import com.huoli.ctar.cfesag.client.deligator.tourism.TourismDeligator;
import com.huoli.ctar.cfesag.client.deligator.tourism.TourismResaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainQPDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainQueryDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainTicketDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainUnifiedDeligator;
import com.huoli.ctar.cfesag.client.deligator.usercenter.UserCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.vetech.VetechDeligator;
import com.huoli.ctar.cfesag.client.deligator.vetech.invoice.VetechFCDeligator;
import com.huoli.ctar.cfesag.client.deligator.wechat.WechatDeligator;
import com.huoli.ctar.cfesag.client.deligator.xxl.job.admin.JobDeligator;
import com.huoli.ctar.cfesag.client.deligator.yonyou.YonYouTrvlDeligator;
import com.huoli.ctar.cfesag.client.deligator.zhongtai.HuoliZhongtaiDeligator;
import com.huoli.ctar.cfesag.client.exception.CfesagInvokeException;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.cfesag.external.sys.api.agitech.model.ChatParam;
import com.huoli.ctar.cfesag.external.sys.api.basedata.cache.model.DataResult;
import com.huoli.ctar.cfesag.external.sys.api.car.model.*;
import com.huoli.ctar.cfesag.external.sys.api.carhailing.model.CarhailingSelectInitModel;
import com.huoli.ctar.cfesag.external.sys.api.carhailing.model.CreateHailingOrderParam;
import com.huoli.ctar.cfesag.external.sys.api.carhailing.model.QueryOrderStatusModel;
import com.huoli.ctar.cfesag.external.sys.api.carhailing.vo.*;
import com.huoli.ctar.cfesag.external.sys.api.common.model.*;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.*;
import com.huoli.ctar.cfesag.external.sys.api.cosps.model.DftReimbursementReceiptRecordVO;
import com.huoli.ctar.cfesag.external.sys.api.cosps.model.ReimbursementReceiptPkgRecordVO;
import com.huoli.ctar.cfesag.external.sys.api.cosps.model.ReimbursementReceiptSummitRecordVO;
import com.huoli.ctar.cfesag.external.sys.api.distributor.model.*;
import com.huoli.ctar.cfesag.external.sys.api.distributor.vo.*;
import com.huoli.ctar.cfesag.external.sys.api.feishu.*;
import com.huoli.ctar.cfesag.external.sys.api.flight.coupon.model.*;
import com.huoli.ctar.cfesag.external.sys.api.flight.dymanics.svc.model.FlightBoardingInfo;
import com.huoli.ctar.cfesag.external.sys.api.flight.dymanics.svc.model.FlightBoardingInfoQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.common.model.PageFlightTicketResult;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.common.model.TmcFlightTicketOrderMobileVO;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.book.model.*;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.booking.model.DomesticFlightTicketRefundRecord;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.booking.support.model.DomesticFlightTicketTransFlowRecord;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.channel.model.UatpInfoCreateRequest;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.channel.model.UatpInfoUpdateRequest;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.dynamic.DynamicBaseParam;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.dynamic.DynamicDetailResult;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.flightBiz.*;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.grabbing.GrabbingInfo;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.ordercenter.model.*;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.search.model.DftPriceTrendResult;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.search.model.DftQueryPriceTrendParam;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.vipHall.model.VipHallResult;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.international.model.*;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.international.search.model.InFlightTicketHandleUrlQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.fulu.model.model.*;
import com.huoli.ctar.cfesag.external.sys.api.hljx.vo.TmcHLCommoditiesRequest;
import com.huoli.ctar.cfesag.external.sys.api.hljx.vo.TmcHLCommoditiesWrapper;
import com.huoli.ctar.cfesag.external.sys.api.hotel.model.*;
import com.huoli.ctar.cfesag.external.sys.api.insurance.model.*;
import com.huoli.ctar.cfesag.external.sys.api.invoice.model.*;
import com.huoli.ctar.cfesag.external.sys.api.maycur.model.*;
import com.huoli.ctar.cfesag.external.sys.api.notify.model.CooperatedInvoiceApplyMsg;
import com.huoli.ctar.cfesag.external.sys.api.nyyh.model.NyyhPostSaleProcessParam;
import com.huoli.ctar.cfesag.external.sys.api.oyh.model.CooperateHoterOrderMsg;
import com.huoli.ctar.cfesag.external.sys.api.oyh.model.OYHDftOrderMsg;
import com.huoli.ctar.cfesag.external.sys.api.oyh.model.YuHongNofifResult;
import com.huoli.ctar.cfesag.external.sys.api.oyh.model.bill.OYHSettlementBill;
import com.huoli.ctar.cfesag.external.sys.api.payment.account.model.*;
import com.huoli.ctar.cfesag.external.sys.api.payment.gateway.model.*;
import com.huoli.ctar.cfesag.external.sys.api.payment.sys.model.*;
import com.huoli.ctar.cfesag.external.sys.api.pnr.PnrInfo;
import com.huoli.ctar.cfesag.external.sys.api.pnr.PnrInfoQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.points.model.*;
import com.huoli.ctar.cfesag.external.sys.api.post.service.model.*;
import com.huoli.ctar.cfesag.external.sys.api.pushcenter.model.*;
import com.huoli.ctar.cfesag.external.sys.api.resale.booking.model.*;
import com.huoli.ctar.cfesag.external.sys.api.resale.booking.model.back.*;
import com.huoli.ctar.cfesag.external.sys.api.resale.booking.model.order.*;
import com.huoli.ctar.cfesag.external.sys.api.resale.booking.model.shelf.*;
import com.huoli.ctar.cfesag.external.sys.api.resale.gateway.model.ApiClient;
import com.huoli.ctar.cfesag.external.sys.api.resale.gateway.model.ApiClientRegisterRequest;
import com.huoli.ctar.cfesag.external.sys.api.resale.gateway.model.ApiClientUpdateRequest;
import com.huoli.ctar.cfesag.external.sys.api.resale.ticket.domestic.model.DftOrderBoardingInfo;
import com.huoli.ctar.cfesag.external.sys.api.resale.ticket.domestic.model.DftResaleInfoParam;
import com.huoli.ctar.cfesag.external.sys.api.resale.ticket.domestic.model.DftResaleInfoVO;
import com.huoli.ctar.cfesag.external.sys.api.settle.money.mgmt.CorpSettleBillExternalVO;
import com.huoli.ctar.cfesag.external.sys.api.sso.model.SSOUser;
import com.huoli.ctar.cfesag.external.sys.api.tools.apitools.model.PostUserAccount;
import com.huoli.ctar.cfesag.external.sys.api.tools.busapi.model.PostInfoRequest;
import com.huoli.ctar.cfesag.external.sys.api.tools.busapi.model.WorkOrderRequest;
import com.huoli.ctar.cfesag.external.sys.api.tools.model.TktReceiptInfo;
import com.huoli.ctar.cfesag.external.sys.api.train.model.*;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.*;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.*;
import com.huoli.ctar.cfesag.external.sys.api.usercenter.model.*;
import com.huoli.ctar.cfesag.external.sys.api.vetech.invoice.VetechInvInfoUploadReq;
import com.huoli.ctar.cfesag.external.sys.api.vetech.model.VetechOrder;
import com.huoli.ctar.cfesag.external.sys.api.vetech.model.VetechResult;
import com.huoli.ctar.cfesag.external.sys.api.vetech.model.VetechSyncInvoiceParam;
import com.huoli.ctar.cfesag.external.sys.api.wechat.*;
import com.huoli.ctar.cfesag.external.sys.api.xxl.job.admin.model.TriggerJobRequestParams;
import com.huoli.ctar.cfesag.external.sys.api.yonyou.YonYouPageWrapper;
import com.huoli.ctar.cfesag.external.sys.api.yonyou.YonYouSettlementOrder;
import com.huoli.ctar.cfesag.external.sys.api.yonyou.YonYouSettlementOrderQueryReq;
import com.huoli.ctar.cfesag.external.sys.api.zhongtai.vo.MerchantInfoQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.zhongtai.vo.MerchantInfoZhongtaiVO;
import com.huoli.ctar.core.infra.constant.AppName;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;

@Setter
public class DefaultCfesagClient implements CfesagClient {

    private static final Logger log = LoggerFactory.getLogger(DefaultCfesagClient.class);

    private TripNowApiDeligator tripNowApiDeligator;

    private SSODeligator ssoDeligator;

    private ResaleGatewayDeligator resaleGatewayDeligator;

    private ResaleBookingDeligator resaleBookingDeligator;

    private PaymentGatewayDeligator paymentGatewayDeligator;

    private PaymentSysDeligator paymentSysDeligator;

    private BaseDataCacheDeligator baseDataCacheDeligator;

    private InternationalFlightTicketDeligator internationalFlightTicketDeligator;

    private InternationalFlightTicketSearchDeligator internationalFlightTicketSearchDeligator;

    private TrainTicketDeligator trainTicketDeligator;

    private CarDeligator carDeligator;

    private CarHailingDeligator carHailingDeligator;

    private YuHongFlightOrderDeligator yuHongFlightOrderDeligator;

    private EasypnpOrderDeligator easypnpOrderDeligator;

    private YuHongHotelOrderDeligator yuHongHotelOrderDeligator;

    private NyyhPostSaleDeligator nyyhPostSaleDeligator;

    private NyjkPostSaleDeligator nyjkPostSaleDeligator;

    private HotelDeligator hotelDeligator;

    private InvoiceDeligator invoiceDeligator;

    private UserCenterDeligator userCenterDeligator;

    private PushCenterDeligator pushCenterDeligator;

    private DomesticFlightTicketBookingSupportDeligator domesticFlightTicketBookingSupportDeligator;

    private DomesticFlightTicketBookDeligator domesticFlightTicketBookDeligator;

    private DomesticFlightTicketBookingDeligator domesticFlightTicketBookingDeligator;

    private DomesticFlightTicketChannelDeligator domesticFlightTicketChannelDeligator;

    private DomesticFlightTicketOrderCenterDeligator domesticFlightTicketOrderCenterDeligator;

    private DomesticFlightTicketDynamicDeligator domesticFlightTicketDynamicDeligator;

    private DomesticFlightTicketVipHallDeligator domesticFlightTicketVipHallDeligator;

    private DomesticFlightTicketBizDeligator domesticFlightTicketBizDeligator;

    private BizTicketOrderDeligator bizTicketOrderDeligator;

    private InsuranceDeligator insuranceDeligator;

    private BusApiDeligator busApiDeligator;

    private ApiToolsDeligator apiToolsDeligator;

    private TrainTicketMsgPushDeligator trainTicketMsgPushDeligator;

    private PostServiceDeligator postServiceDeligator;

    private MaycurDeligator maycurDeligator;

    private FlightTicketGrabbingDeligator flightTicketGrabbingDeligator;

    private ReimbursementReceiptDeligator reimbursementReceiptDeligator;

    private TrainQueryDeligator trainQueryDeligator;

    private TrainDistributorDeligator trainDistributorDeligator;

    private TrainUnifiedDeligator trainUnifiedDeligator;

    private TrainQPDeligator trainQPDeligator;

    private DomesticTicketSearchDeligator domesticTicketSearchDeligator;

    private CouponDeligator couponDeligator;

    private PaymentAccountSysDeligator paymentAccountSysDeligator;

    private CorpPayDelegator corpPayDelegator;

    private ResaleInfoDeligator resaleInfoDeligator;

    private FuluDeligator fuluDeligator;

    private HljxDeligator hljxDeligator;

    private JobDeligator jobDeligator;

    private TourismDeligator tourismDeligator;

    private VetechDeligator vetechDeligator;

    private FeiShuDeligator feiShuDeligator;

    private MallDeligator mallDeligator;

    private PnrDeligator pnrDeligator;

    private InternationalFlightTicketOrderCenterDeligator internationalFlightTicketOrderCenterDeligator;

    private TourismResaleDeligator tourismResaleDeligator;

    private WechatDeligator wechatDeligator;

    private YonYouTrvlDeligator yonYouTrvlDeligator;

    private DomesticFlightTicketOrderSensitiveDeligator domesticFlightTicketOrderSensitiveDeligator;

    private CorpInvoiceNotifyDeligator corpInvoiceNotifyDeligator;

    private HuoliZhongtaiDeligator huoliZhongtaiDeligator;

    private SettleMoneyMgmtSysDeligator settleMoneyMgmtSysDeligator;

    private FlightCouponDeligator flightCouponDeligator;

    private FlightDynamicsSvcDeligator flightDynamicsSvcDeligator;

    private VetechFCDeligator vetechFCDeligator;

    @Override
    public SSOUser getSSOUser(final String name) {
        return ssoDeligator.getSSOUser(name);
    }

    @Override
    public ApiClient register(final ApiClientRegisterRequest apiClientRegisterRequest) {
        return resaleGatewayDeligator.register(apiClientRegisterRequest);
    }

    @Override
    public String updateApiClient(final ApiClientUpdateRequest apiClientUpdateRequest, final String openId) {
        return resaleGatewayDeligator.updateApiClient(apiClientUpdateRequest, openId);
    }

    @Override
    public ApiClient getApiClientByOpenId(final String openId) {
        return resaleGatewayDeligator.getApiClientByOpenId(openId);
    }

    @Override
    public ResaleUser register(final ResaleUserRegisterRequest resaleUserRegisterRequest) {
        return resaleBookingDeligator.register(resaleUserRegisterRequest);
    }

    @Override
    public String updateResaleUser(final ResaleUserUpdateRequest resaleUserUpdateRequest, final String openId) {
        return resaleBookingDeligator.updateResaleUser(resaleUserUpdateRequest, openId);
    }

    @Override
    public PageWrapper<List<DomesticFlightTicketOrder>> loadOrders(final String openId, final String orderId, final String orderStatus, final String passengerName,
                                                                   final String idCard, final String ticketNo, final Long orderCreateTimeFrom, final Long orderCreateTimeTo, final Integer pageNo, final Integer pageSize) {
        return resaleBookingDeligator.loadOrders(openId, orderId, orderStatus, passengerName, idCard, ticketNo, orderCreateTimeFrom, orderCreateTimeTo, pageNo, pageSize);
    }

    @Override
    public PageWrapper<List<OutputProductsFilter>> loadDistributeFilters(final Integer id, final String clientId, final String dataSrc, final Integer whiteFlag, final Integer pageNo, final Integer pageSize) {
        return resaleBookingDeligator.loadDistributeFilters(id, clientId, dataSrc, whiteFlag, pageNo, pageSize);
    }

    @Override
    public OutputProductsFilter createDistributeFilter(final OutputProductsFilterParam outputProductsFilterParam) {
        return resaleBookingDeligator.createDistributeFilter(outputProductsFilterParam);
    }

    @Override
    public OutputProductsFilter updateDistributeFilter(final OutputProductsFilterParam outputProductsFilterParam) {
        return resaleBookingDeligator.updateDistributeFilter(outputProductsFilterParam);
    }

    @Override
    public String deleteDistributeFilter(final Integer id) {
        return resaleBookingDeligator.deleteDistributeFilter(id);
    }

    @Override
    public PageWrapper<List<PartnerOutputConfig>> loadDistributePartners(final Integer id, final String clientId, final Integer allowNonStand, final Integer pageNo, final Integer pageSize) {
        return resaleBookingDeligator.loadDistributePartners(id, clientId, allowNonStand, pageNo, pageSize);
    }

    @Override
    public PartnerOutputConfig createDistributePartner(final PartnerOutPutParam partnerOutPutParam) {
        return resaleBookingDeligator.createDistributePartner(partnerOutPutParam);
    }

    @Override
    public PartnerOutputConfig updateDistributePartner(final PartnerOutPutParam partnerOutPutParam) {
        return resaleBookingDeligator.updateDistributePartner(partnerOutPutParam);
    }

    @Override
    public String deleteDistributePartner(final Integer id) {
        return resaleBookingDeligator.deleteDistributePartner(id);
    }

    @Override
    public PageWrapper<List<ProductPolicy>> loadDistributePolicies(final Integer id, final String clientId, final String airLine, final String isEffect, final Integer pageNo, final Integer pageSize) {
        return resaleBookingDeligator.loadDistributePolicies(id, clientId, airLine, isEffect, pageNo, pageSize);
    }

    @Override
    public ProductPolicy createDistributePolicy(final ProductPolicyParam productPolicyParam) {
        return resaleBookingDeligator.createDistributePolicy(productPolicyParam);
    }

    @Override
    public ProductPolicy updateDistributePolicy(final ProductPolicyParam productPolicyParam) {
        return resaleBookingDeligator.updateDistributePolicy(productPolicyParam);
    }

    @Override
    public String deleteDistributePolicy(final Integer id) {
        return resaleBookingDeligator.deleteDistributePolicy(id);
    }

    @Override
    public PageWrapper<List<ProductPolicyRule>> loadDistributePolicyRules(final Integer pid, final Integer pageNo, final Integer pageSize) {
        return resaleBookingDeligator.loadDistributePolicyRules(pid, pageNo, pageSize);
    }

    @Override
    public ProductPolicyRule createDistributePolicyRule(final ProductPolicyRuleParam productPolicyRuleParam) {
        return resaleBookingDeligator.createDistributePolicyRule(productPolicyRuleParam);
    }

    @Override
    public String updateDistributePolicyRule(final ProductPolicyRuleParam productPolicyRuleParam) {
        return resaleBookingDeligator.updateDistributePolicyRule(productPolicyRuleParam);
    }

    @Override
    public String deleteDistributePolicyRule(final Integer id) {
        return resaleBookingDeligator.deleteDistributePolicyRule(id);
    }

    @Override
    public PageWrapper<List<ResaleGroupParam>> loadResaleGroups(final Integer id) {
        return resaleBookingDeligator.loadResaleGroups(id);
    }

    @Override
    public String createResaleGroup(final ResaleGroupParam resaleGroupParam) {
        return resaleBookingDeligator.createResaleGroup(resaleGroupParam);
    }

    @Override
    public String updateResaleGroup(final ResaleGroupParam resaleGroupParam) {
        return resaleBookingDeligator.updateResaleGroup(resaleGroupParam);
    }

    @Override
    public String deleteResaleGroup(final Integer id) {
        return resaleBookingDeligator.deleteResaleGroup(id);
    }

    @Override
    public Map<String, List<FlightResaleDoc>> queryDocInfo() {
        return resaleBookingDeligator.queryDocInfo();
    }

    @Override
    public DomesticFlightTicketOrderDetail getFlightTicketOrderDetail(final String orderId) {
        return resaleBookingDeligator.getFlightTicketOrderDetail(orderId);
    }

    @Override
    public PageWrapper<List<FlightTicketOrderLog>> loadFlightTicketOrderLogs(final String orderId, final Integer pageNo, final Integer pageSize) {
        return resaleBookingDeligator.loadFlightTicketOrderLogs(orderId, pageNo, pageSize);
    }

    @Override
    public PageWrapper<List<ProductTypeConfig>> loadProductTypes(final Integer pageNo, final Integer pageSize) {
        return resaleBookingDeligator.loadProductTypes(pageNo, pageSize);
    }

    @Override
    public PageWrapper<List<OrderTransDetail>> queryTransRecords(final DomesticFlightTransRecordQueryParams transRecordQueryParams) {
        return resaleBookingDeligator.queryTransRecords(transRecordQueryParams);
    }

    @Override
    public List<OrderTransDetail> queryOrderStatusByOrderIds(final OrderStatusQueryParam orderStatusQueryParam) {
        return resaleBookingDeligator.queryOrderStatusByOrderIds(orderStatusQueryParam);
    }

    @Override
    public List<OrderTransDetail> querySubOrderStatusByOrderIds(final OrderStatusQueryParam orderStatusQueryParam) {
        return resaleBookingDeligator.querySubOrderStatusByOrderIds(orderStatusQueryParam);
    }

    @Override
    public String verifySSOUser(final VerifySSOUserParam verifySSOUserParam) {
        return paymentGatewayDeligator.verifySSOUser(verifySSOUserParam);
    }

    @Override
    public DistPaymentAccount getDistPaymentAccount(final QueryDistPaymentAccountRequest queryDistPaymentAccountRequest) {
        return paymentGatewayDeligator.getDistPaymentAccount(queryDistPaymentAccountRequest);
    }

    @Override
    public PageWrapper<List<DistPaymentAccountInfo>> loadDistPaymentAccountInfos(final QueryDistPaymentAccountInfoRequest queryDistPaymentAccountInfoRequest) {
        return paymentGatewayDeligator.loadDistPaymentAccountInfos(queryDistPaymentAccountInfoRequest);
    }

    @Override
    public String updateDistPaymentAccount(final UpdateDistPaymentAccountRequest updateDistPaymentAccountRequest) {
        return paymentGatewayDeligator.updateDistPaymentAccount(updateDistPaymentAccountRequest);
    }

    @Override
    public String createDistPaymentAccount(final CreateDistPaymentAccountRequest createDistPaymentAccountRequest) {
        return paymentGatewayDeligator.createDistPaymentAccount(createDistPaymentAccountRequest);
    }

    @Override
    public String rechargeDistPaymentAccount(final DistPaymentAccountRechargeRequest distPaymentAccountRechargeRequest) {
        return paymentGatewayDeligator.rechargeDistPaymentAccount(distPaymentAccountRechargeRequest);
    }

    @Override
    public CorpPaymentAccount getCorpPaymentAccount(final Long corpId) {
        return paymentGatewayDeligator.getCorpPaymentAccount(corpId);
    }

    @Override
    public String createCorpPaymentAccount(final CreateCorpPaymentAccountRequest createCorpPaymentAccountRequest) {
        return paymentGatewayDeligator.createCorpPaymentAccount(createCorpPaymentAccountRequest);
    }

    @Override
    public String updateCorpPaymentAccount(final UpdateCorpPaymentAccountRequest updateCorpPaymentAccountRequest) {
        return paymentGatewayDeligator.updateCorpPaymentAccount(updateCorpPaymentAccountRequest);
    }

    @Override
    public String createCorpMemberPaymentInfo(final CreateCorpMemberPaymentInfoRequest createCorpMemberPaymentInfoRequest) {
        return paymentGatewayDeligator.createCorpMemberPaymentInfo(createCorpMemberPaymentInfoRequest);
    }

    @Override
    public String updateCorpMemberPaymentInfo(final UpdateCorpMemberPaymentInfoRequest updateCorpMemberPaymentInfoRequest) {
        return paymentGatewayDeligator.updateCorpMemberPaymentInfo(updateCorpMemberPaymentInfoRequest);
    }

    @Override
    public String mergeCorpMemberPaymentInfo(final MergeCorpMemberPaymentInfoRequest mergeCorpMemberPaymentInfoRequest) {
        return paymentGatewayDeligator.mergeCorpMemberPaymentInfo(mergeCorpMemberPaymentInfoRequest);
    }

    @Override
    public String rechargeCorpPaymentAccount(final RechargeCorpPaymentAccountRequest rechargeCorpPaymentAccountRequest) {
        return paymentGatewayDeligator.rechargeCorpPaymentAccount(rechargeCorpPaymentAccountRequest);
    }

    @Override
    public PageWrapper<List<CorpPaymentAccount>> loadCorpPaymentAccounts(final List<String> corpIds, final Integer pageNo, final Integer pageSize) {
        return paymentGatewayDeligator.loadCorpPaymentAccounts(corpIds, pageNo, pageSize);
    }

    @Override
    public CorpConsumptionInfo getCorpConsumptionInfo(final Long corpId, final String productIds) {
        return paymentGatewayDeligator.getCorpConsumptionInfo(corpId, productIds);
    }

    @Override
    public PageWrapper<List<CorpMemberPaymentInfo>> loadCorpMemberPaymentInfos(final Long corpId, final List<String> phoneIds, final Integer pageNo, final Integer pageSize) {
        return paymentGatewayDeligator.loadCorpMemberPaymentInfos(corpId, phoneIds, pageNo, pageSize);
    }

    @Override
    public CorpMemberPaymentInfo getCorpMemberPaymentInfo(final Long corpId, final Integer phoneId) {
        return paymentGatewayDeligator.getCorpMemberPaymentInfo(corpId, phoneId);
    }

    @Override
    public PageWrapper<List<CorpTransRecord>> loadCorpTransRecords(final Long corpId, final Integer phoneId, final Long transTimeFrom, final Long transTimeTo, final Integer pageNo, final Integer pageSize) {
        return paymentGatewayDeligator.loadCorpTransRecords(corpId, phoneId, transTimeFrom, transTimeTo, pageNo, pageSize);
    }

    @Override
    public PageWrapper<List<CorpTransRecord>> loadCorpTransRecordsNew(final Long corpId, final Integer phoneId, final Long transTimeFrom, final Long transTimeTo, final Integer pageNo, final Integer pageSize) {
        return paymentGatewayDeligator.loadCorpTransRecordsNew(corpId, phoneId, transTimeFrom, transTimeTo, pageNo, pageSize);
    }

    @Override
    public PageWrapper<List<CorpRechargeRecord>> loadCorpRechargeRecords(final Long corpId, final Long transTimeFrom, final Long transTimeTo, final Integer pageNo, final Integer pageSize) {
        return paymentGatewayDeligator.loadCorpRechargeRecords(corpId, transTimeFrom, transTimeTo, pageNo, pageSize);
    }

    @Override
    public String emergencyQueryRegister(final EmergencyQueryRegisterParams emergencyQueryRegisterParams) {
        return paymentGatewayDeligator.emergencyQueryRegister(emergencyQueryRegisterParams);
    }

    @Override
    public PageWrapper<List<CaissaCorpTransRecord>> loadCaissaTransRecords(final Long corpId, final String orderId, final String payOrderId, final String tradeStart, final String tradeEnd, final String timeStart, final String timeEnd, final Integer pageNo, final Integer pageSize) {
        return paymentGatewayDeligator.loadCaissaTransRecords(corpId, orderId, payOrderId, tradeStart, tradeEnd, timeStart, timeEnd, pageNo, pageSize);
    }

    @Override
    public List<MixPayStatusResult> getMixPayStatus(final String payOrderId) {
        return paymentGatewayDeligator.getMixPayStatus(payOrderId);
    }

    @Override
    public String getPayOrderId(final RetrievePayOrderIdRequest retrievePayOrderIdRequest) {
        return paymentSysDeligator.getPayOrderId(retrievePayOrderIdRequest);
    }

    @Override
    public ResultWithRefund refundPayOrder(final PayRefundRequest payRefundRequest) {
        return paymentSysDeligator.refundPayOrder(payRefundRequest);
    }

    @Override
    public ResultWithPayOrderId createPayOrder(final RetrievePayOrderIdRequest retrievePayOrderIdRequest) {
        return paymentSysDeligator.createPayOrder(retrievePayOrderIdRequest);
    }

    @Override
    public PageWrapper<List<CorpTransRecordNew>> queryCorpTradeRecord(final Long corpId, final Integer phoneId, final Long transTimeFrom, final Long transTimeTo, final Integer pageNo, final Integer pageSize) {
        return paymentSysDeligator.queryCorpTradeRecord(corpId, phoneId, transTimeFrom, transTimeTo, pageNo, pageSize);
    }

    @Override
    public List<OrderPaymentInfo> queryOrderPaymentInfoByOrderId(final String orderId) {
        return paymentSysDeligator.queryOrderPaymentInfoByOrderId(orderId);
    }

    @Override
    public List<OrderPaymentInfo> queryOrderPaymentInfoByOrderIds(final List<String> orderIds) {
        return paymentSysDeligator.queryOrderPaymentInfoByOrderIds(orderIds);
    }

    @Override
    public PageWrapper<List<CorpTransRecordNew>> queryCorpTradeRecord(final Long corpId, final List<String> orderIds, final Integer phoneId, final Long transTimeFrom, final Long transTimeTo, final Integer pageNo, final Integer pageSize) {
        return paymentSysDeligator.queryCorpTradeRecord(corpId, orderIds, phoneId, transTimeFrom, transTimeTo, pageNo, pageSize);
    }

    @Override
    public String getQrCodeOfPayment(final RetrieveQrCodeRequest retrieveQrCodeRequest) {
        return paymentSysDeligator.getQrCodeOfPayment(retrieveQrCodeRequest);
    }

    @Override
    public String corpPay(final CorpPayRequest corpPayRequest) {
        return paymentSysDeligator.corpPay(corpPayRequest);
    }

    @Override
    public BatchPayApplyResult batchPayApply(final BatchPayApplyParam batchPayApplyParam) {
        return paymentSysDeligator.batchPayApply(batchPayApplyParam);
    }

    @Override
    public DataResult getCities() {
        return baseDataCacheDeligator.getCities();
    }

    @Override
    public PageWrapper<List<InternationalFlightTicketOrder>> loadInternationalTicketOrders(final InternationalTicketOrderQueryParams internationalTicketOrderQueryParams) {
        return internationalFlightTicketDeligator.loadInternationalTicketOrders(internationalTicketOrderQueryParams);
    }

    @Override
    public InternationalFlightTicketTransFlowRecord getInternationalFlightTicketTransFlowRecord(final String orderId) {
        return internationalFlightTicketDeligator.getInternationalFlightTicketTransFlowRecord(orderId);
    }

    @Override
    public PageWrapper<List<InternationalFlightTicketTransFlowRecord>> getInternationalFlightTicketTransFlowList(final InternationalFlightTicketTransFlowRecordQueryParam internationalFlightTicketTransFlowRecordQueryParam) {
        return internationalFlightTicketDeligator.getInternationalFlightTicketTransFlowList(internationalFlightTicketTransFlowRecordQueryParam);
    }

    @Override
    public PageWrapper<List<InFlightTicketRealTimeOrderVO>> queryInFlightRealTimeOrderInfo(final InFlightTicketRealTimeOrderQueryParam queryParam) {
        return internationalFlightTicketDeligator.queryInFlightRealTimeOrderInfo(queryParam);
    }

    @Override
    public InFlightTicketRealTimeOrderDetailVO queryInFlightRealTimeOrderDetail(final String orderId) {
        return internationalFlightTicketDeligator.queryInFlightRealTimeOrderDetail(orderId);
    }

    @Override
    public PageWrapper<List<InFlightTicketRealTimeOrderReportVO>> queryInFlightRealTimeOrderInfoForReport(final Long corpId, final String startTime, final String endTime, final Integer pageNo, final Integer pageSize) {
        return internationalFlightTicketDeligator.queryInFlightRealTimeOrderInfoForReport(corpId, startTime, endTime, pageNo, pageSize);
    }

    @Override
    public InternationalFlightTicketOrderTripInfo queryInFlightTicketOrderTripInfo(final String orderId, final String payOrderId) {
        return internationalFlightTicketDeligator.queryInFlightTicketOrderTripInfo(orderId, payOrderId);
    }

    @Override
    public InFlightTicketOrderCreateResult createInFlightTicketOrder(final String postBody) {
        return internationalFlightTicketDeligator.createInFlightTicketOrder(postBody);
    }

    @Override
    public IntFlightOrderAppDetailResultVo queryAppOrderDetail(final String orderId, final String phoneId, final String p) {
        return internationalFlightTicketDeligator.queryAppOrderDetail(orderId, phoneId, p);
    }

    @Override
    public BaseResult<IntFlightJourneyBookSureVO> queryJourneyBookSureOrderInfo(final InFlightJourneyBookSureQueryParam queryParam) {
        return internationalFlightTicketDeligator.queryJourneyBookSureOrderInfo(queryParam);
    }

    @Override
    public IntFlightOrderPriceDetailResultVo queryOrderPriceDetail(final String orderId, final String phoneId) {
        return internationalFlightTicketDeligator.queryOrderPriceDetail(orderId, phoneId);
    }

    @Override
    public PageFlightTicketResult<List<TmcFlightTicketOrderMobileVO>> queryTmcIntFlightTicketOrders(final String userId,
                                                                                                    final String orderId,
                                                                                                    final String passengerName,
                                                                                                    final String depCity,
                                                                                                    final String arrCity,
                                                                                                    final String flyNo,
                                                                                                    final String groupType,
                                                                                                    final Boolean isGetAll,
                                                                                                    final Integer maxCount,
                                                                                                    final Boolean isNeedPage,
                                                                                                    final Integer pageSize,
                                                                                                    final Integer pageNum,
                                                                                                    final String p,
                                                                                                    final String effectiveOrderFlag,
                                                                                                    final String bizOrderFlag) {
        return internationalFlightTicketDeligator.queryTmcIntFlightTicketOrders(userId, orderId, passengerName, depCity,
                arrCity, flyNo, groupType, isGetAll, maxCount, isNeedPage, pageSize, pageNum, p, effectiveOrderFlag, bizOrderFlag);
    }

    @Override
    public CorpOrderRecordsWrapper<IftCorpOrderRecord> queryIftCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam) {
        return internationalFlightTicketDeligator.queryIftCorpOrderRecords(queryParam);
    }

    @Override
    public PageWrapper<List<TrainTicketOrder>> loadTrainTicketOrders(final String phoneId, final String orderId, final String orderStatus, final String passengerName, final String cardNo, final Long startTime, final Long endTime, final Integer pageNo, final Integer pageSize) {
        return trainTicketDeligator.loadTrainTicketOrders(phoneId, orderId, orderStatus, passengerName, cardNo, startTime, endTime, pageNo, pageSize);
    }

    @Override
    public PageWrapper<List<DistTrainTicketTransFlowRecord>> loadDistTrainTicketTransFlowRecords(final String distId, final String loginUserName, final String phoneId, final String orderId, final String transType, final String transChannel, final String consumeType, final Long transTimeFrom, final Long transTimeTo, final Integer pageNo, final Integer pageSize) {
        return trainTicketDeligator.loadDistTrainTicketTransFlowRecords(distId, loginUserName, phoneId, orderId, transType, transChannel, consumeType, transTimeFrom, transTimeTo, pageNo, pageSize);
    }

    @Override
    public List<TrainTicketGrabOrderDetailVO> queryGrabOrderByParams(final String id, final String orderId, final String phoneId, final String phone, final String erpId) {
        return trainTicketDeligator.queryGrabOrderByParams(id, orderId, phoneId, phone, erpId);
    }

    @Override
    public String setExpired(final String id) {
        return trainTicketDeligator.setExpired(id);
    }

    @Override
    public PlatformBuyOrderCreate createTrainTicketOrder(final String postBody) {
        return trainTicketDeligator.createTrainTicketOrder(postBody);
    }

    @Override
    public List<TrainGrabOrderStatisticsVO> queryTrainGrabOrderStatistics(final String phoneId, final String startDate, final String endDate) {
        return trainTicketDeligator.queryTrainGrabOrderStatistics(phoneId, startDate, endDate);
    }

    @Override
    public String insertDistTransFlow(final List<DistTrainTradeLine> distTradeLines) {
        return trainTicketDeligator.insertDistTransFlow(distTradeLines);
    }

    @Override
    public String getConfigs(final String key) {
        return trainTicketDeligator.getConfigs(key);
    }

    @Override
    public String sm4DecodeBySource(String content,String source) {
        return trainTicketDeligator.sm4DecodeBySource(content,source);
    }

    @Override
    public String pushMsg(final PushMsgRequest pushMsgRequest) {
        return trainTicketMsgPushDeligator.pushMsg(pushMsgRequest);
    }

    @Override
    public List<TrainTicketTransFlowRecord> loadTrainTicketTransFlowRecords(final String corpId, final String orderId, final Long transTimeFrom, final Long transTimeTo) {
        return trainTicketDeligator.loadTrainTicketTransFlowRecords(corpId, orderId, transTimeFrom, transTimeTo);
    }

    @Override
    public List<DelayCareTransFlowRecord> loadDelayCareTransFlowRecords(final String corpId, final Long transTimeFrom, final Long transTimeTo) {
        return trainTicketDeligator.loadDelayCareTransFlowRecords(corpId, transTimeFrom, transTimeTo);
    }

    @Override
    public CorpTrainTicketOrderDetail getCorpTrainTicketOrderDetail(final String orderId, final String payOrderId) {
        return trainTicketDeligator.getCorpTrainTicketOrderDetail(orderId, payOrderId);
    }

    @Override
    public CorpTrainTicketMainSubOrderDetail getCorpTrainTicketMainSubOrderDetail(final String orderId) {
        return trainTicketDeligator.getCorpTrainTicketMainSubOrderDetail(orderId);
    }

    @Override
    public String getTrainTMCUrl(final TrainTicketHandleUrlQueryParam param) {
        return trainTicketDeligator.getTrainTMCUrl(param);
    }

    @Override
    public PageWrapper<List<TrainTicketRealTimeOrderVO>> queryTrainRealTimeOrderInfo(final TrainTicketRealTimeOrderQueryParam queryParam) {
        return trainTicketDeligator.queryTrainRealTimeOrderInfo(queryParam);
    }

    @Override
    public TrainTicketRealTimeOrderDetailVO queryTrainRealTimeOrderDetailByOrderId(final String orderId) {
        return trainTicketDeligator.queryTrainRealTimeOrderDetailByOrderId(orderId);
    }

    @Override
    public List<RerouteFlightResult> queryRerouteFlight(final RerouteFlightParam rerouteFlightParam) {
        return resaleBookingDeligator.queryRerouteFlight(rerouteFlightParam);
    }

    @Override
    public ReroutePriceConfirmResult reroutePriceConfirm(final ReroutePriceConfirmParam reroutePriceConfirmParam) {
        return resaleBookingDeligator.reroutePriceConfirm(reroutePriceConfirmParam);
    }

    @Override
    public RerouteSubmitResult rerouteSubmit(final RerouteSubmitParam rerouteSubmitParam) {
        return resaleBookingDeligator.rerouteSubmit(rerouteSubmitParam);
    }

    @Override
    public ConfirmRefundResult refundFlight(final RefundFlightParam refundFlightParam) {
        return resaleBookingDeligator.refundFlight(refundFlightParam);
    }

    @Override
    public RefundQueryResult refundQuery(final RefundQueryParam refundQueryParam) {
        return resaleBookingDeligator.refundQuery(refundQueryParam);
    }

    @Override
    public List<RefundFlightParam.FileInfo> upload(final List<RefundFlightParam.FileInfo> files) {
        return resaleBookingDeligator.upload(files);
    }

    @Override
    public String insertResaleLog(final FlightResaleOrderLog flightResaleOrderLog) {
        return resaleBookingDeligator.insertResaleLog(flightResaleOrderLog);
    }

    @Override
    public PageWrapper<List<FlightResaleOrderLog>> loadAppealByPage(final OrderLogParam orderLogParam) {
        return resaleBookingDeligator.loadAppealByPage(orderLogParam);
    }

    @Override
    public PageWrapper<List<CarOrderVO>> loadCarOrders(final CarOrderQueryParams carOrderQueryParams) {
        return carDeligator.loadCarOrders(carOrderQueryParams);
    }

    @Override
    public CarOrderDetailVO queryCarOrderDetailByOrderId(final String orderId) {
        return carDeligator.queryCarOrderDetailByOrderId(orderId);
    }

    @Override
    public List<CarTransFlowRecord> loadCarTransFlowRecords(final String corpId, final String orderId, final String ticketOrderId, final String carOrderId, final Long transTimeFrom, final Long transTimeTo) {
        return carDeligator.loadCarTransFlowRecords(corpId, orderId, ticketOrderId, carOrderId, transTimeFrom, transTimeTo);
    }

    @Override
    public String queryCarTmcUrl(final GatewayParam param) {
        return carDeligator.queryCarTmcUrl(param);
    }

    @Override
    public PageWrapper<List<CarRealTimeOrderVO>> queryCarRealTimeOrderInfo(final CarRealTimeOrderQueryParam carRealTimeOrderQueryParam) {
        return carDeligator.queryCarRealTimeOrderInfo(carRealTimeOrderQueryParam);
    }

    @Override
    public CreateCarOrderResult createCarOrder(final String postBody) {
        return carDeligator.createCarOrder(postBody);
    }

    @Override
    public CreateCarOrderStrongResult createCarOrder(final CarCreateOrderParam carCreateOrderParam) {
        return carDeligator.createCarOrder(carCreateOrderParam);
    }

    @Override
    public CarUserTripsResult moreUserTrips(final CarUserTripParams userTripParams) {
        return carDeligator.moreUserTrips(userTripParams);
    }

    @Override
    public CarQueryMerchandiseResult queryMerchandise(final CarQueryMerchandiseParam carQueryMerchandiseParam) {
        return carDeligator.queryMerchandise(carQueryMerchandiseParam);
    }

    @Override
    public CarQueryMerchandiseV3Result queryMerchandiseV3(final CarQueryMerchandiseParam carQueryMerchandiseParam) {
        return carDeligator.queryMerchandiseV3(carQueryMerchandiseParam);
    }

    @Override
    public CarQueryMerchandiseDetailResult queryMerchandiseDetail(final CarQueryMerchandiseDetailParam carQueryMerchandiseParam) {
        return carDeligator.queryMerchandiseDetail(carQueryMerchandiseParam);
    }

    @Override
    public CarQueryMerchandisePriceDetailResult queryMerchandisePriceDetail(final CarQueryMerchandisePriceDetailParam carQueryMerchandiseParam) {
        return carDeligator.queryMerchandisePriceDetail(carQueryMerchandiseParam);
    }

    @Override
    public PageWrapper<List<CarOrderListVO>> queryOrderList(final CarQueryOrderListParam carQueryOrderListParam) {
        return carDeligator.queryOrderList(carQueryOrderListParam);
    }

    @Override
    public CarQueryOrderDetailResult queryOrderDetail(final CarQueryOrderDetailParam carQueryOrderDetailParam) {
        return carDeligator.queryOrderDetail(carQueryOrderDetailParam);
    }

    @Override
    public CorpOrderRecordsWrapper<CarCorpOrderRecord> queryCarCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam) {
        return carDeligator.queryCarCorpOrderRecords(queryParam);
    }

    @Override
    public List<HotelProvinceDto> loadHotelCityList() {
        return hotelDeligator.loadHotelCityList();
    }

    @Override
    public List<HotelProvinceDto> loadTmcHotelCityList() {
        return hotelDeligator.loadTmcHotelCityList();
    }

    @Override
    public HotelProvinceVO loadHotelProvinces() {
        return hotelDeligator.loadHotelProvinces();
    }

    @Override
    public HotelCityDto queryTmcHotelCityByCityNameFuzzyMatching(String cityName) {
        return hotelDeligator.queryTmcHotelCityByCityNameFuzzyMatching(cityName);
    }

    @Override
    public HotelCityDto queryHotelCityByCityName(final String cityName) {
        return hotelDeligator.queryHotelCityByCityName(cityName);
    }

    @Override
    public HotelCityDto queryHotelCityByCityNameFuzzyMatching(final String cityName) {
        return hotelDeligator.queryHotelCityByCityNameFuzzyMatching(cityName);
    }

    @Override
    public String transferOrderId(final String orderId) {
        return carDeligator.transferOrderId(orderId);
    }

    @Override
    public HotelOrderDetailVO queryHotelOrderDetailByOrderId(final String orderId) {
        return hotelDeligator.queryHotelOrderDetailByOrderId(orderId);
    }

    @Override
    public List<HotelTransFlowRecord> loadHotelTransFlowRecords(final String corpId, final String orderId, final Long transTimeFrom, final Long transTimeTo) {
        return hotelDeligator.loadHotelTransFlowRecords(corpId, orderId, transTimeFrom, transTimeTo);
    }

    @Override
    public String queryHotelTmcUrl(final HotelHandleUrlQueryParam param) {
        return hotelDeligator.queryHotelTmcUrl(param);
    }

    @Override
    public PageWrapper<List<HotelRealTimeOrderDetailVO>> queryHotelRealTimeOrderInfo(final HotelRealTimeOrderQueryParam queryParam) {
        return hotelDeligator.queryHotelRealTimeOrderInfo(queryParam);
    }

    @Override
    public CreateHotelResult createHotelOrder(final String orderFormData) {
        return hotelDeligator.createHotelOrder(orderFormData);
    }

    @Override
    public CreateHotelResult tmcCreateHotelOrder(final String orderFormData) {
        return hotelDeligator.tmcCreateHotelOrder(orderFormData);
    }

    @Override
    public HotelOrderListVO queryOrderList(final HotelOrderListQueryParam param) {
        return hotelDeligator.queryOrderList(param);
    }

    @Override
    public OrderDetailVO handleHotelOrder(final HotelOrderHandleParam param) {
        return hotelDeligator.handleHotelOrder(param);
    }

    @Override
    public CorpOrderRecordsWrapper<HotelCorpOrderRecord> queryHotelCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam) {
        return hotelDeligator.queryHotelCorpOrderRecords(queryParam);
    }

    @Override
    public InvoiceBatchDto invoiceSaves(final InvoiceBatchRequest invoiceBatchRequest) {
        return invoiceDeligator.invoiceSaves(invoiceBatchRequest);
    }

    @Override
    public InvoiceBatchDto.InvoiceDto queryInvoiceDetail(final Long phoneId, final Long invoiceId, final Integer source) {
        return invoiceDeligator.queryInvoiceDetail(phoneId, invoiceId, source);
    }

    @Override
    public String cancelInvoice(final Long invoiceId) {
        return invoiceDeligator.cancelInvoice(invoiceId);
    }

    @Override
    public List<EleInvoDto> queryInvoice(final QueryInvoiceRequest queryInvoiceRequest) {
        return invoiceDeligator.queryInvoice(queryInvoiceRequest);
    }

    @Override
    public TicketConfirmResult itinerarySaves(final PostConfirmParam confirmParam) {
        return invoiceDeligator.itinerarySaves(confirmParam);
    }

    @Override
    public ItineraryCancelResult cancelItinerary(ItineraryCancelParam cancelParam) {
        return invoiceDeligator.cancelItinerary(cancelParam);
    }

    @Override
    public String getItineraryFileUrl(String name, String detailId, String fileType) {
        return invoiceDeligator.getItineraryFileUrl(name, detailId, fileType);
    }

    @Override
    public InvoiceInsConfirmResult insuranceSaves(InvoiceInsConfirmParam param) {
        return invoiceDeligator.insuranceSaves(param);
    }

    @Override
    public InvoiceInsQueryResult insuranceQuery(String orderId) {
        return invoiceDeligator.insuranceQuery(orderId);
    }

    @Override
    public String pushTemplateBizMsg(final PushTemplateMessageRequest templateMessageRequest) {
        return pushCenterDeligator.pushTemplateBizMsg(templateMessageRequest);
    }

    @Override
    public String pushMsg(final PushMessageRequest pushMessageRequest) {
        return pushCenterDeligator.pushMsg(pushMessageRequest);
    }

    @Override
    public String pushWeChatMsg(final PushWeChatMessageRequest pushWeChatMessageRequest) {
        return pushCenterDeligator.pushWeChatMsg(pushWeChatMessageRequest);
    }

    @Override
    public String sendSms(final SendSmsRequest sendSmsRequest) {
        return pushCenterDeligator.sendSms(sendSmsRequest);
    }

    @Override
    public String pushGtgjMsg(final AppMsgPushProviderCallback appMsgPushProviderCallback) {
        for (final String receiver : appMsgPushProviderCallback.getReceivers()) {
            final PushMsgRequest pushMsgRequest = new PushMsgRequest();
            pushMsgRequest.setTitle(appMsgPushProviderCallback.getTitle());
            pushMsgRequest.setContent(appMsgPushProviderCallback.getContent());
            pushMsgRequest.setSendType(PushMsgRequest.SendType.PUSH_BOTH.getId());
            final List<PushMsgRequest.ExtData> links = new ArrayList<>(appMsgPushProviderCallback.getLinkInfoList().size());
            for (final LinkInfo linkInfo : appMsgPushProviderCallback.getLinkInfoList()) {
                final PushMsgRequest.ExtData link = new PushMsgRequest.ExtData();
                link.setLinkText(linkInfo.getLinkText());
                link.setLinkUrl(linkInfo.getLinkUrl());
                links.add(link);
            }
            pushMsgRequest.setLinks(links);
            pushMsgRequest.setPhoneId(receiver);

            try {
                pushMsg(pushMsgRequest);
            } catch (final CfesagUnavailableException | CfesagInvokeException e) {
                log.error(e.getMessage(), e);
            }
        }
        return null;
    }

    @Override
    public String pushHbgjMsg(final AppMsgPushProviderCallback appMsgPushProviderCallback) {
        final PushMessageRequest pushMessageRequest = new PushMessageRequest();
        pushMessageRequest.setUidsOfMsgReceiver(appMsgPushProviderCallback.getReceivers());

        final PushWrapper pushWrapper = new PushWrapper();
        pushWrapper.setType(PushWrapper.MessageType.URL_MSG.getId());
        pushWrapper.setTitle(appMsgPushProviderCallback.getTitle());
        pushWrapper.setMsg(appMsgPushProviderCallback.getContent());
        final PushWrapper.ExtData extData = new PushWrapper.ExtData();
        extData.setType(String.valueOf(PushWrapper.MessageType.URL_MSG.getId()));
        extData.setUrl(appMsgPushProviderCallback.getRedirectUrl());
        pushWrapper.setExtData(extData);
        pushMessageRequest.setPushWrapper(pushWrapper);

        final HelpWrapper helpWrapper = new HelpWrapper();
        helpWrapper.setMsg(appMsgPushProviderCallback.getContent());
        final List<HelpWrapper.ExtData> links = new ArrayList<>();
        for (final LinkInfo linkInfo : appMsgPushProviderCallback.getLinkInfoList()) {
            final HelpWrapper.ExtData link = new HelpWrapper.ExtData();
            link.setLinkText(linkInfo.getLinkText());
            link.setLinkUrl(linkInfo.getLinkUrl());
            links.add(link);
        }
        helpWrapper.setExtDataList(links);
        pushMessageRequest.setHelpWrapper(helpWrapper);

        return pushMsg(pushMessageRequest);
    }

    @Override
    public String pushMsg(final AppMsgPushProviderCallback appMsgPushProviderCallback) {
        final PushMessageRequest pushMessageRequest = new PushMessageRequest();
        pushMessageRequest.setUidsOfMsgReceiver(appMsgPushProviderCallback.getReceivers());
        pushMessageRequest.setPhoneIdsOfMsgReceiver(appMsgPushProviderCallback.getPhoneIdOfReceivers());
        pushMessageRequest.setToApp(appMsgPushProviderCallback.getToApp());

        final PushWrapper pushWrapper = new PushWrapper();
        pushWrapper.setType(PushWrapper.MessageType.URL_MSG.getId());
        pushWrapper.setTitle(appMsgPushProviderCallback.getTitle());
        pushWrapper.setMsg(appMsgPushProviderCallback.getContent());
        final PushWrapper.ExtData extData = new PushWrapper.ExtData();
        extData.setType(String.valueOf(PushWrapper.MessageType.URL_MSG.getId()));
        extData.setUrl(appMsgPushProviderCallback.getRedirectUrl());
        pushWrapper.setExtData(extData);
        pushMessageRequest.setPushWrapper(pushWrapper);

        final HelpWrapper helpWrapper = new HelpWrapper();
        helpWrapper.setMsg(appMsgPushProviderCallback.getContent());
        final List<HelpWrapper.ExtData> links = new ArrayList<>();
        for (final LinkInfo linkInfo : appMsgPushProviderCallback.getLinkInfoList()) {
            final HelpWrapper.ExtData link = new HelpWrapper.ExtData();
            link.setLinkText(linkInfo.getLinkText());
            link.setLinkUrl(linkInfo.getLinkUrl());
            links.add(link);
        }
        helpWrapper.setExtDataList(links);
        pushMessageRequest.setHelpWrapper(helpWrapper);

        return pushMsg(pushMessageRequest);
    }

    @Override
    public String pushWeChatMsg(final WeChatMsgPushProviderCallback weChatMsgPushProviderCallback) {
        for (final String receiver : weChatMsgPushProviderCallback.getReceivers()) {
            final PushWeChatMessageRequest pushWeChatMessageRequest = new PushWeChatMessageRequest();
            pushWeChatMessageRequest.setPhoneId(receiver);
            pushWeChatMessageRequest.setMsgTemplateId(weChatMsgPushProviderCallback.getMsgTemplateId());

            final Map<String, PushWeChatMessageRequest.MsgTemplateParam> msgTemplateParams = new HashMap<>(weChatMsgPushProviderCallback.getMsgTemplateParams().size());
            for (final PushWeChatMessageRequest.MsgTemplateParam msgTemplateParam : weChatMsgPushProviderCallback.getMsgTemplateParams()) {
                msgTemplateParams.put(msgTemplateParam.getParamName(), msgTemplateParam);
            }
            pushWeChatMessageRequest.setMsgTemplateParams(msgTemplateParams);

            final AppName appName = weChatMsgPushProviderCallback.getAppName();
            if (Objects.nonNull(appName)) {
                pushWeChatMessageRequest.setAppId(appName.getWechatAppId());
                pushWeChatMessageRequest.setMiniAppId(appName.getWechatMiniAppId());
            }


            final String h5Url = weChatMsgPushProviderCallback.getUrl();
            final String pagePath = weChatMsgPushProviderCallback.getRedirectUrl();
            if (StringUtils.isNotBlank(pagePath)) {
                pushWeChatMessageRequest.setPagePath(pagePath);
            } else if (StringUtils.isNotBlank(h5Url)) {
                pushWeChatMessageRequest.setUrl(h5Url);
            }

            try {
                pushWeChatMsg(pushWeChatMessageRequest);
            } catch (final CfesagUnavailableException | CfesagInvokeException e) {
                log.error(e.getMessage(), e);
            }
        }

        return null;
    }

    @Override
    public String getUserId(final Long phoneId) {
        return userCenterDeligator.getUserId(phoneId);
    }

    @Override
    public PassengerData loadPassengers(final Long phoneId) {
        return userCenterDeligator.loadPassengers(phoneId);
    }

    @Override
    public PassengerVO createPassenger(final Long phoneId, final CreatePassengerParam createPassengerParam) {
        return userCenterDeligator.createPassenger(phoneId, createPassengerParam);
    }

    @Override
    public boolean deletePassenger(final Long phoneId, final DeletePassengerParam passenger) {
        return userCenterDeligator.deletePassenger(phoneId, passenger);
    }

    @Override
    public Long queryPhoneIdByPhone(final String phone) {
        return userCenterDeligator.queryPhoneIdByPhone(phone);
    }

    @Override
    public Long registerForBusiness(final String phone) {
        return userCenterDeligator.registerForBusiness(phone);
    }

    @Override
    public void updatePhone(final UpdatePhoneRequest updatePhoneRequest) {
        userCenterDeligator.updatePhone(updatePhoneRequest);
    }

    @Override
    public void addTags(final UserTagParam userTagParam) {
        userCenterDeligator.addTags(userTagParam);
    }

    @Override
    public void deleteTags(final UserTagParam userTagParam) {
        userCenterDeligator.deleteTags(userTagParam);
    }

    @Override
    public List<String> queryTags(final UserTagParam userTagParam) {
        return userCenterDeligator.queryTags(userTagParam);
    }

    @Override
    public Long registerPlatformUser(final RegisterPlatformUserRequest registerPlatformUserRequest) {
        return userCenterDeligator.registerPlatformUser(registerPlatformUserRequest);
    }

    @Override
    public Long registerPlatformUserByTpUnionId(final RegisterPlatformUserByUnionIdRequest registerPlatformUserByUnionIdRequest) {
        return userCenterDeligator.registerPlatformUserByTpUnionId(registerPlatformUserByUnionIdRequest);
    }

    @Override
    public void unbindTp(final UnbindTpRequest unbindTpRequest) {
        userCenterDeligator.unbindTp(unbindTpRequest);
    }

    @Override
    public void bindTp(final BindTpRequest bindTpRequest) {
        userCenterDeligator.bindTp(bindTpRequest);
    }

    @Override
    public void updatePlatformUserPhone(final UpdatePlatformUserPhoneRequest updatePlatformUserPhoneRequest) {
        userCenterDeligator.updatePlatformUserPhone(updatePlatformUserPhoneRequest);
    }

    @Override
    public String deletePlatformUser(final DeletePlatformUserRequest deletePlatformUserRequest) {
        return userCenterDeligator.deletePlatformUser(deletePlatformUserRequest);
    }

    @Override
    public AuthCodeResult getAuthByPhoneId(final String phoneId) {
        return userCenterDeligator.getAuthByPhoneId(phoneId);
    }

    @Override
    public AuthCodeResult getAuthByPhoneIdTemp(final String phoneId) {
        return userCenterDeligator.getAuthByPhoneIdTemp(phoneId);
    }

    @Override
    public UserBasicInfo queryUserBaseInfo(final Long phoneId) {
        return userCenterDeligator.queryUserBaseInfo(phoneId);
    }

    @Override
    public UserInvoiceInfo queryUserInvoiceInfo(final Long phoneId) {
        return userCenterDeligator.queryUserInvoiceInfo(phoneId);
    }

    @Override
    public UserPostInfo queryUserPostInfo(final Long phoneId) {
        return userCenterDeligator.queryUserPostInfo(phoneId);
    }

    @Override
    public String getUserLatestPhoneId(final String phoneId) {
        return userCenterDeligator.getUserLatestPhoneId(phoneId);
    }

    @Override
    public List<DomesticFlightTicketRefundRecord> loadDomesticFlightTicketRefundRecords(final Long transTimeFrom, final Long transTimeTo) {
        return domesticFlightTicketBookingDeligator.loadDomesticFlightTicketRefundRecords(transTimeFrom, transTimeTo);
    }

    @Override
    public List<DomesticFlightTicketTransFlowRecord> loadDomesticFlightTicketTransFlowRecords(final String corpId, final String orderId, final Long transTimeFrom, final Long transTimeTo) {
        return domesticFlightTicketBookingSupportDeligator.loadDomesticFlightTicketTransFlowRecords(corpId, orderId, transTimeFrom, transTimeTo);
    }

    @Override
    public List<BizTicketOrderResult> queryInfoByProduct(BizTicketOrderQueryParam queryParam) {
        return bizTicketOrderDeligator.queryInfoByProduct(queryParam);
    }

    @Override
    public List<BizTicketOrderResult> queryInfoByChannel(BizTicketOrderQueryParam queryParam) {
        return bizTicketOrderDeligator.queryInfoByChannel(queryParam);
    }

    @Override
    public List<XInsuranceOrderInfo> queryInsuranceOrderInfos(final XInsuranceOrderQueryReq queryReq) {
        return insuranceDeligator.queryInsuranceOrderInfos(queryReq);
    }

    @Override
    public List<InsuranceTransFlowRecord> loadInsuranceTransFlowRecords(final String corpId, final Long transTimeFrom, final Long transTimeTo) {
        return insuranceDeligator.loadInsuranceTransFlowRecords(corpId, transTimeFrom, transTimeTo);
    }

    @Override
    public List<DftInsureProductCanby> queryFlightAttachProduct(final DftInsureProductParam param) {
        return insuranceDeligator.queryFlightAttachProduct(param);
    }

    @Override
    public DftnsureProductResult queryCorpAttachProduct(final CorpInsureParam param) {
        return insuranceDeligator.queryCorpAttachProduct(param);
    }

    @Override
    public String createUatpInfo(final UatpInfoCreateRequest uatpInfoCreateRequest) {
        return domesticFlightTicketChannelDeligator.createUatpInfo(uatpInfoCreateRequest);
    }

    @Override
    public String updateUatpInfo(final UatpInfoUpdateRequest uatpInfoUpdateRequest) {
        return domesticFlightTicketChannelDeligator.updateUatpInfo(uatpInfoUpdateRequest);
    }

    @Override
    public VipHallResult queryVipHall(final String phoneid, final String airportCode, final String departTime, final String proTerminal) {
        return domesticFlightTicketVipHallDeligator.queryVipHall(phoneid, airportCode, departTime, proTerminal);
    }

    @Override
    public String savePostNew(final PostInfoRequest postInfoRequest) {
        return busApiDeligator.savePostNew(postInfoRequest);
    }

    @Override
    public String createWorkOrder(final WorkOrderRequest workOrderRequest) {
        return busApiDeligator.createWorkOrder(workOrderRequest);
    }

    @Override
    public List<TktReceiptInfo> queryTktReceiptInfos(final List<String> ticketNos) {
        return busApiDeligator.queryTktReceiptInfos(ticketNos);
    }

    @Override
    public String mergePostUserAccount(final PostUserAccount postUserAccount) {
        return apiToolsDeligator.mergePostUserAccount(postUserAccount);
    }

    @Override
    public CorpDomesticFlightTicketOrderDetail getCorpDomesticFlightTicketOrderDetail(final String orderId) {
        return domesticFlightTicketOrderCenterDeligator.getCorpDomesticFlightTicketOrderDetail(orderId);
    }

    @Override
    public DomesticFlightOrderDetail getDomesticFlightTicketOrder(final String orderId) {
        return domesticFlightTicketOrderCenterDeligator.getDomesticFlightTicketOrder(orderId);
    }

    @Override
    public PayRefundResult manuallyRefund(final PayParam payParam) {
        return domesticFlightTicketOrderCenterDeligator.manuallyRefund(payParam);
    }

    @Override
    public FlightSearchResult searchFlight(final FlightSearchParam flightSearchParam) {
        return resaleBookingDeligator.searchFlight(flightSearchParam);
    }

    @Override
    public String insertResaleLogList(final List<FlightResaleOrderLog> resaleOrderLogs) {
        return resaleBookingDeligator.insertResaleLogList(resaleOrderLogs);
    }

    @Override
    public SurplusQueryResult orderSurplusQuery(final SurplusQuery surplusQuery) {
        return domesticFlightTicketOrderCenterDeligator.orderSurplusQuery(surplusQuery);
    }

    @Override
    public PageWrapper<List<DomesticFlightRealTimeOrderVO>> queryDomesticFlightRealTimeOrderInfo(final DomesticFlightRealTimeOrderQueryParam realTimeOrderQueryParam) {
        return domesticFlightTicketOrderCenterDeligator.queryDomesticFlightRealTimeOrderInfo(realTimeOrderQueryParam);
    }

    @Override
    public DomesticFlightRealTimeOrderDetailVO queryDomesticFlightRealTimeOrderInfoByOrderId(final String orderId) {
        return domesticFlightTicketOrderCenterDeligator.queryDomesticFlightRealTimeOrderInfoByOrderId(orderId);
    }

    @Override
    public PageWrapper<List<DomesticFlightRealTimeOrderReportVO>> queryDomesticFlightRealTimeOrderInfoForReport(final Long corpId, final Date startTime, final Date endTime, final Integer pageNo, final Integer pageSize) {
        return domesticFlightTicketOrderCenterDeligator.queryDomesticFlightRealTimeOrderInfoForReport(corpId, startTime, endTime, pageNo, pageSize);
    }

    @Override
    public DomesticFlightTicketBookResult createDomesticFlightOrder(final String orderFormData) {
        return domesticFlightTicketOrderCenterDeligator.createDomesticFlightOrder(orderFormData);
    }

    @Override
    public DftOrderInfo queryDomesticFlightOrderMsgByOrderId(final String orderId) {
        return domesticFlightTicketOrderCenterDeligator.queryDomesticFlightOrderMsgByOrderId(orderId);
    }

    @Override
    public PageFlightTicketResult<List<TmcFlightTicketOrderMobileVO>> queryTmcDomesticFlightTicketOrders(final String userId,
                                                                                                         final String orderId,
                                                                                                         final String passengerName,
                                                                                                         final String depCity,
                                                                                                         final String arrCity,
                                                                                                         final String flyNo,
                                                                                                         final String groupType,
                                                                                                         final Boolean isGetAll,
                                                                                                         final Integer maxCount,
                                                                                                         final Boolean isNeedPage,
                                                                                                         final Integer pageSize,
                                                                                                         final Integer pageNum,
                                                                                                         final String p,
                                                                                                         final String effectiveOrderFlag,
                                                                                                         final String bizOrderFlag) {
        return domesticFlightTicketOrderCenterDeligator.queryTmcDomesticFlightTicketOrders(userId, orderId, passengerName, depCity,
                arrCity, flyNo, groupType, isGetAll, maxCount, isNeedPage, pageSize, pageNum, p, effectiveOrderFlag, bizOrderFlag);
    }

    @Override
    public CorpOrderRecordsWrapper<DftCorpOrderRecord> queryDftCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam) {
        return domesticFlightTicketOrderCenterDeligator.queryDftCorpOrderRecords(queryParam);
    }

    @Override
    public CorpResaleOrderRecsWrapper<DftResaleOrderConsumption> queryDftCorpResaleOrderRecs(final CorpResaleOrderRecsQueryParam queryParam) {
        return domesticFlightTicketOrderCenterDeligator.queryDftCorpResaleOrderRecs(queryParam);
    }

    @Override
    public DftOrderRerouteResult clientRerouteSubmit(final DftOrderRerouteParam param) {
        return domesticFlightTicketOrderCenterDeligator.clientRerouteSubmit(param);
    }

    @Override
    public DftOrderRefundResult clientRefund(final DftOrderRefundParam param) {
        return domesticFlightTicketOrderCenterDeligator.clientRefund(param);
    }

    @Override
    public String queryFlightHandleUrl(final DomesticFlightTicketHandleUrlQueryParam param) {
        return domesticFlightTicketBookDeligator.queryFlightHandleUrl(param);
    }

    @Override
    public DynamicDetailResult queryFlightWeather(final DynamicBaseParam param) {
        return domesticFlightTicketDynamicDeligator.queryFlightWeather(param);
    }

    @Override
    public List<DynamicDetailResult> queryFlightIntegrationBatch(final List<DynamicBaseParam> params) {
        return domesticFlightTicketDynamicDeligator.queryFlightIntegrationBatch(params);
    }

    @Override
    public FlightResult search(final FlightSearchParam flightSearchParam) {
        return resaleBookingDeligator.search(flightSearchParam);
    }

    @Override
    public DomesticFlightTicketOrderDetail getDomesticFlightTicketDetail(final String orderId) {
        return resaleBookingDeligator.getDomesticFlightTicketDetail(orderId);
    }

    @Override
    public PageWrapper<List<ApiLogQueryResult>> apiLogQuery(final ApiLogQueryParam apiLogQueryParam) {
        return resaleBookingDeligator.apiLogQuery(apiLogQueryParam);
    }

    @Override
    public String apiLogExport(final ApiLogQueryParam apiLogQueryParam) {
        return resaleBookingDeligator.apiLogExport(apiLogQueryParam);
    }

    @Override
    public List<MonitorInfoResult> monitorInfoQuery(final MonitorInfoQueryParam monitorInfoQueryParam) {
        return resaleBookingDeligator.monitorInfoQuery(monitorInfoQueryParam);
    }

    @Override
    public String monitorInfoExport(final MonitorInfoQueryParam monitorInfoQueryParam) {
        return resaleBookingDeligator.monitorInfoExport(monitorInfoQueryParam);
    }

    @Override
    public String cancelPnr(final CancelPnrParams cancelPnrParams) {
        return resaleBookingDeligator.cancelPnr(cancelPnrParams);
    }

    @Override
    public TicketPostResult getPostInfoV2(String postId) {
        return postServiceDeligator.getPostInfoV2(postId);
    }

    @Override
    public void cancelTrip(final CancelTripParam cancelTripParam) {
        postServiceDeligator.cancelTrip(cancelTripParam);
    }

    @Override
    public void invalidInvoice(final InvalidInvoiceParam invalidInvoiceParam) {
        postServiceDeligator.invalidInvoice(invalidInvoiceParam);
    }

    @Override
    public void revokePackage(final RevokePackageParam revokePackageParam) {
        postServiceDeligator.revokePackage(revokePackageParam);
    }

    @Override
    public void updateEmail(final UpdateEmailParam updateEmailParam) {
        postServiceDeligator.updateEmail(updateEmailParam);
    }

    @Override
    public void postHang(final HangPostPkgParam hangPostPkgParam) {
        postServiceDeligator.postHang(hangPostPkgParam);
    }

    @Override
    public void update(final UpdatePostPkgParam updatePostPkgParam) {
        postServiceDeligator.update(updatePostPkgParam);
    }

    @Override
    public void removeDetail(final PostCancelParam postCancelParam) {
        postServiceDeligator.removeDetail(postCancelParam);
    }

    @Override
    public TicketConfirmResult corpApplyConfirm(final PostConfirmParam postConfirmParam) {
        return postServiceDeligator.corpApplyConfirm(postConfirmParam);
    }

    @Override
    public TicketConfirmResult corpApplyConfirmV2(PostConfirmParam postConfirmParam) {
        return postServiceDeligator.corpApplyConfirmV2(postConfirmParam);
    }

    @Override
    public String authorize(final MaycurAuthRequest maycurAuthRequest) {
        return maycurDeligator.authorize(maycurAuthRequest);
    }

    @Override
    public String pushFlightTicketOrderInfo(final List<MaycurOrder> orders, final String accessToken) {
        return maycurDeligator.pushFlightTicketOrderInfo(orders, accessToken);
    }

    @Override
    public GrabbingInfo queryFlightTicketGrabFeeInfoByOrderId(final String orderId) {
        return flightTicketGrabbingDeligator.queryFlightTicketGrabFeeInfoByOrderId(orderId);
    }

    @Override
    public String generateCorpReimbursementReport(final Long corpId, final String month, final Integer type, final String loginName) {
        return reimbursementReceiptDeligator.generateCorpReimbursementReport(corpId, month, type, loginName);
    }

    @Override
    public ReimbursementReceiptPkgRecordVO getReimbursementReceiptPkgRecord(final Long corpId, final String calStartDate, final String calEndDate) {
        return reimbursementReceiptDeligator.getReimbursementReceiptPkgRecord(corpId, calStartDate, calEndDate);
    }

    @Override
    public String pkgRecord(final Long corpId, final String calStartDate, final String calEndDate, final List<String> invoiceList) {
        return reimbursementReceiptDeligator.pkgRecord(corpId, calStartDate, calEndDate, invoiceList);
    }

    @Override
    public String queryInFlightTicketSearchListUrl(final InFlightTicketHandleUrlQueryParam param) {
        return internationalFlightTicketSearchDeligator.queryInFlightTicketSearchListUrl(param);
    }

    @Override
    public String getCorpPostId(final Long corpId, final String calStartDate, final String calEndDate) {
        return reimbursementReceiptDeligator.getCorpPostId(corpId, calStartDate, calEndDate);
    }

    @Override
    public List<DftReimbursementReceiptRecordVO> getDftReimbursementRecords(List<String> orderIds) {
        return reimbursementReceiptDeligator.getDftReimbursementRecords(orderIds);
    }

    @Override
    public List<ReimbursementReceiptSummitRecordVO> getSummitRecords(List<String> postIds) {
        return reimbursementReceiptDeligator.getSummitRecords(postIds);
    }

    @Override
    public List<SearchApiCountDomesticFlightResult> queryApiCount(final SearchApiCountDomesticFlightParam param) {
        return resaleBookingDeligator.queryApiCount(param);
    }

    @Override
    public SearchApiCountTrainResult queryTrainCountApi(final SearchApiCountTrainParam searchApiCountTrainParam) {
        return trainTicketDeligator.queryTrainCountApi(searchApiCountTrainParam);
    }

    @Override
    public QueryOrderListResultVO queryOrderList(final TrainTicketOrderListQueryParam param) {
        return trainTicketDeligator.queryOrderList(param);
    }

    @Override
    public OrderDetailResultVO queryOrderDetail(final TrainTicketQueryDetailParam param) {
        return trainTicketDeligator.queryOrderDetail(param);
    }

    @Override
    public TrainZwdRateResultVO queryTrainZwdRate(final String trainNo, final String departStation, final String arriveStation) {
        return trainTicketDeligator.queryTrainZwdRate(trainNo, departStation, arriveStation);
    }

    @Override
    public QueryTrainScheduleDetailVO queryTrainScheduleShareDetail(final QueryTrainScheduleDetailParam param) {
        return trainTicketDeligator.queryTrainScheduleShareDetail(param);
    }

    @Override
    public CorpOrderRecordsWrapper<TrainCorpOrderRecord> queryTrainCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam) {
        return trainTicketDeligator.queryTrainCorpOrderRecords(queryParam);
    }

    @Override
    public TrainQueryRecord queryTrainListByStation(final TrainQueryParam param) {
        return trainQueryDeligator.queryTrainListByStation(param);
    }

    @Override
    public IntFlightOrderStatusProcessResultVo queryIntOrderStatusProcess(final String orderId, final String phoneId) {
        return internationalFlightTicketDeligator.queryIntOrderStatusProcess(orderId, phoneId);
    }

    @Override
    public String cancelOrder(final CarCancelOrderParam param) {
        return carDeligator.cancelOrder(param);
    }

    @Override
    public List<HLServiceVO> queryHLServices(final CarQueryHLServiceParam carQueryHLServiceParam) {
        return carDeligator.queryHLServices(carQueryHLServiceParam);
    }

    @Override
    public CarUserTripsResult queryTripList(final CarTripListParam carTripListParam) {
        return carDeligator.queryTripList(carTripListParam);
    }

    @Override
    public CarQueryCompleteOrderLatelyResult queryCompleteOrderLately(final CarQueryCompleteOrderLatelyParam carQueryCompleteOrderLatelyParam) {
        return carDeligator.queryCompleteOrderLately(carQueryCompleteOrderLatelyParam);
    }

    @Override
    public CarQueryProductNoticeResult queryProductNotice(final CarQueryProductNoticeParam carQueryProductNoticeParam) {
        return carDeligator.queryProductNotice(carQueryProductNoticeParam);
    }

    @Override
    public DftCorpResaleOrderVO queryDFTTicketCorpResaleOrder(final DftCorpResaleOrderQueryParam dftCorpResaleOrderQueryParam) {
        return domesticFlightTicketOrderCenterDeligator.queryDFTTicketCorpResaleOrder(dftCorpResaleOrderQueryParam);
    }

    @Override
    public String cancelOrder(final IftCancelOrderParam param) {
        return internationalFlightTicketDeligator.cancelOrder(param);
    }

    @Override
    public HotelCorpResaleOrderVO queryHotelCorpResaleOrderInfo(final HotelCorpResaleOrderParam hotelCorpResaleOrderParam) {
        return hotelDeligator.queryHotelCorpResaleOrderInfo(hotelCorpResaleOrderParam);
    }

    @Override
    public TmcHotelOrderFormData parseHotelOrderFormData(final TmcOrderFormDataParam formDataParam) {
        return hotelDeligator.parseHotelOrderFormData(formDataParam);
    }

    @Override
    public CorpResaleOrderRecsWrapper<HotelResaleOrderConsumption> queryHotelResaleCorpOrderRecs(final CorpResaleOrderRecsQueryParam queryParam) {
        return hotelDeligator.queryHotelResaleCorpOrderRecs(queryParam);
    }

    @Override
    public BaseResult<HotelOrderRespVO> hotelTmcCreateOrder(final TmcCreateOrderReq param) {
        return hotelDeligator.hotelTmcCreateOrder(param);
    }

    @Override
    public HotelOrderSyncResp tmcSyncOrderStatus(final HotelOrderSyncReq orderSyncReq) {
        return hotelDeligator.tmcSyncOrderStatus(orderSyncReq);
    }

    @Override
    public HotelDetailVO hotelDetail(final String hotelCode) {
        return hotelDeligator.hotelDetail(hotelCode);
    }

    @Override
    public String cancelOrder(final TrainCancelOrderParam param) {
        return trainTicketDeligator.cancelOrder(param);
    }

    @Override
    public InsuranceCorpOrderVO queryInsuranceCorpOrder(final InsuranceCorpOrderParam insuranceCorpOrderParam) {
        return insuranceDeligator.queryInsuranceCorpOrder(insuranceCorpOrderParam);
    }

    /**
     * 国内机票-预订舱位详情查询
     *
     * <AUTHOR>
     */
    @Override
    public DftBookDetailResult queryCabinDetail(final DftQueryTicketSearchParam param) {
        return domesticFlightTicketBookDeligator.queryCabinDetail(param);
    }

    /**
     * tmc行程确认
     */
    @Override
    public DftTmcFlightConfirmResult confirmFlight(final DftFlightConfirmParam param) {
        return domesticFlightTicketBookDeligator.confirmFlight(param);
    }

    @Override
    public DftPreBookInfo queryBookCache(final DftOrderSubmitParam param) {
        return domesticFlightTicketBookDeligator.queryBookCache(param);
    }

    @Override
    public DftOrderSubmitResult submit(final DftOrderSubmitParam param) {
        return domesticFlightTicketBookDeligator.submit(param);
    }

    @Override
    public DftOrderSubmitResult createOrder(final DftOrderSubmitParam param) {
        return domesticFlightTicketBookDeligator.createOrder(param);
    }

    @Override
    public DftOrderSubmitResult createOrderV1(final DftOrderSubmitParam param) {
        return domesticFlightTicketBookDeligator.createOrderV1(param);
    }

    @Override
    public DftPrepayCheckResult corpPrepayCheck(final DftOrderSubmitParam param) {
        return domesticFlightTicketBookDeligator.corpPrepayCheck(param);
    }

    /**
     * 国内机票-验仓验价
     *
     * <AUTHOR>
     */
    @Override
    public BaseResult<DftTicketVerifyResult> pricing(@RequestBody final DftTicketVerifyParam param) {
        return domesticFlightTicketOrderCenterDeligator.pricing(param);
    }

    /**
     * 国内机票-验仓验价
     *
     * <AUTHOR>
     */
    @Override
    public DftTicketVerifyResult pricingV1(@RequestBody final DftTicketVerifyParam param) {
        return domesticFlightTicketOrderCenterDeligator.pricingV1(param);
    }

    /**
     * 国内机票-提交订单
     *
     * <AUTHOR>
     */
    @Override
    public BaseResult<DftBookResult> book(@RequestBody final DftBookParam param) {
        return domesticFlightTicketOrderCenterDeligator.book(param);
    }

    /**
     * 国内机票-提交订单
     *
     * <AUTHOR>
     */
    @Override
    public DftBookResult bookV1(@RequestBody final DftBookParam param) {
        return domesticFlightTicketOrderCenterDeligator.bookV1(param);
    }

    /**
     * 国内机票货架-航班列表
     *
     * <AUTHOR>
     */
    @Override
    public DftFlightListResult queryFlightList(@RequestBody final DftFlightListParam param) {
        return domesticFlightTicketBookDeligator.queryFlightList(param);
    }

    /**
     * 国内机票-查询填单页报销凭证信息填充信息v2
     *
     * <AUTHOR>
     */
    @Override
    public DftVoucherFillResult queryVoucherFill2(@RequestBody final DftVoucherFillParam param) {
        return domesticFlightTicketOrderCenterDeligator.queryVoucherFill2(param);
    }

    @Override
    public FlightBizTicketInfo queryTicketStatus(final String orderId, final String ticketNo) {
        return domesticFlightTicketBizDeligator.queryTicketStatus(orderId, ticketNo);
    }

    @Override
    public List<DftChannelInfo> queryChannelInfo() {
        return domesticFlightTicketBizDeligator.queryChannelInfo();
    }

    @Override
    public List<DftChannelInfo> queryChannelInfos() {
        return domesticFlightTicketBizDeligator.queryChannelInfos();
    }

    @Override
    public TicketValidResult ticketValid(TicketValidParam param) {
        return domesticFlightTicketBizDeligator.ticketValid(param);
    }

    @Override
    public String cancelOrder(final DftCancelOrderParam param) {
        return domesticFlightTicketOrderCenterDeligator.cancelOrder(param);
    }

    @Override
    public List<DftOrderDetailInfoVO> queryDftOrderDetailInfo(final DftOrderDetailParam param) {
        return domesticFlightTicketOrderCenterDeligator.queryDftOrderDetailInfo(param);
    }

    @Override
    public DftOrderDetailResult getDftTmcOrderDetailInfo(final String orderId) {
        return domesticFlightTicketOrderCenterDeligator.getDftTmcOrderDetailInfo(orderId);
    }

    @Override
    public SeatStatusResult queryOrderSeatStatusV2(final SubmitVerifyParam param) {
        return domesticFlightTicketOrderCenterDeligator.queryOrderSeatStatusV2(param);
    }
    
    @Override
    public SeatStatusResult queryOrderSeatStatus(final String orderId) {
        return domesticFlightTicketOrderCenterDeligator.queryOrderSeatStatus(orderId);
    }

    @Override
    public DftOrderDetailInfoVO searchDftOrderInnerOrderDetail(final String orderId) {
        return domesticFlightTicketOrderCenterDeligator.searchDftOrderInnerOrderDetail(orderId);
    }


    @Override
    public CostCentersResultVO queryCostCenters(final String phoneIdOfBookedBy) {
        return trainUnifiedDeligator.queryCostCenters(phoneIdOfBookedBy);
    }

    @Override
    public PlatformBuyOrderCreate createOrderUnified(final TrainCreateOrderParam param) {
        return trainUnifiedDeligator.createOrderUnified(param);
    }

    @Override
    public GetPayUrlResultVO getPayUrlSpV2(final GetPayUrlParam param) {
        return trainTicketDeligator.getPayUrlSpV2(param);
    }

    @Override
    public EndorseRequestResultVO endorseRequest(final EndorseRequestParam param) {
        return trainTicketDeligator.endorseRequest(param);
    }

    @Override
    public HelpPayInstPurchaseCart instPurchased(final InstPurchasedParam instPurchasedParam) {
        return trainTicketDeligator.instPurchased(instPurchasedParam);
    }

    @Override
    public String endorseConfirm(final EndorseConfirmParam param) {
        return trainTicketDeligator.endorseConfirm(param);
    }

    @Override
    public GetEndorsePayUrlResultVO getEndorsePayUrl(final GetEndorsePayUrlParam param) {
        return trainTicketDeligator.getEndorsePayUrl(param);
    }

    @Override
    public CarQueryProdsResult queryCarProds(final CarQueryProdsParam param) {
        return carDeligator.queryCarProds(param);
    }

    @Override
    public CarBatchQueryHlServiceResult batchQueryHlService(final CarBatchQueryHlServiceParam param) {
        return carDeligator.batchQueryHlService(param);
    }

    @Override
    public CarOrderInfoResult queryOrderInfo(final CarQueryOrderDetailParam carQueryOrderDetailParam) {
        return carDeligator.queryOrderInfo(carQueryOrderDetailParam);
    }

    @Override
    public GetGtUserAccountResult getGtUserAccount(final GetGtUserAccountParam param) {
        return trainTicketDeligator.getGtUserAccount(param);
    }

    @Override
    public QPMemberActiveResult isActivedMember(final QPMemberActiveParam param) {
        return trainQPDeligator.isActivedMember(param);
    }

    @Override
    public GetQPOrderIdResult getQPOrderId(final String phoneid) {
        return trainQPDeligator.getQPOrderId(phoneid);
    }

    @Override
    public CreateQPOrderResult createQPOrder(final CreateQPOrderParam.QPOrderFormData param) {
        return trainQPDeligator.createQPOrder(param);
    }

    @Override
    public GetQPOrderDetailResult getQPOrderDetail(final GetQPOrderDetailParam param) {
        return trainQPDeligator.getQPOrderDetail(param);
    }

    @Override
    public DftPriceTrendResult queryPriceTrend(final DftQueryPriceTrendParam param) {
        return domesticTicketSearchDeligator.queryPriceTrend(param);
    }

    @Override
    public String companyAuthorized(final MaycurCorpInfoParam maycurCorpInfoParam) {
        return maycurDeligator.companyAuthorized(maycurCorpInfoParam);
    }

    @Override
    public MaycurTrvlPlatformToken platformAuthLogin() {
        return maycurDeligator.platformAuthLogin();
    }

    @Override
    public String notifyPlatformCorpOpenStatus(final MaycurCorpCreateResultParam corpCreateResultParam,
                                               final String tokenId) {
        return maycurDeligator.notifyPlatformCorpOpenStatus(corpCreateResultParam, tokenId);
    }

    @Override
    public String cancelQP(final CancelQPOrderParam param) {
        return trainQPDeligator.cancelQP(param);
    }

    @Override
    public QPPaymentDetailResult getQPPaymentDetail(final QPPaymentDetailParam param) {
        return trainQPDeligator.getQPPaymentDetail(param);
    }

    @Override
    public UserPointsInfoDTO loadUserAccountInfo(final BonusPointsRequest pointsRequest) {
        return couponDeligator.loadUserAccountInfo(pointsRequest);
    }

    /**
     * 用户余额信息查询接口
     */
    @Override
    public UserPointsInfoDTO loadUserPointsInfo(final BonusPointsRequest pointsRequest) {
        return couponDeligator.loadUserPointsInfo(pointsRequest);
    }

    /**
     * 用户消费积分接口
     */
    @Override
    public String consumptionPoints(final ConsumptionPointsRequest pointsRequest) {
        return couponDeligator.consumptionPoints(pointsRequest);
    }

    /**
     * 用户消费积分接口
     */
    @Override
    public List<BonusCouponsInfoDTO> exchangeCoupons(final ExchangeCouponsRequest exchangeCouponsRequest) {
        return couponDeligator.exchangeCoupons(exchangeCouponsRequest);
    }

    @Override
    public GtgjExchangeCouponsResult exchangeGtgjCoupons(final ExchangeCouponsRequest exchangeCouponsRequest) {
        return couponDeligator.exchangeGtgjCoupons(exchangeCouponsRequest);
    }

    @Override
    public UpgradeMemberMailResult upgradeMemberMail(final UpgradeMemberMailRequest upgradeMemberMailRequest) {
        return couponDeligator.upgradeMemberMail(upgradeMemberMailRequest);
    }

    /**
     * 用户积分赠送接口
     */
    @Override
    public String giveUserBonusPoints(final GiveBonusPointsRequest pointsRequest) {
        return couponDeligator.giveUserBonusPoints(pointsRequest);
    }

    @Override
    public CreateCarHailingResultVO createCarhailingOrder(final CreateHailingOrderParam createCarhailingParam) {
        return carHailingDeligator.createCarhailingOrder(createCarhailingParam);
    }

    @Override
    public String dispatcherTmcOrder(final String orderid) {
        return carHailingDeligator.dispatcherTmcOrder(orderid);
    }

    @Override
    public CarsSelectInitResultVO carhailingSelectInit(final CarhailingSelectInitModel carhailingSelectInitParam) {
        return carHailingDeligator.carhailingSelectInit(carhailingSelectInitParam);
    }

    @Override
    public QueryOrderStatusResultVO queryCarhailingOrderStatus(final QueryOrderStatusModel queryOrderStatusParam) {
        return carHailingDeligator.queryCarhailingOrderStatus(queryOrderStatusParam);
    }

    @Override
    public String warnningToPhonesByWechat(final String content, final String phones) {
        return carHailingDeligator.warnningToPhonesByWechat(content, phones);
    }

    @Override
    public CarHailingServiceCityVO queryCarhailingServiceCitys() {
        return carHailingDeligator.queryCarhailingServiceCitys();
    }

    @Override
    public List<CarHailingOrderStatusVO> queryCorpOrderReconciliation(final Long startTime, final Long endTime, final Long corpId, final String orderId) {
        return carHailingDeligator.queryCorpOrderReconciliation(startTime, endTime, corpId, orderId);
    }

    @Override
    public CarOrderSycnVO carHailingOrderToTmcOrder(final String orderId) {
        return carHailingDeligator.carHailingOrderToTmcOrder(orderId);
    }


    @Override
    public List<CarOrderSycnVO> carHailingOrderListQuery(final String phoneid, final String source) {
        return carHailingDeligator.carHailingOrderListQuery(phoneid, source);
    }

    @Override
    public CorpOrderRecordsWrapper<CarCorpOrderRecord> queryCarHailingCorpOrderTransRecords(final CorpOrderRecordsQueryParam params) {
        return carHailingDeligator.queryCarHailingCorpOrderTransRecords(params);
    }

    @Override
    public PageWrapper<List<CarHailingOrderDetail>> queryCarHailingRealTimeOrderInfo(final CarRealTimeOrderQueryParam carRealTimeOrderQueryParam) {
        return carHailingDeligator.queryCarHailingRealTimeOrderInfo(carRealTimeOrderQueryParam);
    }

    @Override
    public String accountRechargeBalance(final AccountRechargeRequest requestParam) {
        return paymentAccountSysDeligator.accountRechargeBalance(requestParam);
    }

    @Override
    public CouponListResult queryUserCouponList(final CouponListParam param) {
        return paymentAccountSysDeligator.queryUserCouponList(param);
    }

    @Override
    public String corpPayPlus(final CorpPayRequestPlus corpPayRequestPlus) {
        return corpPayDelegator.corpPayPlus(corpPayRequestPlus);
    }

    @Override
    public QueryTrainDataResultVO queryTrainData(final QueryTrainDataParam param) {
        return trainQueryDeligator.queryTrainData(param);
    }

    @Override
    public TrainOrderConsumeInfoResultVO queryOrderConsumeInfo(final TrainOrderConsumeInfoParam param) {
        return trainTicketDeligator.queryOrderConsumeInfo(param);
    }

    @Override
    public List<GetSuccessOrderResultItem> getQPSuccessOrders() {
        return trainQPDeligator.getQPSuccessOrders();
    }

    @Override
    public TrainCorpResaleOrderVO queryTrainCorpResaleOrderInfo(final TrainCorpResaleOrderQueryParam trainCorpResaleOrderQueryParam) {
        return trainTicketDeligator.queryTrainCorpResaleOrderInfo(trainCorpResaleOrderQueryParam);
    }

    @Override
    public CorpResaleOrderRecsWrapper<TrainResaleOrderConsumption> queryTrainResaleCorpOrderRecs(final CorpResaleOrderRecsQueryParam queryParam) {
        return trainTicketDeligator.queryTrainResaleCorpOrderRecs(queryParam);
    }

    @Override
    public String queryTrainResaleOrderSubOrderId(String passengerName, String orderID) {
        return trainTicketDeligator.queryTrainResaleOrderSubOrderId(passengerName, orderID);
    }

    @Override
    public CheckPassengerResultVO checkPassenger(final CheckPassengerParam param) {
        return trainQueryDeligator.checkPassenger(param);
    }

    @Override
    public QueryCheckPassengerStatusResultVO queryCheckPassengerStatus(final QueryCheckPassengerStatusParam param) {
        return trainQueryDeligator.queryCheckPassengerStatus(param);
    }

    @Override
    public List<StationQueryResult.Station> queryTrainStations() {
        return trainQueryDeligator.queryTrainStations();
    }

    @Override
    public List<StationQueryResult.Station> queryTrainStationByCityName(final String cityName) {
        return trainQueryDeligator.queryTrainStationByCityName(cityName);
    }

    @Override
    public List<StationQueryResult.Station> queryTrainStationByCityNameFuzzyMatching(final String cityName) {
        return trainQueryDeligator.queryTrainStationByCityNameFuzzyMatching(cityName);
    }

    @Override
    public Map<String, String> queryCityNameStationName(final String cityList) {
        return trainQueryDeligator.queryCityNameStationName(cityList);
    }

    @Override
    public List<DftResaleInfoVO> queryDFTResaleInfo(final DftResaleInfoParam queryParam) {
        return resaleInfoDeligator.queryDFTResaleInfo(queryParam);
    }

    @Override
    public void doBoardingNotification(final DftOrderBoardingInfo boardingInfo) {
        resaleInfoDeligator.doBoardingNotification(boardingInfo);
    }


    @Override
    public TmcHotelCityData tmcHotelCityData(final TmcHotelCityParam tmcHotelCityParam) {
        return hotelDeligator.tmcHotelCityData(tmcHotelCityParam);
    }

    @Override
    public CarCitylResult queryCity(final CarCitylParam carCitylParam) {
        return carDeligator.queryCity(carCitylParam);
    }

    @Override
    public FuluCreateDirectOrderResult createDirectOrder(final FuluCreateDirectOrderParam param) {
        return fuluDeligator.createDirectOrder(param);
    }

    @Override
    public FuluCreateCarmiOrderResult createCarmiOrder(final FuluCreateCarmiOrderParam param) {
        return fuluDeligator.createCarmiOrder(param);
    }

    @Override
    public TmcHLCommoditiesWrapper loadHlCommodities(final TmcHLCommoditiesRequest request) {
        return hljxDeligator.loadHlCommodities(request);
    }

    @Override
    public FuluGoodsDetailResult queryGoodsDetail(final FuluGoodsParam param) {
        return fuluDeligator.queryGoodsDetail(param);
    }

    @Override
    public List<FuluGoodsBaseInfoResult> queryFuluAllGoods() {
        return fuluDeligator.queryFuluAllGoods();
    }

    @Override
    public FuluOrderResult queryFuluOrderDetail(final FuluOrderParam param) {
        return fuluDeligator.queryFuluOrderDetail(param);
    }

    @Override
    public String triggerJob(final TriggerJobRequestParams triggerJobRequestParams) {
        return jobDeligator.triggerJob(triggerJobRequestParams);
    }


    @Override
    public TrainOccupyingASeatResult occupyingASeat(final TrainOccupyingASeatParam param) {
        return trainDistributorDeligator.occupyingASeat(param);
    }

    @Override
    public TrainConfirmTicketResult confirmTicket(final TrainConfirmTicketParam param) {
        return trainDistributorDeligator.confirmTicket(param);
    }

    @Override
    public TrainCancelOrderResult cancelOrder(final TrainDistributorCancelOrderParam param) {
        return trainDistributorDeligator.cancelOrder(param);
    }

    @Override
    public TrainRefundTicketResult refundTicket(final TrainDistributorRefundTicketParam param) {
        return trainDistributorDeligator.refundTicket(param);
    }

    @Override
    public TrainChangeTicketResult changeTicket(final TrainChangeTicketParam param) {
        return trainDistributorDeligator.changeTicket(param);
    }

    @Override
    public TrainConfirmChangeTicketResult confirmChange(final TrainConfirmChangeTicketParam param) {
        return trainDistributorDeligator.confirmChange(param);
    }

    @Override
    public TrainCancelChangeTicketResult cancelChange(final TrainCancelChangeTicketParam param) {
        return trainDistributorDeligator.cancelChange(param);
    }

    @Override
    public TrainBuyTicketResult buyTicket(final TrainBuyTicketParam param) {
        return trainDistributorDeligator.buyTicket(param);
    }

    @Override
    public String platformCancelOrder(final BaseOrderParams params) {
        return trainDistributorDeligator.platformCancelOrder(params);
    }

    @Override
    public String platformRefundTicket(final BaseOrderParams params) {
        return trainDistributorDeligator.platformRefundTicket(params);
    }

    @Override
    public String platformCancelChange(final BaseOrderParams params) {
        return trainDistributorDeligator.platformCancelChange(params);
    }

    @Override
    public YuHongNofifResult yuHongflightOrderNotif(final OYHDftOrderMsg msg) {
        return yuHongFlightOrderDeligator.yuHongflightOrderNotif(msg);
    }

    @Override
    public BaseResult<String> syncOYHSettlementBill(OYHSettlementBill bill) {
        return yuHongFlightOrderDeligator.syncOYHSettlementBill(bill);
    }

    @Override
    public CorpOrderRecordsWrapper<TourismCorpOrderRecord> queryTourismCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam) {
        return tourismDeligator.queryTourismCorpOrderRecords(queryParam);
    }

    @Override
    public List<MemberDetailResult> queryMemberDetail(final MemberDetailRequest memberDetailRequest) {
        return paymentAccountSysDeligator.queryMemberDetail(memberDetailRequest);
    }

    @Override
    public List<ConsumeDetailResult> queryConsumeDetail(final ConsumeDetailRequest consumeDetailRequest) {
        return paymentAccountSysDeligator.queryConsumeDetail(consumeDetailRequest);
    }


    @Override
    public String pushOrderInfo(final List<VetechOrder> orders) {
        return vetechDeligator.pushOrderInfo(orders);
    }

    @Override
    public VetechResult syncInvoice(VetechSyncInvoiceParam param) {
        return vetechDeligator.syncInvoice(param);
    }

    @Override
    public String generateCaissaFlowFile(final CaissaFlowFileQueryParam queryParam) {
        return paymentSysDeligator.generateCaissaFlowFile(queryParam);
    }

    @Override
    public TokenResult queryAppAccessToken(final AppTokenParam param) {
        return feiShuDeligator.queryAppAccessToken(param);
    }

    @Override
    public TokenResult queryTenantAccessToken(final TenantTokenParam param) {
        return feiShuDeligator.queryTenantAccessToken(param);
    }

    @Override
    public TokenResult appTicketResend(final TicketResendParam param) {
        return feiShuDeligator.appTicketResend(param);
    }

    @Override
    public FeishuPageWrapper<FeiShuDepartInfo> loadDepartInfos(final FeiShuDepartQueryParam departQueryParam) {
        return feiShuDeligator.loadDepartInfos(departQueryParam);
    }

    @Override
    public FeishuPageWrapper<FeiShuUserInfo> loadUserInfos(final FeiShuUserQueryParam userQueryParam) {
        return feiShuDeligator.loadUserInfos(userQueryParam);
    }

    @Override
    public FeiShuUserBasicInfo getUserInfo(final String authorization,
                                           final String userId,
                                           final String userIdType) {
        return feiShuDeligator.getUserInfo(authorization, userId, userIdType);
    }

    @Override
    public FeiShuSingleUserSendMsgResult sendFeiShuUserMsg(final String authorization,
                                                           final String receiveIdType,
                                                           final FeiShuSingleUserSendMsgRequest requestParam) {
        return feiShuDeligator.sendFeiShuUserMsg(authorization, receiveIdType, requestParam);
    }

    @Override
    public FeiShuBatchSendMsgResult batchSendFeiShuMsg(final String authorization,
                                                       final FeiShuBatchSendMsgRequest requestParam) {
        return feiShuDeligator.batchSendFeiShuMsg(authorization, requestParam);
    }

    @Override
    public ExternalTokenResult tmcTenantToken(final TenantAccessTokenParam param) {
        return feiShuDeligator.tmcTenantToken(param);
    }

    @Override
    public FeiShuCardMsgResult sendFeiShuCardMessage(final String authorization,
                                                     final FeiShuCardMsgParam cardMsgParam) {
        return feiShuDeligator.sendFeiShuCardMessage(authorization, cardMsgParam);
    }

    @Override
    public FeiShuTenant getTenant(final String authorization) {
        return feiShuDeligator.getTenant(authorization);
    }

    @Override
    public FeiShuReplyMsgResult replyRobotMessage(final String authorization,
                                                  final String messageId,
                                                  final FeiShuReplyMessageParam replyMessageParam) {
        return feiShuDeligator.replyRobotMessage(authorization, messageId, replyMessageParam);
    }


    @Override
    public CorpOrderRecordsWrapper<MallCorpOrderRecord> queryMallCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam) {
        return mallDeligator.queryMallCorpOrderRecords(queryParam);
    }

    @Override
    public PnrInfo queryPnrInfo(final PnrInfoQueryParam queryParam) {
        return pnrDeligator.queryPnrInfo(queryParam);
    }
    @Override
    public FeiShuLoginUserInfo getFeiShuLoginUserInfo(final String authorization,
                                                      final FeiShuLoginParam feiShuLoginParam) {
        return feiShuDeligator.getFeiShuLoginUserInfo(authorization, feiShuLoginParam);
    }

    @Override
    public ExternalTokenResult tmcAppToken(final AppAccessTokenParam param) {
        return feiShuDeligator.tmcAppToken(param);
    }

    @Override
    public String reconciliationDataSendBack(NyyhPostSaleProcessParam param) {
        return nyyhPostSaleDeligator.reconciliationDataSendBack(param);
    }

    @Override
    public String nyjkCallback(NyyhPostSaleProcessParam param) {
        return nyjkPostSaleDeligator.nyjkCallback(param);
    }

    @Override
    public String invoiceDataSendBack(NyyhPostSaleProcessParam param) {
        return nyyhPostSaleDeligator.invoiceDataSendBack(param);
    }

	@Override
	public String waybillDataSendBack(NyyhPostSaleProcessParam param)
	{
		return nyyhPostSaleDeligator.waybillDataSendBack(param);
	}

	@Override
	public String reconciliationDataNylcSendBack(NyyhPostSaleProcessParam param)
	{
		return nyyhPostSaleDeligator.reconciliationDataNylcSendBack(param);
	}

	@Override
	public String invoiceDataNyclSendBack(NyyhPostSaleProcessParam param)
	{
		return nyyhPostSaleDeligator.invoiceDataNyclSendBack(param);
	}

	@Override
	public String waybillDataNyclSendBack(NyyhPostSaleProcessParam param)
	{
		return nyyhPostSaleDeligator.waybillDataNyclSendBack(param);
	}

	@Override
    public String createShortUrl(ShortUrlParam param){
        return carDeligator.createShortUrl(param);
    }

    @Override
    public YuHongNofifResult easypnpHotelOrderNotif(CooperateHoterOrderMsg msg) {
        return easypnpOrderDeligator.easypnpHotelOrderNotif(msg);
    }

    @Override
    public YuHongNofifResult yuHongHotelOrderNotif(CooperateHoterOrderMsg msg) {
        return yuHongHotelOrderDeligator.yuHongHotelOrderNotif(msg);
    }

    @Override
    public CorpResaleOrderRecsWrapper<IftResaleOrderConsumption> queryIftCorpResaleOrderRecs(CorpResaleOrderRecsQueryParam queryParam) {
        return internationalFlightTicketOrderCenterDeligator.queryIftCorpResaleOrderRecs(queryParam);
    }

    @Override
    public CorpResaleOrderRecsWrapper<TourismResaleOrderConsumption> queryTourismCorpResaleOrderRecords(CorpResaleOrderRecsQueryParam queryParam) {
        return tourismResaleDeligator.queryTourismCorpResaleOrderRecords(queryParam);
    }

    @Override
    public SuiteAccessTokenResult getSuiteAccessToken(SuiteTokenParam suiteTokenParam) {
        return wechatDeligator.getSuiteAccessToken(suiteTokenParam);
    }

    @Override
    public PreAuthCodeResult getPreAuthCode(String suiteAccessToken) {
        return wechatDeligator.getPreAuthCode(suiteAccessToken);
    }

    @Override
    public CorpPermanentCodeResult getPermanentCode(String suiteAccessToken, PermanentCodeParam permanentCodeParam) {
        return wechatDeligator.getPermanentCode(suiteAccessToken, permanentCodeParam);
    }

    @Override
    public CorpAccessTokenResult getCorpToken(String suiteAccessToken, CorpTokenParam corpTokenParam) {
        return wechatDeligator.getCorpToken(suiteAccessToken, corpTokenParam);
    }

    @Override
    public WechatBaseResult setSessionInfo(String suiteAccessToken, SessionInfoParam sessionInfoParam) {
        return wechatDeligator.setSessionInfo(suiteAccessToken, sessionInfoParam);
    }

    @Override
    public CorpResaleOrderIdsWrapper queryDftCorpOrderIds(final CorpOrderRecordsQueryParam queryParam) {
        return domesticFlightTicketOrderCenterDeligator.queryDftCorpOrderIds(queryParam);
    }

    @Override
    public CorpResaleOrderIdsWrapper queryDftCorpResaleOrderIds(CorpResaleOrderRecsQueryParam queryParam) {
        return domesticFlightTicketOrderCenterDeligator.queryDftCorpResaleOrderIds(queryParam);
    }

    @Override
    public YonYouPageWrapper<YonYouSettlementOrder> querySettlementOrders(YonYouSettlementOrderQueryReq queryReq) {
        return yonYouTrvlDeligator.querySettlementOrders(queryReq);
    }

    @Override
    public OrderSensitiveInfoVO querySensitiveInfo(OrderSensitiveQueryParam orderSensitiveQueryParam) {
        return domesticFlightTicketOrderSensitiveDeligator.querySensitiveInfo(orderSensitiveQueryParam);
    }

    @Override
    public TrainOrderSensitiveInfoVO queryOrderSensitiveInfo(TrainOrderSensitiveQueryParam trainOrderSensitiveQueryParam) {
        return trainTicketDeligator.queryOrderSensitiveInfo(trainOrderSensitiveQueryParam);
    }

    @Deprecated
    @Override
    public String corpInvoiceNotify(String corpCode, CooperatedInvoiceApplyMsg cooperatedInvoiceApplyMsg) {
        return corpInvoiceNotifyDeligator.corpInvoiceNotify(corpCode, cooperatedInvoiceApplyMsg);
    }

    @Override
    public String chatByTripNow(ChatParam chatParam) {
        return tripNowApiDeligator.chatByTripNow(chatParam);
    }

    @Override
    public String userReChargeBalance(RechargeBalanceRequest rechargeBalanceRequest) {
        return paymentAccountSysDeligator.userReChargeBalance(rechargeBalanceRequest);
    }

    @Override
    public MerchantInfoZhongtaiVO queryMerchantInfo(final MerchantInfoQueryParam queryParam) {
        return huoliZhongtaiDeligator.queryMerchantInfo(queryParam);
    }

    @Override
    public void syncCorpSettleBill(final CorpSettleBillExternalVO corpSettleBill) {
        settleMoneyMgmtSysDeligator.syncCorpSettleBill(corpSettleBill);
    }

    @Override
    public void removeCorpSettleBill(final Long settleBillId) {
        settleMoneyMgmtSysDeligator.removeCorpSettleBill(settleBillId);
    }

    @Override
    public FlightCouponListResult queryFlightCoupon(FlightCouponQueryParam param) {
        return flightCouponDeligator.queryFlightCoupon(param);
    }

    @Override
    public FlightCouponManageListResult queryFlightCouponManage(FlightCouponManageQueryParam param) {
        return flightCouponDeligator.queryFlightCouponManage(param);
    }

    @Override
    public String delete(FlightCouponManageDeleteParam param) {
        return flightCouponDeligator.delete(param);
    }

    @Override
    public String upload(FlightCouponManageUploadParam param) {
        return flightCouponDeligator.upload(param);
    }

    @Override
    public FlightBoardingInfo queryFlightBoardingInfo(final FlightBoardingInfoQueryParam queryParam) {
        return flightDynamicsSvcDeligator.queryFlightBoardingInfo(queryParam);
    }

    @Override
    public void uploadHotelOrderInvInfo(final VetechInvInfoUploadReq uploadReq) {
        vetechFCDeligator.uploadHotelOrderInvInfo(uploadReq);
    }

    @Override
    public void uploadDftOrderInvInfo(final VetechInvInfoUploadReq uploadReq) {
        vetechFCDeligator.uploadDftOrderInvInfo(uploadReq);
    }
}
