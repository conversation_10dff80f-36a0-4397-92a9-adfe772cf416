package com.huoli.ctar.cfesag.client.deligator.train;

import com.huoli.ctar.cfesag.external.sys.api.train.model.*;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.CheckPassengerParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.CheckPassengerResultVO;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.QueryCheckPassengerStatusParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.QueryCheckPassengerStatusResultVO;
import com.huoli.ctar.core.infra.model.BaseResult;

import java.util.List;
import java.util.Map;

public interface TrainQueryDeligator {
    TrainQueryRecord queryTrainListByStation(TrainQueryParam param);

    QueryTrainDataResultVO queryTrainData(QueryTrainDataParam param);

    /**
     * 出行人手机号核验
     *
     * @param param
     * @return
     */
    CheckPassengerResultVO checkPassenger(CheckPassengerParam param);

    /**
     * 核验状态查询
     *
     * @param param
     * @return
     */
    QueryCheckPassengerStatusResultVO queryCheckPassengerStatus(QueryCheckPassengerStatusParam param);

    /**
     * 查询火车票站点信息
     *
     * @return 站点信息
     */
    List<StationQueryResult.Station> queryTrainStations();

    List<StationQueryResult.Station> queryTrainStationByCityName(final String cityName);

    List<StationQueryResult.Station> queryTrainStationByCityNameFuzzyMatching(final String cityName);

    Map<String, String> queryCityNameStationName(String cityList);
}
