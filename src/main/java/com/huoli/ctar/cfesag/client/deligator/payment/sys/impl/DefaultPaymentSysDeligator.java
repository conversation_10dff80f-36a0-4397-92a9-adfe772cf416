package com.huoli.ctar.cfesag.client.deligator.payment.sys.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.sys.PaymentSysDeligator;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CaissaFlowFileQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.payment.gateway.model.CorpTransRecordNew;
import com.huoli.ctar.cfesag.external.sys.api.payment.gateway.model.QueryCorpTransFlowRequest;
import com.huoli.ctar.cfesag.external.sys.api.payment.sys.model.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;
import com.huoli.utils.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class DefaultPaymentSysDeligator extends BaseDeligator implements PaymentSysDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultPaymentSysDeligator.class);

    public DefaultPaymentSysDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String getPayOrderId(RetrievePayOrderIdRequest retrievePayOrderIdRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<RetrievePayOrderIdRequest> httpEntity = new HttpEntity<>(retrievePayOrderIdRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/sys/payOrderId/obtain";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public ResultWithRefund refundPayOrder(PayRefundRequest payRefundRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PayRefundRequest> httpEntity = new HttpEntity<>(payRefundRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/sys/refund";

        ResponseEntity<BaseResult<ResultWithRefund>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<ResultWithRefund>>() {
        });
        final BaseResult<ResultWithRefund> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public ResultWithPayOrderId createPayOrder(RetrievePayOrderIdRequest retrievePayOrderIdRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<RetrievePayOrderIdRequest> httpEntity = new HttpEntity<>(retrievePayOrderIdRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/sys/createPayOrder";

        ResponseEntity<BaseResult<ResultWithPayOrderId>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<ResultWithPayOrderId>>() {
        });
        final BaseResult<ResultWithPayOrderId> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String getQrCodeOfPayment(RetrieveQrCodeRequest retrieveQrCodeRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<RetrieveQrCodeRequest> httpEntity = new HttpEntity<>(retrieveQrCodeRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/sys/payQrCode/obtain";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String corpPay(final CorpPayRequest corpPayRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpPayRequest> httpEntity = new HttpEntity<>(corpPayRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/sys/corpPay";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    /**
     * 支付侧批量扣款接口
     */
    @Override
    public BatchPayApplyResult batchPayApply(BatchPayApplyParam batchPayApplyParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<BatchPayApplyParam> httpEntity = new HttpEntity<>(batchPayApplyParam, httpHeaders);
        ResponseEntity<BaseResult<BatchPayApplyResult>> response;
        try {
            final String url = this.getCfesagHost() + "/external/sys/api/payment/sys/biz-support/batchPay/apply";
            if (log.isInfoEnabled()) {
                log.info(String.format("url: %s, httpMethod: %s, contentType: %s, requestBody: %s",
                        url, HttpMethod.POST.name(), MediaType.APPLICATION_JSON_UTF8, batchPayApplyParam));
            }
            response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<BatchPayApplyResult>>() {
            });
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
        final BaseResult<BatchPayApplyResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<CorpTransRecordNew>> queryCorpTradeRecord(Long corpId, Integer phoneId, Long transTimeFrom, Long transTimeTo, Integer pageNo, Integer pageSize) {
        return queryCorpTradeRecord(corpId, null, phoneId, transTimeFrom, transTimeTo, pageNo, pageSize);
    }

    @Override
    public List<OrderPaymentInfo> queryOrderPaymentInfoByOrderId(final String orderId) {
        if (StringUtil.isBlank(orderId)) {
            return Collections.emptyList();
        }
        return queryOrderPaymentInfoByOrderIds(Arrays.asList(orderId));
    }

    @Override
    public List<OrderPaymentInfo> queryOrderPaymentInfoByOrderIds(final List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        final HttpHeaders httpHeaders = constructCommonHeaders();
        OrderPaymentInfoQueryParameter orderPaymentInfoQueryParameter = new OrderPaymentInfoQueryParameter();
        orderPaymentInfoQueryParameter.setOrderIds(orderIds);
        final HttpEntity<OrderPaymentInfoQueryParameter> httpEntity = new HttpEntity<>(orderPaymentInfoQueryParameter, httpHeaders);
        ResponseEntity<BaseResult<List<OrderPaymentInfo>>> response;
        try {
            final String url = String.format("%s/external/sys/api/payment/sys/orderPaymentInfo/query", this.getCfesagHost());
            response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<OrderPaymentInfo>>>() {
            });
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
        final BaseResult<List<OrderPaymentInfo>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<CorpTransRecordNew>> queryCorpTradeRecord(final Long corpId,
                                                                      final List<String> orderIds,
                                                                      final Integer phoneId,
                                                                      final Long transTimeFrom,
                                                                      final Long transTimeTo,
                                                                      final Integer pageNo,
                                                                      final Integer pageSize) {
        final QueryCorpTransFlowRequest queryCorpTransFlowRequest = new QueryCorpTransFlowRequest();
        queryCorpTransFlowRequest.setCorpId(corpId);
        queryCorpTransFlowRequest.setOrderIds(CollectionUtils.isEmpty(orderIds) ? null : StringUtils.join(orderIds, ","));
        queryCorpTransFlowRequest.setPhoneId(phoneId);
        queryCorpTransFlowRequest.setIsNeedCoupon(0);
        queryCorpTransFlowRequest.setTimeStart(transTimeFrom);
        queryCorpTransFlowRequest.setTimeEnd(transTimeTo);
        queryCorpTransFlowRequest.setPageNum(pageNo);
        queryCorpTransFlowRequest.setPageSize(pageSize);

        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QueryCorpTransFlowRequest> httpEntity = new HttpEntity<>(queryCorpTransFlowRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/sys/corpTradeRecord/query";

        ResponseEntity<BaseResult<PageWrapper<List<CorpTransRecordNew>>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<CorpTransRecordNew>>>>() {
        });
        final BaseResult<PageWrapper<List<CorpTransRecordNew>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String generateCaissaFlowFile(final CaissaFlowFileQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CaissaFlowFileQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/sys/caissaflowfile/generate";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
}
