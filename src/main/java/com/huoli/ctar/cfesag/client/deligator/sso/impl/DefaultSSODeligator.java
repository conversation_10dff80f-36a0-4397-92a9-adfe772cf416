package com.huoli.ctar.cfesag.client.deligator.sso.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.sso.SSODeligator;
import com.huoli.ctar.cfesag.external.sys.api.sso.model.SSOUser;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultSSODeligator extends BaseDeligator implements SSODeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultSSODeligator.class);

    public DefaultSSODeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public SSOUser getSSOUser(String name) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/sso/users/%s", name);

        ResponseEntity<BaseResult<SSOUser>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<SSOUser>>() {
        });
        final BaseResult<SSOUser> result = checkResponse(response);
        return result.getData();
    }
}
