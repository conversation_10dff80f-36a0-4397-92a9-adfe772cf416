package com.huoli.ctar.cfesag.client.deligator.cosps.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.cosps.ReimbursementReceiptDeligator;
import com.huoli.ctar.cfesag.external.sys.api.cosps.model.DftReimbursementReceiptRecordVO;
import com.huoli.ctar.cfesag.external.sys.api.cosps.model.PkgRecordRequestParams;
import com.huoli.ctar.cfesag.external.sys.api.cosps.model.ReimbursementReceiptPkgRecordVO;
import com.huoli.ctar.cfesag.external.sys.api.cosps.model.ReimbursementReceiptSummitRecordVO;
import com.huoli.ctar.cfesag.external.sys.api.hotel.model.CreateHotelResult;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.util.TraceContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DefaultReimbursementReceiptDeligator extends BaseDeligator implements ReimbursementReceiptDeligator {

    public DefaultReimbursementReceiptDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String generateCorpReimbursementReport(Long corpId, String month, Integer type, String loginName) {
        Map<String, Object> map = new HashMap<>();
        map.put("corpId", corpId);
        map.put("month", month);
        map.put("type", type);
        map.put("loginName", loginName);

        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append(this.getCfesagHost());
        urlBuilder.append("/external/sys/api/cosps/rrh/report/generate");

        String params = buildUrlParams(map);
        if (!CollectionUtils.isEmpty(map)) {
            urlBuilder.append(String.format("?%s", params));
        }

        String url = urlBuilder.toString();
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, null, map, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public ReimbursementReceiptPkgRecordVO getReimbursementReceiptPkgRecord(Long corpId, String calStartDate, String calEndDate) {
        Map<String, Object> map = new HashMap<>();
        map.put("corpId", corpId);
        map.put("calStartDate", calStartDate);
        map.put("calEndDate", calEndDate);

        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append(this.getCfesagHost());
        urlBuilder.append("/external/sys/api/cosps/rrh/reimbursementReceiptPkgRecords");

        String params = buildUrlParams(map);
        if (!CollectionUtils.isEmpty(map)) {
            urlBuilder.append(String.format("?%s", params));
        }

        String url = urlBuilder.toString();
        ResponseEntity<BaseResult<ReimbursementReceiptPkgRecordVO>> response = execute(url, HttpMethod.GET, null, map, new ParameterizedTypeReference<BaseResult<ReimbursementReceiptPkgRecordVO>>() {
        });
        final BaseResult<ReimbursementReceiptPkgRecordVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String pkgRecord(Long corpId, String calStartDate, String calEndDate, List<String> invoiceList) {
        HttpHeaders headers = constructCommonHeaders();

        PkgRecordRequestParams pkgRecordRequestParams = new PkgRecordRequestParams();
        pkgRecordRequestParams.setCorpId(String.valueOf(corpId));
        pkgRecordRequestParams.setCalStartDate(calStartDate);
        pkgRecordRequestParams.setCalEndDate(calEndDate);
        pkgRecordRequestParams.setInvoiceList(invoiceList);

        HttpEntity<PkgRecordRequestParams> httpEntity = new HttpEntity<>(pkgRecordRequestParams, headers);

        String url = String.format("%s/external/sys/api/cosps/rrh/reimbursementReceiptPkgRecords/pkgRecord", this.getCfesagHost());
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);

        return result.getData();
    }

    @Override
    public String getCorpPostId(Long corpId, String calStartDate, String calEndDate) {
        Map<String, Object> map = new HashMap<>();
        map.put("corpId", corpId);
        map.put("calStartDate", calStartDate);
        map.put("calEndDate", calEndDate);

        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append(this.getCfesagHost());
        urlBuilder.append("/external/sys/api/cosps/rrh/domesticFlightTicket/postId");

        String params = buildUrlParams(map);
        if (!CollectionUtils.isEmpty(map)) {
            urlBuilder.append(String.format("?%s", params));
        }

        String url = urlBuilder.toString();
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.GET, null, map, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<DftReimbursementReceiptRecordVO> getDftReimbursementRecords(final List<String> orderIds) {
        HttpHeaders headers = constructCommonHeaders();
        HttpEntity<List<String>> httpEntity = new HttpEntity<>(orderIds, headers);

        String url = String.format("%s/external/sys/api/cosps/rrh/dft/reimbursementRecords", this.getCfesagHost());
        ResponseEntity<BaseResult<List<DftReimbursementReceiptRecordVO>>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<List<DftReimbursementReceiptRecordVO>>>() {
                });
        final BaseResult<List<DftReimbursementReceiptRecordVO>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<ReimbursementReceiptSummitRecordVO> getSummitRecords(final List<String> postIds) {
        HttpHeaders headers = constructCommonHeaders();
        HttpEntity<List<String>> httpEntity = new HttpEntity<>(postIds, headers);

        String url = String.format("%s/external/sys/api/cosps/rrh/corp/summitRecords", this.getCfesagHost());
        ResponseEntity<BaseResult<List<ReimbursementReceiptSummitRecordVO>>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<List<ReimbursementReceiptSummitRecordVO>>>() {
                });
        final BaseResult<List<ReimbursementReceiptSummitRecordVO>> result = checkResponse(response);
        return result.getData();
    }
}
