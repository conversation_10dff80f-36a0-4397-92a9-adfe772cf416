package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.viphall.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.viphall.DomesticFlightTicketVipHallDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.book.model.DftFlightConfirmParam;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.vipHall.model.VipHallParam;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.vipHall.model.VipHallResult;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultDomesticFlightTicketVipHallDeligator extends BaseDeligator implements DomesticFlightTicketVipHallDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultDomesticFlightTicketVipHallDeligator.class);

    public DefaultDomesticFlightTicketVipHallDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public VipHallResult queryVipHall(String phoneid, String airportCode, String departTime, String proTerminal){
        HttpHeaders httpHeaders = this.constructCommonHeaders();
        VipHallParam vipHallParam = new VipHallParam();
        vipHallParam.setPhoneid(phoneid);
        vipHallParam.setAirportCode(airportCode);
        vipHallParam.setDepartTime(departTime);
        vipHallParam.setProTerminal(proTerminal);
        HttpEntity<DftFlightConfirmParam> httpEntity = new HttpEntity(vipHallParam, httpHeaders);
        String url = String.format("%s/external/sys/api/vip/queryVipHall", this.getCfesagHost());
        ResponseEntity<BaseResult<VipHallResult>> response = this.execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<VipHallResult>>() {
        });
        BaseResult<VipHallResult> result = this.checkResponseNotCheckCode(response);
        return result.getData();
    }

}
