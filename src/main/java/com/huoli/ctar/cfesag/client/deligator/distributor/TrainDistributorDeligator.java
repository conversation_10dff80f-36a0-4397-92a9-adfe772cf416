package com.huoli.ctar.cfesag.client.deligator.distributor;

import com.huoli.ctar.cfesag.external.sys.api.distributor.model.*;
import com.huoli.ctar.cfesag.external.sys.api.distributor.vo.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.springframework.web.bind.annotation.RequestBody;

public interface TrainDistributorDeligator {

    TrainOccupyingASeatResult occupyingASeat(TrainOccupyingASeatParam param);

    TrainConfirmTicketResult confirmTicket( TrainConfirmTicketParam param);

    TrainCancelOrderResult cancelOrder( TrainDistributorCancelOrderParam param);


    TrainRefundTicketResult refundTicket( TrainDistributorRefundTicketParam param);

    TrainChangeTicketResult changeTicket( TrainChangeTicketParam param);

    TrainConfirmChangeTicketResult confirmChange( TrainConfirmChangeTicketParam param);

    TrainCancelChangeTicketResult cancelChange(TrainCancelChangeTicketParam param);

    TrainBuyTicketResult buyTicket(TrainBuyTicketParam param);

    String platformCancelOrder(BaseOrderParams params);

    String platformRefundTicket(BaseOrderParams params);

    String platformCancelChange(BaseOrderParams params);

}
