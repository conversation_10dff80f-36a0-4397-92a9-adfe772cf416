package com.huoli.ctar.cfesag.client.deligator.train;

import com.huoli.ctar.cfesag.external.sys.api.train.model.CostCentersResultVO;
import com.huoli.ctar.cfesag.external.sys.api.train.model.PlatformBuyOrderCreate;
import com.huoli.ctar.cfesag.external.sys.api.train.model.TrainCreateOrderParam;

public interface TrainUnifiedDeligator {
    CostCentersResultVO queryCostCenters(String phoneIdOfBookedBy);

    PlatformBuyOrderCreate createOrderUnified(TrainCreateOrderParam param);
}
