package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.support.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.support.DomesticFlightTicketBookingSupportDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.booking.support.model.DomesticFlightTicketTransFlowRecord;
import com.huoli.ctar.common.utils.DisplayUtil;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Objects;

public class DefaultDomesticFlightTicketBookingSupportDeligator extends BaseDeligator implements DomesticFlightTicketBookingSupportDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultDomesticFlightTicketBookingSupportDeligator.class);

    public DefaultDomesticFlightTicketBookingSupportDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public List<DomesticFlightTicketTransFlowRecord> loadDomesticFlightTicketTransFlowRecords(final String corpId, final String orderId, final Long transTimeFrom, final Long transTimeTo) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/domesticFlightTicket/booking/support/transFlowRecords?corpId=%s&orderId=%s&transTimeFrom=%s&transTimeTo=%s",
                DisplayUtil.display(corpId),
                DisplayUtil.display(orderId),
                Objects.isNull(transTimeFrom) ? "" : String.valueOf(transTimeFrom),
                Objects.isNull(transTimeTo) ? "" : String.valueOf(transTimeTo));

        ResponseEntity<BaseResult<List<DomesticFlightTicketTransFlowRecord>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<List<DomesticFlightTicketTransFlowRecord>>>() {
        });
        final BaseResult<List<DomesticFlightTicketTransFlowRecord>> result = checkResponse(response);
        return result.getData();
    }
}
