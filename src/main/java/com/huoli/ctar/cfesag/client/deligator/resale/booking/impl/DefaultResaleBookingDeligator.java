package com.huoli.ctar.cfesag.client.deligator.resale.booking.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.booking.ResaleBookingDeligator;
import com.huoli.ctar.cfesag.client.exception.CfesagInvokeException;
import com.huoli.ctar.cfesag.external.sys.api.resale.booking.model.*;
import com.huoli.ctar.cfesag.external.sys.api.resale.booking.model.back.*;
import com.huoli.ctar.cfesag.external.sys.api.resale.booking.model.order.*;
import com.huoli.ctar.cfesag.external.sys.api.resale.booking.model.shelf.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;

public class DefaultResaleBookingDeligator extends BaseDeligator implements ResaleBookingDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultResaleBookingDeligator.class);

    public DefaultResaleBookingDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public ResaleUser register(ResaleUserRegisterRequest resaleUserRegisterRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ResaleUserRegisterRequest> httpEntity = new HttpEntity<>(resaleUserRegisterRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/user/register";

        ResponseEntity<BaseResult<ResaleUser>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<ResaleUser>>() {
        });
        final BaseResult<ResaleUser> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String updateResaleUser(ResaleUserUpdateRequest resaleUserUpdateRequest, String openId) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ResaleUserUpdateRequest> httpEntity = new HttpEntity<>(resaleUserUpdateRequest, httpHeaders);
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/user/%s", openId);

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.PUT, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<DomesticFlightTicketOrder>> loadOrders(String openId, String orderId, String orderStatus, String passengerName, String idCard, String ticketNo, Long orderCreateTimeFrom,
                                                                   Long orderCreateTimeTo, Integer pageNo, Integer pageSize) {
        final List<String> queryParams = new ArrayList<>();
        if (StringUtils.isNotBlank(openId)) {
            queryParams.add(String.format("openId=%s", openId));
        }
        if (StringUtils.isNotBlank(orderId)) {
            queryParams.add(String.format("orderId=%s", orderId));
        }
        if (StringUtils.isNotBlank(orderStatus)) {
            queryParams.add(String.format("orderStatus=%s", orderStatus));
        }
        if (StringUtils.isNotBlank(passengerName)) {
            queryParams.add(String.format("passengerName=%s", passengerName));
        }
        if (StringUtils.isNotBlank(idCard)) {
            queryParams.add(String.format("idCard=%s", idCard));
        }
        if (StringUtils.isNotBlank(ticketNo)) {
            queryParams.add(String.format("ticketNo=%s", ticketNo));
        }
        if (Objects.nonNull(orderCreateTimeFrom)) {
            queryParams.add(String.format("orderCreateTimeFrom=%s", orderCreateTimeFrom));
        }
        if (Objects.nonNull(orderCreateTimeTo)) {
            queryParams.add(String.format("orderCreateTimeTo=%s", orderCreateTimeTo));
        }
        if (Objects.nonNull(pageNo)) {
            queryParams.add(String.format("pageNo=%d", pageNo));
        }
        if (Objects.nonNull(pageSize)) {
            queryParams.add(String.format("pageSize=%d", pageSize));
        }
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/order/flightOrders?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : StringUtils.join(queryParams, "&"));

        ResponseEntity<BaseResult<PageWrapper<List<DomesticFlightTicketOrder>>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<PageWrapper<List<DomesticFlightTicketOrder>>>>() {
        });
        final BaseResult<PageWrapper<List<DomesticFlightTicketOrder>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<OutputProductsFilter>> loadDistributeFilters(Integer id, String clientId, String dataSrc, Integer whiteFlag, Integer pageNo, Integer pageSize) {
        final List<String> queryParams = new ArrayList<>();
        if (Objects.nonNull(id)) {
            queryParams.add(String.format("id=%d", id));
        }
        if (StringUtils.isNotBlank(clientId)) {
            queryParams.add(String.format("clientId=%s", clientId));
        }
        if (StringUtils.isNotBlank(dataSrc)) {
            queryParams.add(String.format("dataSrc=%s", dataSrc));
        }
        if (Objects.nonNull(whiteFlag)) {
            queryParams.add(String.format("whiteFlag=%d", whiteFlag));
        }
        if (Objects.nonNull(pageNo)) {
            queryParams.add(String.format("pageNo=%d", pageNo));
        }
        if (Objects.nonNull(pageSize)) {
            queryParams.add(String.format("pageSize=%d", pageSize));
        }
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/productFilters?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : StringUtils.join(queryParams, "&"));

        ResponseEntity<BaseResult<PageWrapper<List<OutputProductsFilter>>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<PageWrapper<List<OutputProductsFilter>>>>() {
        });
        final BaseResult<PageWrapper<List<OutputProductsFilter>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public OutputProductsFilter createDistributeFilter(OutputProductsFilterParam outputProductsFilterParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<OutputProductsFilterParam> httpEntity = new HttpEntity<>(outputProductsFilterParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/productFilters";

        ResponseEntity<BaseResult<OutputProductsFilter>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<OutputProductsFilter>>() {
        });
        final BaseResult<OutputProductsFilter> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public OutputProductsFilter updateDistributeFilter(OutputProductsFilterParam outputProductsFilterParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<OutputProductsFilterParam> httpEntity = new HttpEntity<>(outputProductsFilterParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/productFilters/update";

        ResponseEntity<BaseResult<OutputProductsFilter>> response = execute(url, HttpMethod.PUT, httpEntity, new ParameterizedTypeReference<BaseResult<OutputProductsFilter>>() {
        });
        final BaseResult<OutputProductsFilter> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String deleteDistributeFilter(Integer id) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/productFilters/%d", id);

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.DELETE, null, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<PartnerOutputConfig>> loadDistributePartners(Integer id, String clientId, Integer allowNonStand, Integer pageNo, Integer pageSize) {
        final List<String> queryParams = new ArrayList<>();
        if (Objects.nonNull(id)) {
            queryParams.add(String.format("id=%d", id));
        }
        if (StringUtils.isNotBlank(clientId)) {
            queryParams.add(String.format("clientId=%s", clientId));
        }
        if (Objects.nonNull(allowNonStand)) {
            queryParams.add(String.format("allowNonStand=%d", allowNonStand));
        }
        if (Objects.nonNull(pageNo)) {
            queryParams.add(String.format("pageNo=%d", pageNo));
        }
        if (Objects.nonNull(pageSize)) {
            queryParams.add(String.format("pageSize=%d", pageSize));
        }

        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/partnerConfigs?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : StringUtils.join(queryParams, "&"));

        ResponseEntity<BaseResult<PageWrapper<List<PartnerOutputConfig>>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<PageWrapper<List<PartnerOutputConfig>>>>() {
        });
        final BaseResult<PageWrapper<List<PartnerOutputConfig>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PartnerOutputConfig createDistributePartner(PartnerOutPutParam partnerOutPutParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PartnerOutPutParam> httpEntity = new HttpEntity<>(partnerOutPutParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/Shelf/partnerConfigs";

        ResponseEntity<BaseResult<PartnerOutputConfig>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PartnerOutputConfig>>() {
        });
        final BaseResult<PartnerOutputConfig> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PartnerOutputConfig updateDistributePartner(PartnerOutPutParam partnerOutPutParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PartnerOutPutParam> httpEntity = new HttpEntity<>(partnerOutPutParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/partnerConfigs/update";

        ResponseEntity<BaseResult<PartnerOutputConfig>> response = execute(url, HttpMethod.PUT, httpEntity, new ParameterizedTypeReference<BaseResult<PartnerOutputConfig>>() {
        });
        final BaseResult<PartnerOutputConfig> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String deleteDistributePartner(Integer id) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/partnerConfigs/%d", id);

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.DELETE, null, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<ProductPolicy>> loadDistributePolicies(Integer id, String clientId, String airLine, String isEffect, Integer pageNo, Integer pageSize) {
        final List<String> queryParams = new ArrayList<>();
        if (Objects.nonNull(id)) {
            queryParams.add(String.format("id=%d", id));
        }
        if (StringUtils.isNotBlank(clientId)) {
            queryParams.add(String.format("clientId=%s", clientId));
        }
        if (StringUtils.isNotBlank(airLine)) {
            queryParams.add(String.format("airLine=%s", airLine));
        }
        if (StringUtils.isNotBlank(isEffect)) {
            queryParams.add(String.format("isEffect=%s", isEffect));
        }
        if (Objects.nonNull(pageNo)) {
            queryParams.add(String.format("pageNo=%d", pageNo));
        }
        if (Objects.nonNull(pageSize)) {
            queryParams.add(String.format("pageSize=%d", pageSize));
        }

        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/productPolicies?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : StringUtils.join(queryParams, "&"));

        ResponseEntity<BaseResult<PageWrapper<List<ProductPolicy>>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<PageWrapper<List<ProductPolicy>>>>() {
        });
        final BaseResult<PageWrapper<List<ProductPolicy>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public ProductPolicy createDistributePolicy(ProductPolicyParam productPolicyParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ProductPolicyParam> httpEntity = new HttpEntity<>(productPolicyParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/productPolicies";

        ResponseEntity<BaseResult<ProductPolicy>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<ProductPolicy>>() {
        });
        final BaseResult<ProductPolicy> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public ProductPolicy updateDistributePolicy(ProductPolicyParam productPolicyParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ProductPolicyParam> httpEntity = new HttpEntity<>(productPolicyParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/productPolicies/update";

        ResponseEntity<BaseResult<ProductPolicy>> response = execute(url, HttpMethod.PUT, httpEntity, new ParameterizedTypeReference<BaseResult<ProductPolicy>>() {
        });
        final BaseResult<ProductPolicy> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String deleteDistributePolicy(Integer id) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/productPolicies/%d", id);

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.DELETE, null, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<ProductPolicyRule>> loadDistributePolicyRules(Integer pid, Integer pageNo, Integer pageSize) {
        final List<String> queryParams = new ArrayList<>();
        if (Objects.nonNull(pid)) {
            queryParams.add(String.format("pid=%d", pid));
        }
        if (Objects.nonNull(pageNo)) {
            queryParams.add(String.format("pageNo=%d", pageNo));
        }
        if (Objects.nonNull(pageSize)) {
            queryParams.add(String.format("pageSize=%d", pageSize));
        }

        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/productPolicyRules?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : StringUtils.join(queryParams, "&"));

        ResponseEntity<BaseResult<PageWrapper<List<ProductPolicyRule>>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<PageWrapper<List<ProductPolicyRule>>>>() {
        });
        final BaseResult<PageWrapper<List<ProductPolicyRule>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public ProductPolicyRule createDistributePolicyRule(ProductPolicyRuleParam productPolicyRuleParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ProductPolicyRuleParam> httpEntity = new HttpEntity<>(productPolicyRuleParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/productPolicyRules";

        ResponseEntity<BaseResult<ProductPolicyRule>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<ProductPolicyRule>>() {
        });
        final BaseResult<ProductPolicyRule> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String updateDistributePolicyRule(ProductPolicyRuleParam productPolicyRuleParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ProductPolicyRuleParam> httpEntity = new HttpEntity<>(productPolicyRuleParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/productPolicyRules/update";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.PUT, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String deleteDistributePolicyRule(Integer id) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/productPolicyRules/%d", id);

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.DELETE, null, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<ResaleGroupParam>> loadResaleGroups(Integer id) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/resaleGroups%s",
                Objects.nonNull(id) ? String.format("?id=%d", id) : "");

        ResponseEntity<BaseResult<PageWrapper<List<ResaleGroupParam>>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<PageWrapper<List<ResaleGroupParam>>>>() {
        });
        final BaseResult<PageWrapper<List<ResaleGroupParam>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String createResaleGroup(ResaleGroupParam resaleGroupParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ResaleGroupParam> httpEntity = new HttpEntity<>(resaleGroupParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/resaleGroups";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String updateResaleGroup(ResaleGroupParam resaleGroupParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ResaleGroupParam> httpEntity = new HttpEntity<>(resaleGroupParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/resaleGroups/update";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.PUT, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String deleteResaleGroup(Integer id) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/resaleGroups/%d", id);

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.DELETE, null, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public Map<String, List<FlightResaleDoc>> queryDocInfo() {
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/back/query/doc/info";

        ResponseEntity<BaseResult<Map<String, List<FlightResaleDoc>>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<Map<String, List<FlightResaleDoc>>>>() {
        });
        final BaseResult<Map<String, List<FlightResaleDoc>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DomesticFlightTicketOrderDetail getFlightTicketOrderDetail(String orderId) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/order/flightTicketOrders/%s", orderId);

        ResponseEntity<BaseResult<DomesticFlightTicketOrderDetail>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<DomesticFlightTicketOrderDetail>>() {
        });
        final BaseResult<DomesticFlightTicketOrderDetail> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<FlightTicketOrderLog>> loadFlightTicketOrderLogs(String orderId, Integer pageNo, Integer pageSize) {
        final List<String> queryParams = new ArrayList<>();
        if (StringUtils.isNotBlank(orderId)) {
            queryParams.add(String.format("orderId=%s", orderId));
        }
        if (Objects.nonNull(pageNo)) {
            queryParams.add(String.format("pageNo=%d", pageNo));
        }
        if (Objects.nonNull(pageSize)) {
            queryParams.add(String.format("pageSize=%d", pageSize));
        }

        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/flightTicketOrderLogs?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : StringUtils.join(queryParams, "&"));

        ResponseEntity<BaseResult<PageWrapper<List<FlightTicketOrderLog>>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<PageWrapper<List<FlightTicketOrderLog>>>>() {
        });
        final BaseResult<PageWrapper<List<FlightTicketOrderLog>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<ProductTypeConfig>> loadProductTypes(Integer pageNo, Integer pageSize) {
        final List<String> queryParams = new ArrayList<>();
        if (Objects.nonNull(pageNo)) {
            queryParams.add(String.format("pageNo=%d", pageNo));
        }
        if (Objects.nonNull(pageSize)) {
            queryParams.add(String.format("pageSize=%d", pageSize));
        }

        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/productTypeConfigs?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : StringUtils.join(queryParams, "&"));

        ResponseEntity<BaseResult<PageWrapper<List<ProductTypeConfig>>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<PageWrapper<List<ProductTypeConfig>>>>() {
        });
        final BaseResult<PageWrapper<List<ProductTypeConfig>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<OrderTransDetail>> queryTransRecords(DomesticFlightTransRecordQueryParams transRecordQueryParams) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DomesticFlightTransRecordQueryParams> httpEntity = new HttpEntity<>(transRecordQueryParams, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/back/order/info/query";

        ResponseEntity<BaseResult<PageWrapper<List<OrderTransDetail>>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<OrderTransDetail>>>>() {
        }, false);
        final BaseResult<PageWrapper<List<OrderTransDetail>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<OrderTransDetail> queryOrderStatusByOrderIds(OrderStatusQueryParam orderStatusQueryParam) {
        return queryStatus("/external/sys/api/resale/booking/back/order/ticket/status", orderStatusQueryParam);
    }

    @Override
    public List<OrderTransDetail> querySubOrderStatusByOrderIds(OrderStatusQueryParam orderStatusQueryParam) {
        return queryStatus("/external/sys/api/resale/booking/back/suborder/ticket/status", orderStatusQueryParam);
    }

    private List<OrderTransDetail> queryStatus(String url, OrderStatusQueryParam orderStatusQueryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<OrderStatusQueryParam> httpEntity = new HttpEntity<>(orderStatusQueryParam, httpHeaders);
        url = String.format("%s%s", this.getCfesagHost(), url);

        ResponseEntity<BaseResult<List<OrderTransDetail>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<OrderTransDetail>>>() {
        }, false);
        final BaseResult<List<OrderTransDetail>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<RerouteFlightResult> queryRerouteFlight(RerouteFlightParam rerouteFlightParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<RerouteFlightParam> httpEntity = new HttpEntity<>(rerouteFlightParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/back/reroute/flight/query";

        ResponseEntity<BaseResult<List<RerouteFlightResult>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<RerouteFlightResult>>>() {
        });
        final BaseResult<List<RerouteFlightResult>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public ReroutePriceConfirmResult reroutePriceConfirm(ReroutePriceConfirmParam reroutePriceConfirmParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ReroutePriceConfirmParam> httpEntity = new HttpEntity<>(reroutePriceConfirmParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/back/reroute/price/confirm";

        ResponseEntity<BaseResult<ReroutePriceConfirmResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<ReroutePriceConfirmResult>>() {
        });
        final BaseResult<ReroutePriceConfirmResult> result = checkResponse(response);

        ReroutePriceConfirmResult reroutePriceConfirmResult = result.getData();
        reroutePriceConfirmResult.setMsg(result.getMsg());
        reroutePriceConfirmResult.setCode(result.getCode());
        return reroutePriceConfirmResult;
    }

    @Override
    public RerouteSubmitResult rerouteSubmit(RerouteSubmitParam rerouteSubmitParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<RerouteSubmitParam> httpEntity = new HttpEntity<>(rerouteSubmitParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/back/reroute/submit";

        ResponseEntity<BaseResult<RerouteSubmitResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<RerouteSubmitResult>>() {
        });
        final BaseResult<RerouteSubmitResult> result = checkResponse(response);
        return result.getData();
    }


    @Override
    public ConfirmRefundResult refundFlight(RefundFlightParam refundFlightParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<RefundFlightParam> httpEntity = new HttpEntity<>(refundFlightParam, httpHeaders);
        ResponseEntity<BaseResult<ConfirmRefundResult>> response;
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/back/flight/refund";
        response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<ConfirmRefundResult>>() {
        });
        final BaseResult<ConfirmRefundResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public RefundQueryResult refundQuery(final RefundQueryParam refundQueryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<RefundQueryParam> httpEntity = new HttpEntity<>(refundQueryParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/back/refund/query";

        ResponseEntity<BaseResult<RefundQueryResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<RefundQueryResult>>() {
        });
        final BaseResult<RefundQueryResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    protected <T> BaseResult<T> checkResponse(ResponseEntity<BaseResult<T>> response) {
        final HttpStatus.Series series = HttpStatus.Series.valueOf(response.getStatusCode());
        if (HttpStatus.Series.CLIENT_ERROR == series || HttpStatus.Series.SERVER_ERROR == series) {
            if (Objects.nonNull(response.getBody())) {
                throw new CfesagInvokeException(response.getBody().getMsg());
            } else {
                throw new CfesagInvokeException(response.getStatusCode().getReasonPhrase());
            }
        }
        if (Objects.isNull(response.getBody())) {
            throw new CfesagInvokeException("网关服务器内部错误");
        }
        if (BaseResult.SUCCESS != response.getBody().getCode() && 2001 != response.getBody().getCode()) {
            throw new CfesagInvokeException(response.getBody().getCode(), response.getBody().getMsg());
        }
        return response.getBody();
    }

    @Override
    public List<RefundFlightParam.FileInfo> upload(List<RefundFlightParam.FileInfo> files) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<List<RefundFlightParam.FileInfo>> httpEntity = new HttpEntity<>(files, httpHeaders);
        ResponseEntity<BaseResult<Map<String, List<RefundFlightParam.FileInfo>>>> response;
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/order/upload";
        response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<Map<String, List<RefundFlightParam.FileInfo>>>>() {
        });
        final BaseResult<Map<String, List<RefundFlightParam.FileInfo>>> result = checkResponse(response);
        Map<String, List<RefundFlightParam.FileInfo>> map = result.getData();
        List<RefundFlightParam.FileInfo> urls = map.get("attachUrl");
        return urls;
    }


    @Override
    public String insertResaleLog(FlightResaleOrderLog flightResaleOrderLog) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<FlightResaleOrderLog> httpEntity = new HttpEntity<>(flightResaleOrderLog, httpHeaders);
        ResponseEntity<BaseResult<String>> response;
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/back/resale/log";
        response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }


    @Override
    public String insertResaleLogList(List<FlightResaleOrderLog> resaleOrderLogs) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<List<FlightResaleOrderLog>> httpEntity = new HttpEntity<>(resaleOrderLogs, httpHeaders);
        ResponseEntity<BaseResult<String>> response;
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/back/resale/log/list";
        response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<FlightResaleOrderLog>> loadAppealByPage(OrderLogParam orderLogParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<OrderLogParam> httpEntity = new HttpEntity<>(orderLogParam, httpHeaders);
        ResponseEntity<BaseResult<PageWrapper<List<FlightResaleOrderLog>>>> response;
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/back/resale/filterSelect";
        response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<FlightResaleOrderLog>>>>() {
        });
        final BaseResult<PageWrapper<List<FlightResaleOrderLog>>> result = checkResponse(response);
        return result.getData();
    }


    @Override
    public FlightSearchResult searchFlight(FlightSearchParam flightSearchParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<FlightSearchParam> httpEntity = new HttpEntity<>(flightSearchParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/shelf/query";

        ResponseEntity<BaseResult<FlightSearchResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FlightSearchResult>>() {
        });
        final BaseResult<FlightSearchResult> result = checkResponse(response);
        return result.getData();
    }


    @Override
    public FlightResult search(FlightSearchParam flightSearchParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<FlightSearchParam> httpEntity = new HttpEntity<>(flightSearchParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/flight/ic";
        ResponseEntity<BaseResult<FlightResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FlightResult>>() {
        });
        final BaseResult<FlightResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DomesticFlightTicketOrderDetail getDomesticFlightTicketDetail(String orderId) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/resale/booking/domesticFlight/order/detail?orderId=%s", orderId);

        ResponseEntity<BaseResult<DomesticFlightTicketOrderDetail>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<DomesticFlightTicketOrderDetail>>() {
        });
        final BaseResult<DomesticFlightTicketOrderDetail> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<ApiLogQueryResult>> apiLogQuery(ApiLogQueryParam apiLogQueryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ApiLogQueryParam> httpEntity = new HttpEntity<>(apiLogQueryParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale//back/api/log/query";
        ResponseEntity<BaseResult<PageWrapper<List<ApiLogQueryResult>>>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<PageWrapper<List<ApiLogQueryResult>>>>() {
                });
        final BaseResult<PageWrapper<List<ApiLogQueryResult>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String apiLogExport(ApiLogQueryParam apiLogQueryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ApiLogQueryParam> httpEntity = new HttpEntity<>(apiLogQueryParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/back/api/log/export";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<String>>() {
                });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<MonitorInfoResult> monitorInfoQuery(MonitorInfoQueryParam monitorInfoQueryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<MonitorInfoQueryParam> httpEntity = new HttpEntity<>(monitorInfoQueryParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/back/monitor/info/query";
        ResponseEntity<BaseResult<List<MonitorInfoResult>>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<List<MonitorInfoResult>>>() {
                });
        final BaseResult<List<MonitorInfoResult>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String monitorInfoExport(MonitorInfoQueryParam monitorInfoQueryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<MonitorInfoQueryParam> httpEntity = new HttpEntity<>(monitorInfoQueryParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/back/monitor/info/export";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<String>>() {
                });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String cancelPnr(CancelPnrParams cancelPnrParams) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CancelPnrParams> httpEntity = new HttpEntity<>(cancelPnrParams, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/back/cancelPnr";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<String>>() {
                });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }



    @Override
    public List<SearchApiCountDomesticFlightResult> queryApiCount(SearchApiCountDomesticFlightParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<SearchApiCountDomesticFlightParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/resale/booking/back/query/api/count";
        ResponseEntity<BaseResult<List<SearchApiCountDomesticFlightResult>>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<List<SearchApiCountDomesticFlightResult>>>() {
                });
        final BaseResult<List<SearchApiCountDomesticFlightResult>> result = checkResponse(response);
        return result.getData();
    }
}
