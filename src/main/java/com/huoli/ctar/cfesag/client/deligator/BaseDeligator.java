package com.huoli.ctar.cfesag.client.deligator;

import com.huoli.ctar.cfesag.client.exception.CfesagInvokeException;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.common.utils.JsonUtil;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.util.TraceContextUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;

public abstract class BaseDeligator {
    private final RestTemplate restTemplate;
    private final String cfesagHost;
    private final Logger log;

    public BaseDeligator(final RestTemplate restTemplate, final String cfesagHost, final Logger log) {
        this.restTemplate = restTemplate;
        this.cfesagHost = cfesagHost;
        this.log = log;
    }

    protected void cacheValue(final String key,
                              final Object value,
                              final long expireSeconds) {
        LocalCache.cacheValue(key, value, expireSeconds);
    }

    protected void cacheValue(final String key,
                              final Object value) {
        LocalCache.cacheValue(key, value);
    }

    protected Object getCacheValue(final String key) {
        return LocalCache.getCacheValue(key);
    }

    protected HttpHeaders constructCommonHeaders() {
        final HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.add(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());
        return headers;
    }

    protected <I> ResponseEntity<I> execute(String url, HttpMethod method, HttpEntity<?> httpEntity, ParameterizedTypeReference<I> responseType) {
        return execute(url, method, httpEntity, responseType, true);
    }

    protected <I> ResponseEntity<I> execute(String url, HttpMethod method, HttpEntity<?> httpEntity, ParameterizedTypeReference<I> responseType, boolean logEnable) {
        ResponseEntity<I> response;
        long startTime = System.currentTimeMillis();
        RequestContext requestContext = new RequestContext();

        try {
            if (log.isInfoEnabled()) {
                requestContext.setUrl(url);
                requestContext.setMethod(method.name());
                requestContext.setRequestHeaders(Objects.nonNull(httpEntity) ? httpEntity.getHeaders().toString() : "");
                requestContext.setRequestBodyText(Objects.nonNull(httpEntity) ? JsonUtil.toJson(httpEntity.getBody()) : "");
                requestContext.setOriginalRequest(getOriginalRequest());
            }

            if (Objects.isNull(httpEntity)) {
                HttpHeaders headers = new HttpHeaders();
                headers.add(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());
                httpEntity = new HttpEntity<>(headers);
            }
            response = getRestTemplate().exchange(url, method, httpEntity, responseType);

            requestContext.setResponseStatus(response.getStatusCodeValue());
            requestContext.setResponseBodyText(JsonUtil.toJson(response));
            requestContext.setResponseHeaders(response.getHeaders().toString());
        } catch (RestClientException e) {
            requestContext.setException(e);
            throw new CfesagUnavailableException(e.getMessage(), e);
        } finally {
            if (log.isInfoEnabled()) {
                requestContext.setElapsedTime(System.currentTimeMillis() - startTime);
                log.info(requestContext.logText(logEnable));
            }
        }

        return response;
    }

    protected <I> ResponseEntity<I> execute(String url, HttpMethod method, HttpEntity<?> httpEntity, Map<String, ?> uriVariables, ParameterizedTypeReference<I> responseType) {
        return execute(url, method, httpEntity, uriVariables, responseType, true);
    }

    protected <I> ResponseEntity<I> execute(String url, HttpMethod method, HttpEntity<?> httpEntity, Map<String, ?> uriVariables, ParameterizedTypeReference<I> responseType, boolean logEnable) {
        ResponseEntity<I> response = null;
        long startTime = System.currentTimeMillis();
        RequestContext requestContext = new RequestContext();
        try {
            if (log.isInfoEnabled()) {
                requestContext.setUrl(url);
                requestContext.setMethod(method.name());
                requestContext.setRequestHeaders(Objects.nonNull(httpEntity) ? httpEntity.getHeaders().toString() : "");
                requestContext.setRequestBodyText(Objects.nonNull(httpEntity) ? JsonUtil.toJson(httpEntity.getBody()) : "");
                requestContext.setUriVariables(Objects.nonNull(uriVariables) ? JsonUtil.toJson(uriVariables) : "");
                requestContext.setOriginalRequest(getOriginalRequest());
            }

            if (Objects.isNull(httpEntity)) {
                HttpHeaders headers = new HttpHeaders();
                headers.add(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());
                httpEntity = new HttpEntity<>(headers);
            }

            response = getRestTemplate().exchange(url, method, httpEntity, responseType, uriVariables);

            requestContext.setResponseStatus(response.getStatusCodeValue());
            requestContext.setResponseBodyText(JsonUtil.toJson(response));
            requestContext.setResponseHeaders(response.getHeaders().toString());
        } catch (RestClientException e) {
            requestContext.setException(e);
            throw new CfesagUnavailableException(e.getMessage(), e);
        } finally {
            if (log.isInfoEnabled()) {
                requestContext.setElapsedTime(System.currentTimeMillis() - startTime);
                log.info(requestContext.logText(logEnable));
            }
        }
        return response;
    }

    protected <T> BaseResult<T> checkResponse(ResponseEntity<BaseResult<T>> response) {
        final HttpStatus.Series series = HttpStatus.Series.valueOf(response.getStatusCode());
        if (HttpStatus.Series.CLIENT_ERROR == series || HttpStatus.Series.SERVER_ERROR == series) {
            if (Objects.nonNull(response.getBody())) {
                log.info(String.format("------%s", response.getBody().getMsg()));
                throw new CfesagInvokeException(response.getBody().getCode(), response.getBody().getMsg(), response.getBody().getButtons(), response.getBody().getTitle());
            } else {
                throw new CfesagInvokeException(response.getStatusCode().getReasonPhrase());
            }
        }
        if (Objects.isNull(response.getBody())) {
            throw new CfesagInvokeException("网关服务器内部错误");
        }
        if (BaseResult.SUCCESS != response.getBody().getCode()) {
            throw new CfesagInvokeException(response.getBody().getCode(), response.getBody().getMsg(), response.getBody().getButtons(), response.getBody().getTitle());
        }
        return response.getBody();
    }

    protected <T> BaseResult<T> checkResponseNotCheckCode(ResponseEntity<BaseResult<T>> response) {
        final HttpStatus.Series series = HttpStatus.Series.valueOf(response.getStatusCode());
        if (HttpStatus.Series.CLIENT_ERROR == series || HttpStatus.Series.SERVER_ERROR == series) {
            if (Objects.nonNull(response.getBody())) {
                log.info("------{}", response.getBody().getMsg());
                throw new CfesagInvokeException(response.getBody().getCode(), response.getBody().getMsg(), response.getBody().getButtons(), response.getBody().getTitle());
            } else {
                throw new CfesagInvokeException(response.getStatusCode().getReasonPhrase());
            }
        }
        if (Objects.isNull(response.getBody())) {
            throw new CfesagInvokeException("网关服务器内部错误");
        }
        return response.getBody();
    }

    protected String buildUrlParams(Map<String, ?> values) {
        if (CollectionUtils.isEmpty(values)) {
            return null;
        }
        StringBuilder params = new StringBuilder();
        values.keySet().forEach(param -> {
            if (params.length() > 0) {
                params.append("&");
            }
            params.append(String.format("%s={%s}", param, param));
        });
        return params.toString();
    }

    protected RestTemplate getRestTemplate() {
        return this.restTemplate;
    }

    protected String getCfesagHost() {
        return this.cfesagHost;
    }


    private OriginalRequest getOriginalRequest() {
        try {
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (Objects.isNull(requestAttributes)) {
                return null;
            }

            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            OriginalRequest originalRequest = new OriginalRequest();
            originalRequest.setUrl(request.getRequestURL().toString());
            originalRequest.setClientIp(getClientIp(request));
            return originalRequest;
        } catch (Exception ex) {
            return null;
        }
    }

    private String getClientIp(HttpServletRequest request) {
        if (Objects.isNull(request)) {
            return null;
        }
        String clientIp = request.getHeader("X-Real-IP");
        if (StringUtils.isBlank(clientIp)) {
            clientIp = request.getHeader("X-Forwarded-For");
        }
        if (StringUtils.isBlank(clientIp)) {
            clientIp = request.getRemoteAddr();
        }
        return clientIp;
    }


    @Getter
    @Setter
    public static class RequestContext {
        private String url;
        private String method;
        private String requestHeaders;
        private String uriVariables;
        private String requestBodyText;
        private int responseStatus;
        private String responseHeaders;
        private String responseBodyText;
        private long elapsedTime;
        private OriginalRequest originalRequest;

        private Exception exception;


        public String logText(boolean logEnable) {
            if (logEnable) {
                return String.format("client ip:%s, original url:%s, url:%s, method:%s, Request Header:%s, uri variables:%s, Request Body:%s, Request Cost:%sms, Response Status:%s, Response Header:{%s}, Response Text:%s %s",
                        Objects.isNull(originalRequest) || StringUtils.isBlank(originalRequest.getClientIp()) ? "" : originalRequest.getClientIp(),
                        Objects.isNull(originalRequest) || StringUtils.isBlank(originalRequest.getUrl()) ? "" : originalRequest.getUrl(),
                        StringUtils.isBlank(this.url) ? "" : this.url,
                        StringUtils.isBlank(this.method) ? "" : this.method,
                        StringUtils.isBlank(this.requestHeaders) ? "" : this.requestHeaders,
                        StringUtils.isBlank(this.uriVariables) ? "" : this.uriVariables,
                        StringUtils.isBlank(this.requestBodyText) ? "" : this.requestBodyText,
                        this.elapsedTime,
                        this.responseStatus,
                        StringUtils.isBlank(this.responseHeaders) ? "" : this.responseHeaders,
                        StringUtils.isBlank(this.responseBodyText) ? "" : this.responseBodyText,
                        Objects.nonNull(exception) ? String.format("Exception:%s", exception.getMessage()) : "");
            } else {
                return String.format("client ip:%s, original url:%s, url:%s, method:%s, Request Cost:%sms, Response Status:%s",
                        Objects.isNull(originalRequest) || StringUtils.isBlank(originalRequest.getClientIp()) ? "" : originalRequest.getClientIp(),
                        Objects.isNull(originalRequest) || StringUtils.isBlank(originalRequest.getUrl()) ? "" : originalRequest.getUrl(),
                        StringUtils.isBlank(this.url) ? "" : this.url,
                        StringUtils.isBlank(this.method) ? "" : this.method,
                        this.elapsedTime,
                        this.responseStatus);
            }
        }

    }


    @Getter
    @Setter
    public static class OriginalRequest {
        private String url;
        private String clientIp;
    }
}
