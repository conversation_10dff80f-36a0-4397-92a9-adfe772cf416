package com.huoli.ctar.cfesag.client.deligator.train.impl;

import java.util.*;

import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.TrainCorpOrderRecord;

import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.TrainResaleOrderConsumption;
import com.huoli.ctar.cfesag.external.sys.api.train.model.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainTicketDeligator;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.GetGtUserAccountParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.GetGtUserAccountResult;
import com.huoli.ctar.common.utils.DisplayUtil;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;
import com.huoli.ctar.core.infra.util.TraceContextUtil;
import com.huoli.utils.StringUtil;

public class DefaultTrainTicketDeligator extends BaseDeligator implements TrainTicketDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultTrainTicketDeligator.class);

    public DefaultTrainTicketDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public PageWrapper<List<TrainTicketOrder>> loadTrainTicketOrders(String phoneId, String orderId, String orderStatus, String passengerName, String cardNo, Long startTime, Long endTime, Integer pageNo, Integer pageSize) {
        final List<String> queryParams = new ArrayList<>();
        if (StringUtils.isNotBlank(phoneId)) {
            queryParams.add(String.format("phoneId=%s", phoneId));
        }
        if (StringUtils.isNotBlank(orderId)) {
            queryParams.add(String.format("orderId=%s", orderId));
        }
        if (StringUtils.isNotBlank(orderStatus)) {
            queryParams.add(String.format("orderStatus=%s", orderStatus));
        }
        if (StringUtils.isNotBlank(passengerName)) {
            queryParams.add(String.format("passengerName=%s", passengerName));
        }
        if (StringUtils.isNotBlank(cardNo)) {
            queryParams.add(String.format("cardNo=%s", cardNo));
        }
        if (Objects.nonNull(startTime)) {
            queryParams.add(String.format("startTime=%d", startTime));
        }
        if (Objects.nonNull(endTime)) {
            queryParams.add(String.format("endTime=%d", endTime));
        }
        if (Objects.nonNull(pageNo)) {
            queryParams.add(String.format("pageNo=%d", pageNo));
        }
        if (Objects.nonNull(pageSize)) {
            queryParams.add(String.format("pageSize=%d", pageSize));
        }

        final String url = String.format(this.getCfesagHost() + "/external/sys/api/train/dgout/trainTicketOrders?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : StringUtils.join(queryParams, "&"));

        ResponseEntity<BaseResult<PageWrapper<List<TrainTicketOrder>>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<PageWrapper<List<TrainTicketOrder>>>>() {
        });
        final BaseResult<PageWrapper<List<TrainTicketOrder>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<DistTrainTicketTransFlowRecord>> loadDistTrainTicketTransFlowRecords(String distId, String loginUserName, String phoneId, String orderId,
                                                                                                 String transType, String transChannel, String consumeType,
                                                                                                 Long transTimeFrom, Long transTimeTo, Integer pageNo, Integer pageSize) {
        final List<String> queryParams = new ArrayList<>();
        if (StringUtils.isNotBlank(distId)) {
            queryParams.add(String.format("distId=%s", distId));
        }
        if (StringUtils.isNotBlank(loginUserName)) {
            queryParams.add(String.format("loginUserName=%s", loginUserName));
        }
        if (StringUtils.isNotBlank(phoneId)) {
            queryParams.add(String.format("phoneId=%s", phoneId));
        }
        if (StringUtils.isNotBlank(orderId)) {
            queryParams.add(String.format("orderId=%s", orderId));
        }
        if (StringUtils.isNotBlank(transType)) {
            queryParams.add(String.format("transType=%s", transType));
        }
        if (StringUtils.isNotBlank(transChannel)) {
            queryParams.add(String.format("transChannel=%s", transChannel));
        }
        if (StringUtils.isNotBlank(consumeType)) {
            queryParams.add(String.format("consumeType=%s", consumeType));
        }
        if (Objects.nonNull(transTimeFrom)) {
            queryParams.add(String.format("transTimeFrom=%d", transTimeFrom));
        }
        if (Objects.nonNull(transTimeTo)) {
            queryParams.add(String.format("transTimeTo=%d", transTimeTo));
        }
        if (Objects.nonNull(pageNo)) {
            queryParams.add(String.format("pageNo=%d", pageNo));
        }
        if (Objects.nonNull(pageSize)) {
            queryParams.add(String.format("pageSize=%d", pageSize));
        }

        final String url = String.format(this.getCfesagHost() + "/external/sys/api/train/dist/distTrainTicketTransFlowRecords?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : StringUtils.join(queryParams, "&"));

        ResponseEntity<BaseResult<PageWrapper<List<DistTrainTicketTransFlowRecord>>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<PageWrapper<List<DistTrainTicketTransFlowRecord>>>>() {
        });
        final BaseResult<PageWrapper<List<DistTrainTicketTransFlowRecord>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String insertDistTransFlow(List<DistTrainTradeLine> distTradeLines) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<List<DistTrainTradeLine>> httpEntity = new HttpEntity<>(distTradeLines, httpHeaders);

        final String url = this.getCfesagHost() + "/external/sys/api/train/dist/insertDistTransFlow";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String getConfigs(String key) {
        String url = this.getCfesagHost() + "/external/sys/api/train/getconfigs?key="+key;
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        return response.getBody().getData();
    }

	@Override
	public String sm4DecodeBySource(String content,String source) {
		String url = this.getCfesagHost() + "/external/sys/api/train/sm4Decode?source="+source;
		final HttpHeaders httpHeaders = constructCommonHeaders();
		final HttpEntity<String> httpEntity = new HttpEntity<>(content, httpHeaders);
		ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
		});
		return response.getBody().getData();
	}


	@Override
    public List<TrainTicketTransFlowRecord> loadTrainTicketTransFlowRecords(final String corpId,
                                                                            final String orderId,
                                                                            final Long transTimeFrom,
                                                                            final Long transTimeTo) {

        final List<String> queryParams = new ArrayList<>();
        if (StringUtils.isNotBlank(orderId)) {
            queryParams.add(String.format("orderId=%s", orderId));
        }
        if (StringUtils.isNotBlank(corpId)) {
            queryParams.add(String.format("corpId=%s", corpId));
        }
        if (Objects.nonNull(transTimeFrom)) {
            queryParams.add(String.format("transTimeFrom=%s", transTimeFrom));
        }
        if (Objects.nonNull(transTimeTo)) {
            queryParams.add(String.format("transTimeTo=%s", transTimeTo));
        }

        final String url = String.format(this.getCfesagHost() + "/external/sys/api/train/trainTicketTransFlowRecords?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : StringUtils.join(queryParams, "&"));
        ResponseEntity<BaseResult<List<TrainTicketTransFlowRecord>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<List<TrainTicketTransFlowRecord>>>() {
        });
        final BaseResult<List<TrainTicketTransFlowRecord>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<DelayCareTransFlowRecord> loadDelayCareTransFlowRecords(final String corpId,
                                                                        final Long transTimeFrom,
                                                                        final Long transTimeTo) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/train/delayCareTransFlowRecords?corpId=%s&transTimeFrom=%s&transTimeTo=%s",
                DisplayUtil.display(corpId),
                Objects.isNull(transTimeFrom) ? "" : String.valueOf(transTimeFrom),
                Objects.isNull(transTimeTo) ? "" : String.valueOf(transTimeTo));

        ResponseEntity<BaseResult<List<DelayCareTransFlowRecord>>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<List<DelayCareTransFlowRecord>>>() {
        });
        final BaseResult<List<DelayCareTransFlowRecord>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CorpTrainTicketOrderDetail getCorpTrainTicketOrderDetail(final String orderId, final String payOrderId) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/train/corpTrainTicketOrders/%s?payOrderId=%s",
                DisplayUtil.display(orderId), StringUtil.isEmpty(payOrderId) ? "" : payOrderId);

        ResponseEntity<BaseResult<CorpTrainTicketOrderDetail>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<CorpTrainTicketOrderDetail>>() {
        });
        final BaseResult<CorpTrainTicketOrderDetail> result = checkResponse(response);
        return result.getData();
    }
    
    @Override
    public CorpTrainTicketMainSubOrderDetail getCorpTrainTicketMainSubOrderDetail(final String orderId) {
    	final String url = String.format(this.getCfesagHost() + "/external/sys/api/train/corpTrainTicketMainSubOrders/%s",DisplayUtil.display(orderId));
    	
    	ResponseEntity<BaseResult<CorpTrainTicketMainSubOrderDetail>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<CorpTrainTicketMainSubOrderDetail>>() {
    	});
    	final BaseResult<CorpTrainTicketMainSubOrderDetail> result = checkResponse(response);
    	return result.getData();
    }

    @Override
    public String getTrainTMCUrl(TrainTicketHandleUrlQueryParam param) {
        Map<String, String> params = param.forGetData();
        String url = String.format("%s/external/sys/api/train/queryTmcUrl?%s", this.getCfesagHost(), buildUrlParams(params));
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.GET, null, params, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<TrainTicketRealTimeOrderVO>> queryTrainRealTimeOrderInfo(TrainTicketRealTimeOrderQueryParam queryParam) {
        Map<String, Object> params = queryParam.forGetData();
        String url = String.format("%s/external/sys/api/train/queryRealTimeOrderInfo?%s", this.getCfesagHost(), buildUrlParams(params));
        ResponseEntity<BaseResult<PageWrapper<List<TrainTicketRealTimeOrderVO>>>> response = execute(url, HttpMethod.GET, null, params, new ParameterizedTypeReference<BaseResult<PageWrapper<List<TrainTicketRealTimeOrderVO>>>>() {
        });
        final BaseResult<PageWrapper<List<TrainTicketRealTimeOrderVO>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public TrainTicketRealTimeOrderDetailVO queryTrainRealTimeOrderDetailByOrderId(String orderId) {
        String url = String.format("%s/external/sys/api/train/queryRealTimeOrderDetail/%s", this.getCfesagHost(), orderId);
        ResponseEntity<BaseResult<TrainTicketRealTimeOrderDetailVO>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<TrainTicketRealTimeOrderDetailVO>>() {
        });
        final BaseResult<TrainTicketRealTimeOrderDetailVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<TrainTicketGrabOrderDetailVO> queryGrabOrderByParams(String id, String orderId, String phoneId, String phone, String erpId) {
        final List<String> queryParams = new ArrayList<>();
        if (StringUtils.isNotBlank(id)) {
            queryParams.add(String.format("id=%s", id));
        }
        if (StringUtils.isNotBlank(orderId)) {
            queryParams.add(String.format("orderId=%s", orderId));
        }
        if (StringUtils.isNotBlank(phoneId)) {
            queryParams.add(String.format("phoneId=%s", phoneId));
        }
        if (StringUtils.isNotBlank(phone)) {
            queryParams.add(String.format("phone=%s", phone));
        }
        if (StringUtils.isNotBlank(erpId)) {
            queryParams.add(String.format("erpId=%s", erpId));
        }
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/train/queryGrabOrder?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : StringUtils.join(queryParams, "&"));

        ResponseEntity<BaseResult<List<TrainTicketGrabOrderDetailVO>>> response =
                execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<List<TrainTicketGrabOrderDetailVO>>>() {
                });
        final BaseResult<List<TrainTicketGrabOrderDetailVO>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String setExpired(String id) {
        String url = String.format("%s/external/sys/api/train/setExpired?id=%s",
                this.getCfesagHost(), id);
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PlatformBuyOrderCreate createTrainTicketOrder(String orderFormData) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("orderFormData", orderFormData);

        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(params, headers);

        String url = String.format("%s/external/sys/api/train/createOrder", this.getCfesagHost());
        ResponseEntity<BaseResult<PlatformBuyOrderCreate>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PlatformBuyOrderCreate>>() {
        });
        final BaseResult<PlatformBuyOrderCreate> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<TrainGrabOrderStatisticsVO> queryTrainGrabOrderStatistics(String phoneId, String startDate, String endDate) {
        final List<String> queryParams = new ArrayList<>();
        if (StringUtils.isNotBlank(phoneId)) {
            queryParams.add(String.format("phoneId=%s", phoneId));
        }
        if (StringUtils.isNotBlank(startDate)) {
            queryParams.add(String.format("startDate=%s", startDate));
        }
        if (StringUtils.isNotBlank(endDate)) {
            queryParams.add(String.format("endDate=%s", endDate));
        }

        final String url = String.format(this.getCfesagHost() + "/external/sys/api/train/qp/dataStat?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : StringUtils.join(queryParams, "&"));

        ResponseEntity<BaseResult<List<TrainGrabOrderStatisticsVO>>> response =
                execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<List<TrainGrabOrderStatisticsVO>>>() {
                });
        final BaseResult<List<TrainGrabOrderStatisticsVO>> result = checkResponse(response);
        return result.getData();
    }


    @Override
    public SearchApiCountTrainResult queryTrainCountApi(SearchApiCountTrainParam param) {
            final HttpHeaders httpHeaders = constructCommonHeaders();
            final HttpEntity<SearchApiCountTrainParam> httpEntity = new HttpEntity<>(param, httpHeaders);
            final String url = this.getCfesagHost() + "/external/sys/api/train/dist/count";
            ResponseEntity<BaseResult<SearchApiCountTrainResult>> response = execute(url, HttpMethod.POST, httpEntity,
                    new ParameterizedTypeReference<BaseResult<SearchApiCountTrainResult>>() {
                    });
            final BaseResult<SearchApiCountTrainResult> result = checkResponse(response);
            return result.getData();
        }

    @Override
    public QueryOrderListResultVO queryOrderList(TrainTicketOrderListQueryParam param)
    {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<TrainTicketOrderListQueryParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/queryOrderList";
        ResponseEntity<BaseResult<QueryOrderListResultVO>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<QueryOrderListResultVO>>() {
                });
        final BaseResult<QueryOrderListResultVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public OrderDetailResultVO queryOrderDetail(TrainTicketQueryDetailParam param)
    {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<TrainTicketQueryDetailParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/queryOrderDetail";
        ResponseEntity<BaseResult<OrderDetailResultVO>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<OrderDetailResultVO>>() {
                });
        final BaseResult<OrderDetailResultVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public TrainZwdRateResultVO queryTrainZwdRate(String trainNo, String departStation, String arriveStation) {
        final List<String> queryParams = new ArrayList<>();
        if (StringUtils.isNotBlank(trainNo)) {
            queryParams.add(String.format("trainNo=%s", trainNo));
        }
        if (StringUtils.isNotBlank(departStation)) {
            queryParams.add(String.format("departStation=%s", departStation));
        }
        if (StringUtils.isNotBlank(arriveStation)) {
            queryParams.add(String.format("arriveStation=%s", arriveStation));
        }

        final String url = String.format(this.getCfesagHost() + "/external/sys/api/train/query/queryTrainZwdRate?%s",
                CollectionUtils.isEmpty(queryParams) ? "" : StringUtils.join(queryParams, "&"));
        ResponseEntity<BaseResult<TrainZwdRateResultVO>> response =
                execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<TrainZwdRateResultVO>>() {
                });
        final BaseResult<TrainZwdRateResultVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public QueryTrainScheduleDetailVO queryTrainScheduleShareDetail(QueryTrainScheduleDetailParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QueryTrainScheduleDetailParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = String.format("%s/external/sys/api/train/query/queryTrainScheduleShareDetail", this.getCfesagHost());
        ResponseEntity<BaseResult<QueryTrainScheduleDetailVO>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<QueryTrainScheduleDetailVO>>() {
        });
        final BaseResult<QueryTrainScheduleDetailVO> result = checkResponse(response);
        return result.getData();
    }

    /*
     * 查询火车票业务方企业订单消费数据
     */
    @Override
    public CorpOrderRecordsWrapper<TrainCorpOrderRecord> queryTrainCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpOrderRecordsQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/train/corpOrderRecords/query", this.getCfesagHost());
        ResponseEntity<BaseResult<CorpOrderRecordsWrapper<TrainCorpOrderRecord>>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<CorpOrderRecordsWrapper<TrainCorpOrderRecord>>>() {
                });
        final BaseResult<CorpOrderRecordsWrapper<TrainCorpOrderRecord>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String cancelOrder(TrainCancelOrderParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<TrainCancelOrderParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = String.format("%s/external/sys/api/train/cancelOrder", this.getCfesagHost());
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
    
    @Override
    public GetPayUrlResultVO getPayUrlSpV2(GetPayUrlParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<GetPayUrlParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/getPayUrlSpV2";
        ResponseEntity<BaseResult<GetPayUrlResultVO>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<GetPayUrlResultVO>>() {});
        final BaseResult<GetPayUrlResultVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public EndorseRequestResultVO endorseRequest(EndorseRequestParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<EndorseRequestParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/endorseRequest";
        ResponseEntity<BaseResult<EndorseRequestResultVO>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<EndorseRequestResultVO>>() {});
        final BaseResult<EndorseRequestResultVO> result = checkResponse(response);
        return result.getData();
    }
    
    @Override
    public HelpPayInstPurchaseCart instPurchased(InstPurchasedParam instPurchasedParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<InstPurchasedParam> httpEntity = new HttpEntity<>(instPurchasedParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/instPurchased";
        ResponseEntity<BaseResult<HelpPayInstPurchaseCart>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<HelpPayInstPurchaseCart>>() {});
        final BaseResult<HelpPayInstPurchaseCart> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String endorseConfirm(EndorseConfirmParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<EndorseConfirmParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/endorseConfirm";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<String>>() {});
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
    
    @Override
    public GetEndorsePayUrlResultVO getEndorsePayUrl(GetEndorsePayUrlParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<GetEndorsePayUrlParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/getEndorsePayUrl";
        ResponseEntity<BaseResult<GetEndorsePayUrlResultVO>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<GetEndorsePayUrlResultVO>>() {});
        final BaseResult<GetEndorsePayUrlResultVO> result = checkResponse(response);
        return result.getData();
    }
    
    @Override
    public GetGtUserAccountResult getGtUserAccount(GetGtUserAccountParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<GetGtUserAccountParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/user/query";
        ResponseEntity<BaseResult<GetGtUserAccountResult>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<GetGtUserAccountResult>>() {});
        final BaseResult<GetGtUserAccountResult> result = checkResponse(response);
        return result.getData();
    }
    
    @Override
    public TrainOrderConsumeInfoResultVO queryOrderConsumeInfo(TrainOrderConsumeInfoParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<TrainOrderConsumeInfoParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/queryOrderConsumeInfo";
        ResponseEntity<BaseResult<TrainOrderConsumeInfoResultVO>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<TrainOrderConsumeInfoResultVO>>() {});
        final BaseResult<TrainOrderConsumeInfoResultVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public TrainCorpResaleOrderVO queryTrainCorpResaleOrderInfo(TrainCorpResaleOrderQueryParam trainCorpResaleOrderQueryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<TrainCorpResaleOrderQueryParam> httpEntity = new HttpEntity<>(trainCorpResaleOrderQueryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/train/queryTrainCorpResaleOrderInfo", this.getCfesagHost());
        ResponseEntity<BaseResult<TrainCorpResaleOrderVO>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<TrainCorpResaleOrderVO>>() {
                });
        final BaseResult<TrainCorpResaleOrderVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CorpResaleOrderRecsWrapper<TrainResaleOrderConsumption> queryTrainResaleCorpOrderRecs(CorpResaleOrderRecsQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpResaleOrderRecsQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/train/corpResaleOrderRecs/query", this.getCfesagHost());
        ResponseEntity<BaseResult<CorpResaleOrderRecsWrapper<TrainResaleOrderConsumption>>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<CorpResaleOrderRecsWrapper<TrainResaleOrderConsumption>>>() {
                });
        final BaseResult<CorpResaleOrderRecsWrapper<TrainResaleOrderConsumption>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String queryTrainResaleOrderSubOrderId(String passengerName, String orderID) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/train/corpResaleOrderRecs/query?passengerName=%s&orderID=%s",passengerName,orderID);

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public TrainOrderSensitiveInfoVO queryOrderSensitiveInfo(TrainOrderSensitiveQueryParam trainOrderSensitiveQueryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<TrainOrderSensitiveQueryParam> httpEntity = new HttpEntity<>(trainOrderSensitiveQueryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/train/queryOrderSensitiveInfo", this.getCfesagHost());

        ResponseEntity<BaseResult<TrainOrderSensitiveInfoVO>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<TrainOrderSensitiveInfoVO>>() {
        });
        final BaseResult<TrainOrderSensitiveInfoVO> result = checkResponse(response);
        return result.getData();
    }
}
