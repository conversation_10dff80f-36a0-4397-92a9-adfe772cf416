package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.dynamic.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.dynamic.DomesticFlightTicketDynamicDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.dynamic.DynamicBaseParam;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.dynamic.DynamicDetailResult;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

public class DefaultDomesticFlightTicketDynamicDeligator extends BaseDeligator implements DomesticFlightTicketDynamicDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultDomesticFlightTicketDynamicDeligator.class);

    public DefaultDomesticFlightTicketDynamicDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public DynamicDetailResult queryFlightWeather(DynamicBaseParam param){
        HttpHeaders httpHeaders = this.constructCommonHeaders();
        HttpEntity<DynamicDetailResult> httpEntity = new HttpEntity(param, httpHeaders);
        String url = String.format("%s/external/sys/api/dynamic/queryFlightWeather", this.getCfesagHost());
        ResponseEntity<BaseResult<DynamicDetailResult>> response = this.execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DynamicDetailResult>>() {
        });
        BaseResult<DynamicDetailResult> result = this.checkResponse(response);
        return result.getData();
    }

    @Override
    public List<DynamicDetailResult> queryFlightIntegrationBatch(List<DynamicBaseParam> params){
        HttpHeaders httpHeaders = this.constructCommonHeaders();
        HttpEntity<List<DynamicDetailResult>> httpEntity = new HttpEntity(params, httpHeaders);
        String url = String.format("%s/external/sys/api/dynamic/integration/batch", this.getCfesagHost());
        ResponseEntity<BaseResult<List<DynamicDetailResult>>> response = this.execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<DynamicDetailResult>>>() {
        });
        BaseResult<List<DynamicDetailResult>> result = this.checkResponse(response);
        return result.getData();
    }
}
