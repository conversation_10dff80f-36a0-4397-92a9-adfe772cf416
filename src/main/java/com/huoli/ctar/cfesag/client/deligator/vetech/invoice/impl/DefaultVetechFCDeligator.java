package com.huoli.ctar.cfesag.client.deligator.vetech.invoice.impl;


import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.vetech.invoice.VetechFCDeligator;
import com.huoli.ctar.cfesag.external.sys.api.vetech.invoice.VetechInvInfoUploadReq;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultVetechFCDeligator
        extends BaseDeligator
        implements VetechFCDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultVetechFCDeligator.class);

    public DefaultVetechFCDeligator(final RestTemplate restTemplate, final String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public void uploadHotelOrderInvInfo(final VetechInvInfoUploadReq uploadReq) {
        final HttpHeaders headers = constructCommonHeaders();
        final HttpEntity<VetechInvInfoUploadReq> httpEntity = new HttpEntity<>(uploadReq, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/vetech/fc/hotel/order/invoice/upload");
        final ResponseEntity<BaseResult<String>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {}, false);
        checkResponse(result);
    }

    @Override
    public void uploadDftOrderInvInfo(final VetechInvInfoUploadReq uploadReq) {
        final HttpHeaders headers = constructCommonHeaders();
        final HttpEntity<VetechInvInfoUploadReq> httpEntity = new HttpEntity<>(uploadReq, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/vetech/fc/dft/order/invoice/upload");
        final ResponseEntity<BaseResult<String>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {}, false);
        checkResponse(result);
    }
}
