package com.huoli.ctar.cfesag.client.deligator.payment.account.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.account.PaymentAccountSysDeligator;
import com.huoli.ctar.cfesag.external.sys.api.payment.account.model.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

public class DefaultPaymentAccountSysDeligator extends BaseDeligator implements PaymentAccountSysDeligator {
    private static final Logger log = LoggerFactory.getLogger(DefaultPaymentAccountSysDeligator.class);

    public DefaultPaymentAccountSysDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String accountRechargeBalance(final AccountRechargeRequest requestParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<AccountRechargeRequest> httpEntity = new HttpEntity<>(requestParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/account/rechargeBalance";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });

        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CouponListResult queryUserCouponList(final CouponListParam param){
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CouponListParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/btaccount/coupon/queryUserCouponList";

        ResponseEntity<BaseResult<CouponListResult>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CouponListResult>>() {
        });

        final BaseResult<CouponListResult> result = checkResponseNotCheckCode(response);
        return result.getData();
    }

    @Override
    public List<MemberDetailResult> queryMemberDetail(final MemberDetailRequest memberDetailRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<MemberDetailRequest> httpEntity = new HttpEntity<>(memberDetailRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/account/queryMemberDetail";

        ResponseEntity<BaseResult<List<MemberDetailResult>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<MemberDetailResult>>>() {
        });

        final BaseResult<List<MemberDetailResult>> result = checkResponseNotCheckCode(response);
        return result.getData();
    }

    @Override
    public List<ConsumeDetailResult> queryConsumeDetail(final ConsumeDetailRequest consumeDetailRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ConsumeDetailRequest> httpEntity = new HttpEntity<>(consumeDetailRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/account/queryConsumeDetail";

        ResponseEntity<BaseResult<List<ConsumeDetailResult>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<ConsumeDetailResult>>>() {
        });

        final BaseResult<List<ConsumeDetailResult>> result = checkResponseNotCheckCode(response);
        return result.getData();
    }

    @Override
    public String userReChargeBalance(RechargeBalanceRequest rechargeBalanceRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<RechargeBalanceRequest> httpEntity = new HttpEntity<>(rechargeBalanceRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/account/userRechargeBalance";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });

        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
}
