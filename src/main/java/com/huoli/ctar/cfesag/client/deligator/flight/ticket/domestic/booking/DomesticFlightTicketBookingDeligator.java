package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking;

import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.booking.model.DomesticFlightTicketRefundRecord;

import java.util.List;

public interface DomesticFlightTicketBookingDeligator {
    List<DomesticFlightTicketRefundRecord> loadDomesticFlightTicketRefundRecords(final Long transTimeFrom, final Long transTimeTo);
}
