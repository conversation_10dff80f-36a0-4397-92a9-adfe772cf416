package com.huoli.ctar.cfesag.client.deligator.tourism.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.tourism.TourismResaleDeligator;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.TourismResaleOrderConsumption;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;


public class DefaultTourismResaleDeligator extends BaseDeligator implements TourismResaleDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultTourismResaleDeligator.class);

    public DefaultTourismResaleDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public CorpResaleOrderRecsWrapper<TourismResaleOrderConsumption> queryTourismCorpResaleOrderRecords(CorpResaleOrderRecsQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpResaleOrderRecsQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/tourism/corpResaleOrderRecs/query", this.getCfesagHost());
        ResponseEntity<BaseResult<CorpResaleOrderRecsWrapper<TourismResaleOrderConsumption>>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<CorpResaleOrderRecsWrapper<TourismResaleOrderConsumption>>>() {});
        final BaseResult<CorpResaleOrderRecsWrapper<TourismResaleOrderConsumption>> result = checkResponse(response);
        return result.getData();
    }
}
