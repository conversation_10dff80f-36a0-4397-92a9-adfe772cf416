package com.huoli.ctar.cfesag.client.deligator.invoice;

import com.huoli.ctar.cfesag.external.sys.api.invoice.model.*;
import com.huoli.ctar.cfesag.external.sys.api.post.service.model.ItineraryCancelParam;
import com.huoli.ctar.cfesag.external.sys.api.post.service.model.ItineraryCancelResult;
import com.huoli.ctar.cfesag.external.sys.api.post.service.model.PostConfirmParam;
import com.huoli.ctar.cfesag.external.sys.api.post.service.model.TicketConfirmResult;

import java.util.List;

public interface InvoiceDeligator {
    InvoiceBatchDto invoiceSaves(InvoiceBatchRequest invoiceBatchRequest);

    InvoiceBatchDto.InvoiceDto queryInvoiceDetail(Long phoneId, Long invoiceId, Integer source);

    String cancelInvoice(Long invoiceId);

    List<EleInvoDto> queryInvoice(QueryInvoiceRequest queryInvoiceRequest);

    TicketConfirmResult itinerarySaves(PostConfirmParam confirmParam);

    ItineraryCancelResult cancelItinerary(ItineraryCancelParam cancelParam);

    String getItineraryFileUrl(String name, String detailId, String fileType);

    InvoiceInsConfirmResult insuranceSaves(InvoiceInsConfirmParam param);

    InvoiceInsQueryResult insuranceQuery(String orderId);
}
