package com.huoli.ctar.cfesag.client.deligator.resale.booking;

import com.huoli.ctar.cfesag.external.sys.api.resale.booking.model.*;
import com.huoli.ctar.cfesag.external.sys.api.resale.booking.model.back.*;
import com.huoli.ctar.cfesag.external.sys.api.resale.booking.model.order.*;
import com.huoli.ctar.cfesag.external.sys.api.resale.booking.model.shelf.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

public interface ResaleBookingDeligator {

    ResaleUser register(final ResaleUserRegisterRequest resaleUserRegisterRequest);

    String updateResaleUser(final ResaleUserUpdateRequest resaleUserUpdateRequest, final String openId);

    PageWrapper<List<DomesticFlightTicketOrder>> loadOrders(final String openId,
                                                            final String orderId,
                                                            final String orderStatus,
                                                            final String passengerName,
                                                            final String idCard,
                                                            final String ticketNo,
                                                            final Long orderCreateTimeFrom,
                                                            final Long orderCreateTimeTo,
                                                            final Integer pageNo,
                                                            final Integer pageSize);

    PageWrapper<List<OutputProductsFilter>> loadDistributeFilters(final Integer id,
                                                                  final String clientId,
                                                                  final String dataSrc,
                                                                  final Integer whiteFlag,
                                                                  final Integer pageNo,
                                                                  final Integer pageSize);

    OutputProductsFilter createDistributeFilter(final OutputProductsFilterParam outputProductsFilterParam);

    OutputProductsFilter updateDistributeFilter(final OutputProductsFilterParam outputProductsFilterParam);

    String deleteDistributeFilter(final Integer id);

    PageWrapper<List<PartnerOutputConfig>> loadDistributePartners(final Integer id,
                                                                  final String clientId,
                                                                  final Integer allowNonStand,
                                                                  final Integer pageNo,
                                                                  final Integer pageSize);

    PartnerOutputConfig createDistributePartner(final PartnerOutPutParam partnerOutPutParam);

    PartnerOutputConfig updateDistributePartner(final PartnerOutPutParam partnerOutPutParam);

    String deleteDistributePartner(final Integer id);

    PageWrapper<List<ProductPolicy>> loadDistributePolicies(final Integer id,
                                                                        final String clientId,
                                                                        final String airLine,
                                                                        final String isEffect,
                                                                        final Integer pageNo,
                                                                        final Integer pageSize);

    ProductPolicy createDistributePolicy(final ProductPolicyParam productPolicyParam);

    ProductPolicy updateDistributePolicy(final ProductPolicyParam productPolicyParam);

    String deleteDistributePolicy(final Integer id);

    PageWrapper<List<ProductPolicyRule>> loadDistributePolicyRules(final Integer pid,
                                                                   final Integer pageNo,
                                                                   final Integer pageSize);

    ProductPolicyRule createDistributePolicyRule(final ProductPolicyRuleParam productPolicyRuleParam);

    String updateDistributePolicyRule(final ProductPolicyRuleParam productPolicyRuleParam);

    String deleteDistributePolicyRule(final Integer id);

    PageWrapper<List<ResaleGroupParam>> loadResaleGroups(final Integer id);

    String createResaleGroup(final ResaleGroupParam resaleGroupParam);

    String updateResaleGroup(ResaleGroupParam resaleGroupParam);

    String deleteResaleGroup(final Integer id);

    Map<String, List<FlightResaleDoc>> queryDocInfo();

    DomesticFlightTicketOrderDetail getFlightTicketOrderDetail(final String orderId);

    PageWrapper<List<FlightTicketOrderLog>> loadFlightTicketOrderLogs(final String orderId,
                                                                      final Integer pageNo,
                                                                      final Integer pageSize);

    PageWrapper<List<ProductTypeConfig>> loadProductTypes(final Integer pageNo,
                                                          final Integer pageSize);

    PageWrapper<List<OrderTransDetail>> queryTransRecords(final DomesticFlightTransRecordQueryParams transRecordQueryParams);

    List<OrderTransDetail> queryOrderStatusByOrderIds(final OrderStatusQueryParam orderStatusQueryParam);

    List<OrderTransDetail> querySubOrderStatusByOrderIds(final OrderStatusQueryParam orderStatusQueryParam);

    List<RerouteFlightResult> queryRerouteFlight(final RerouteFlightParam rerouteFlightParam);

    ReroutePriceConfirmResult reroutePriceConfirm(final ReroutePriceConfirmParam reroutePriceConfirmParam);

    RerouteSubmitResult rerouteSubmit(final RerouteSubmitParam rerouteSubmitParam);

    ConfirmRefundResult refundFlight(final RefundFlightParam refundFlightParam);

    RefundQueryResult refundQuery(RefundQueryParam refundQueryParam);

    List<RefundFlightParam.FileInfo> upload( List<RefundFlightParam.FileInfo>  files);

    String insertResaleLog(FlightResaleOrderLog flightResaleOrderLog);

    String insertResaleLogList(List<FlightResaleOrderLog> resaleOrderLogs);

    PageWrapper<List<FlightResaleOrderLog>> loadAppealByPage(OrderLogParam orderLogParam);

    FlightSearchResult searchFlight(FlightSearchParam flightSearchParam);

    FlightResult search(FlightSearchParam flightSearchParam);

    DomesticFlightTicketOrderDetail getDomesticFlightTicketDetail(String orderId);

    PageWrapper<List<ApiLogQueryResult>> apiLogQuery(ApiLogQueryParam apiLogQueryParam);

    String apiLogExport(ApiLogQueryParam apiLogQueryParam);

    List<MonitorInfoResult> monitorInfoQuery(MonitorInfoQueryParam monitorInfoQueryParam);

    String monitorInfoExport(MonitorInfoQueryParam monitorInfoQueryParam);

    String cancelPnr(CancelPnrParams cancelPnrParams);

    List<SearchApiCountDomesticFlightResult> queryApiCount(SearchApiCountDomesticFlightParam param);

}
