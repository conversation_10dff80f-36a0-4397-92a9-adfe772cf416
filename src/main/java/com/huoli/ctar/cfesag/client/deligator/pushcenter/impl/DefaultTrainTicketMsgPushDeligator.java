package com.huoli.ctar.cfesag.client.deligator.pushcenter.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.pushcenter.TrainTicketMsgPushDeligator;
import com.huoli.ctar.cfesag.external.sys.api.pushcenter.model.PushMsgRequest;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

@Slf4j
public class DefaultTrainTicketMsgPushDeligator extends BaseDeligator implements TrainTicketMsgPushDeligator {

    public DefaultTrainTicketMsgPushDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String pushMsg(final PushMsgRequest pushMsgRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PushMsgRequest> httpEntity = new HttpEntity<>(pushMsgRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/msg/push";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
}
