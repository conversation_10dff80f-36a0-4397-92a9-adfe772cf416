package com.huoli.ctar.cfesag.client.deligator.payment.gateway.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.gateway.PaymentGatewayDeligator;
import com.huoli.ctar.cfesag.external.sys.api.payment.gateway.model.*;
import com.huoli.ctar.common.utils.DisplayUtil;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.model.PageWrapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

public class DefaultPaymentGatewayDeligator extends BaseDeligator implements PaymentGatewayDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultPaymentGatewayDeligator.class);

    public DefaultPaymentGatewayDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String verifySSOUser(VerifySSOUserParam verifySSOUserParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<VerifySSOUserParam> httpEntity = new HttpEntity<>(verifySSOUserParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/ssoUsers/verify";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });

        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DistPaymentAccount getDistPaymentAccount(QueryDistPaymentAccountRequest queryDistPaymentAccountRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QueryDistPaymentAccountRequest> httpEntity = new HttpEntity<>(queryDistPaymentAccountRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/distPaymentAccounts/retrieve";

        ResponseEntity<BaseResult<DistPaymentAccount>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DistPaymentAccount>>() {
        });
        final BaseResult<DistPaymentAccount> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<DistPaymentAccountInfo>> loadDistPaymentAccountInfos(QueryDistPaymentAccountInfoRequest queryDistPaymentAccountInfoRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QueryDistPaymentAccountInfoRequest> httpEntity = new HttpEntity<>(queryDistPaymentAccountInfoRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/distPaymentAccountInfos/query";
        ResponseEntity<BaseResult<PageWrapper<List<DistPaymentAccountInfo>>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<DistPaymentAccountInfo>>>>() {
        });
        final BaseResult<PageWrapper<List<DistPaymentAccountInfo>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String updateDistPaymentAccount(UpdateDistPaymentAccountRequest updateDistPaymentAccountRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<UpdateDistPaymentAccountRequest> httpEntity = new HttpEntity<>(updateDistPaymentAccountRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/distPaymentAccounts/update";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.PUT, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String createDistPaymentAccount(CreateDistPaymentAccountRequest createDistPaymentAccountRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CreateDistPaymentAccountRequest> httpEntity = new HttpEntity<>(createDistPaymentAccountRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/distPaymentAccounts";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String rechargeDistPaymentAccount(DistPaymentAccountRechargeRequest distPaymentAccountRechargeRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DistPaymentAccountRechargeRequest> httpEntity = new HttpEntity<>(distPaymentAccountRechargeRequest, httpHeaders);

        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/distPaymentAccounts/recharge";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CorpPaymentAccount getCorpPaymentAccount(final Long corpId) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/payment/gateway/corpPaymentAccounts/%d", corpId);
        ResponseEntity<BaseResult<CorpPaymentAccount>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<CorpPaymentAccount>>() {
        });
        final BaseResult<CorpPaymentAccount> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String createCorpPaymentAccount(final CreateCorpPaymentAccountRequest createCorpPaymentAccountRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CreateCorpPaymentAccountRequest> httpEntity = new HttpEntity<>(createCorpPaymentAccountRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/corpPaymentAccounts";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String updateCorpPaymentAccount(final UpdateCorpPaymentAccountRequest updateCorpPaymentAccountRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<UpdateCorpPaymentAccountRequest> httpEntity = new HttpEntity<>(updateCorpPaymentAccountRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/corpPaymentAccounts/update";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.PUT, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String createCorpMemberPaymentInfo(final CreateCorpMemberPaymentInfoRequest createCorpMemberPaymentInfoRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CreateCorpMemberPaymentInfoRequest> httpEntity = new HttpEntity<>(createCorpMemberPaymentInfoRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/corpMemberPaymentInfos";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String updateCorpMemberPaymentInfo(final UpdateCorpMemberPaymentInfoRequest updateCorpMemberPaymentInfoRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<UpdateCorpMemberPaymentInfoRequest> httpEntity = new HttpEntity<>(updateCorpMemberPaymentInfoRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/corpMemberPaymentInfos/update";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.PUT, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String mergeCorpMemberPaymentInfo(final MergeCorpMemberPaymentInfoRequest mergeCorpMemberPaymentInfoRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<MergeCorpMemberPaymentInfoRequest> httpEntity = new HttpEntity<>(mergeCorpMemberPaymentInfoRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/corpMemberPaymentInfos/merge";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.PUT, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String rechargeCorpPaymentAccount(final RechargeCorpPaymentAccountRequest rechargeCorpPaymentAccountRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<RechargeCorpPaymentAccountRequest> httpEntity = new HttpEntity<>(rechargeCorpPaymentAccountRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/corpPaymentAccounts/recharge";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<CorpPaymentAccount>> loadCorpPaymentAccounts(final List<String> corpIds, final Integer pageNo, final Integer pageSize) {
        final QueryCorpPaymentAccountRequest queryCorpPaymentAccountRequest = new QueryCorpPaymentAccountRequest();
        queryCorpPaymentAccountRequest.setCorpIds(StringUtils.join(corpIds, ","));
        queryCorpPaymentAccountRequest.setPageNo(pageNo);
        queryCorpPaymentAccountRequest.setPageSize(pageSize);

        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QueryCorpPaymentAccountRequest> httpEntity = new HttpEntity<>(queryCorpPaymentAccountRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/corpPaymentAccounts/query";


        ResponseEntity<BaseResult<PageWrapper<List<CorpPaymentAccount>>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<CorpPaymentAccount>>>>() {
        });
        final BaseResult<PageWrapper<List<CorpPaymentAccount>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CorpConsumptionInfo getCorpConsumptionInfo(final Long corpId, final String productIds) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/payment/gateway/corpConsumptionInfo/retrieve?corpId=%d&productIds=%s", corpId, DisplayUtil.display(productIds));
        ResponseEntity<BaseResult<CorpConsumptionInfo>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<CorpConsumptionInfo>>() {
        });

        final BaseResult<CorpConsumptionInfo> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<CorpMemberPaymentInfo>> loadCorpMemberPaymentInfos(final Long corpId, final List<String> phoneIds, final Integer pageNo, final Integer pageSize) {
        final QueryCorpMemberPaymentInfoRequest queryCorpMemberPaymentInfoRequest = new QueryCorpMemberPaymentInfoRequest();
        queryCorpMemberPaymentInfoRequest.setCorpId(corpId);
        queryCorpMemberPaymentInfoRequest.setPhoneIds(phoneIds);
        queryCorpMemberPaymentInfoRequest.setPageNum(pageNo);
        queryCorpMemberPaymentInfoRequest.setPageSize(pageSize);

        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QueryCorpMemberPaymentInfoRequest> httpEntity = new HttpEntity<>(queryCorpMemberPaymentInfoRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/corpMemberPaymentInfos/query";

        ResponseEntity<BaseResult<PageWrapper<List<CorpMemberPaymentInfo>>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<CorpMemberPaymentInfo>>>>() {
        });
        final BaseResult<PageWrapper<List<CorpMemberPaymentInfo>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CorpMemberPaymentInfo getCorpMemberPaymentInfo(final Long corpId, final Integer phoneId) {
        final String url = String.format(this.getCfesagHost() + "/external/sys/api/payment/gateway/corpMemberPaymentInfos/retrieve?corpId=%d&phoneId=%d", corpId, phoneId);

        ResponseEntity<BaseResult<CorpMemberPaymentInfo>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<CorpMemberPaymentInfo>>() {
        });
        final BaseResult<CorpMemberPaymentInfo> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<CorpTransRecord>> loadCorpTransRecords(final Long corpId, final Integer phoneId, final Long transTimeFrom, final Long transTimeTo, final Integer pageNo, final Integer pageSize) {
        final QueryCorpTransFlowRequest queryCorpTransFlowRequest = new QueryCorpTransFlowRequest();
        queryCorpTransFlowRequest.setCorpId(corpId);
        queryCorpTransFlowRequest.setPhoneId(phoneId);
        queryCorpTransFlowRequest.setIsNeedCoupon(0);
        queryCorpTransFlowRequest.setTimeStart(transTimeFrom);
        queryCorpTransFlowRequest.setTimeEnd(transTimeTo);
        queryCorpTransFlowRequest.setPageNum(pageNo);
        queryCorpTransFlowRequest.setPageSize(pageSize);

        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QueryCorpTransFlowRequest> httpEntity = new HttpEntity<>(queryCorpTransFlowRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/corpTransRecords/query";

        ResponseEntity<BaseResult<PageWrapper<List<CorpTransRecord>>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<CorpTransRecord>>>>() {
        });
        final BaseResult<PageWrapper<List<CorpTransRecord>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<CorpTransRecord>> loadCorpTransRecordsNew(final Long corpId,
                                                                      final Integer phoneId,
                                                                      final Long transTimeFrom,
                                                                      final Long transTimeTo,
                                                                      final Integer pageNo,
                                                                      final Integer pageSize) {
        final QueryCorpTransFlowRequest queryCorpTransFlowRequest = new QueryCorpTransFlowRequest();
        queryCorpTransFlowRequest.setCorpId(corpId);
        queryCorpTransFlowRequest.setPhoneId(phoneId);
        queryCorpTransFlowRequest.setIsNeedCoupon(0);
        queryCorpTransFlowRequest.setTimeStart(transTimeFrom);
        queryCorpTransFlowRequest.setTimeEnd(transTimeTo);
        queryCorpTransFlowRequest.setPageNum(pageNo);
        queryCorpTransFlowRequest.setPageSize(pageSize);

        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QueryCorpTransFlowRequest> httpEntity = new HttpEntity<>(queryCorpTransFlowRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/account/corpTransRecords/query";

        ResponseEntity<BaseResult<PageWrapper<List<CorpTransRecord>>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<CorpTransRecord>>>>() {
        });
        final BaseResult<PageWrapper<List<CorpTransRecord>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<CorpRechargeRecord>> loadCorpRechargeRecords(final Long corpId, final Long transTimeFrom, final Long transTimeTo, final Integer pageNo, final Integer pageSize) {
        final QueryCorpTransFlowRequest queryCorpTransFlowRequest = new QueryCorpTransFlowRequest();
        queryCorpTransFlowRequest.setCorpId(corpId);
        queryCorpTransFlowRequest.setTimeStart(transTimeFrom);
        queryCorpTransFlowRequest.setTimeEnd(transTimeTo);
        queryCorpTransFlowRequest.setPageNum(pageNo);
        queryCorpTransFlowRequest.setPageSize(pageSize);

        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QueryCorpTransFlowRequest> httpEntity = new HttpEntity<>(queryCorpTransFlowRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/corpRechargeRecords/query";

        ResponseEntity<BaseResult<PageWrapper<List<CorpRechargeRecord>>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<CorpRechargeRecord>>>>() {
        });
        final BaseResult<PageWrapper<List<CorpRechargeRecord>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String emergencyQueryRegister(EmergencyQueryRegisterParams emergencyQueryRegisterParams) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<EmergencyQueryRegisterParams> httpEntity = new HttpEntity<>(emergencyQueryRegisterParams, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/corpRechargeRecords/emergencyQueryRegister";

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PageWrapper<List<CaissaCorpTransRecord>> loadCaissaTransRecords(Long corpId, final String orderId, final String payOrderId, final String tradeStart, final String tradeEnd, String timeStart, String timeEnd, Integer pageNo, Integer pageSize) {
        final QueryCorpTransRecordsRequest queryCorpTransRecordsRequest = new QueryCorpTransRecordsRequest();
        queryCorpTransRecordsRequest.setCorpId(corpId);
        queryCorpTransRecordsRequest.setBizOrderId(orderId);
        queryCorpTransRecordsRequest.setPayOrderId(payOrderId);
        queryCorpTransRecordsRequest.setTradeStart(tradeStart);
        queryCorpTransRecordsRequest.setTradeEnd(tradeEnd);
        queryCorpTransRecordsRequest.setTimeStart(timeStart);
        queryCorpTransRecordsRequest.setTimeEnd(timeEnd);
        queryCorpTransRecordsRequest.setPageNum(pageNo);
        queryCorpTransRecordsRequest.setPageSize(pageSize);

        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QueryCorpTransRecordsRequest> httpEntity = new HttpEntity<>(queryCorpTransRecordsRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/caissaTransRecords/query";

        ResponseEntity<BaseResult<PageWrapper<List<CaissaCorpTransRecord>>>> response =
                execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PageWrapper<List<CaissaCorpTransRecord>>>>() {
        });
        final BaseResult<PageWrapper<List<CaissaCorpTransRecord>>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<MixPayStatusResult> getMixPayStatus(String payOrderId)
    {
        final QueryMixPayStatusRequest queryMixPayStatusRequest = new QueryMixPayStatusRequest();
        queryMixPayStatusRequest.setPayOrderId(payOrderId);
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QueryMixPayStatusRequest> httpEntity = new HttpEntity<>(queryMixPayStatusRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/payment/gateway/mixPayStatus/query";

        ResponseEntity<BaseResult<List<MixPayStatusResult>>> response =
                execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<MixPayStatusResult>>>() {
                });
        final BaseResult<List<MixPayStatusResult>> result = checkResponse(response);
        return result.getData();
    }
}
