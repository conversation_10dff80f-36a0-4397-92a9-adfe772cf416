package com.huoli.ctar.cfesag.client.config;

import com.huoli.ctar.cfesag.client.CfesagClient;
import com.huoli.ctar.cfesag.client.DefaultCfesagClient;
import com.huoli.ctar.cfesag.client.deligator.agitech.TripNowApiDeligator;
import com.huoli.ctar.cfesag.client.deligator.basedata.cache.BaseDataCacheDeligator;
import com.huoli.ctar.cfesag.client.deligator.caihailing.CarHailingDeligator;
import com.huoli.ctar.cfesag.client.deligator.car.CarDeligator;
import com.huoli.ctar.cfesag.client.deligator.cosps.ReimbursementReceiptDeligator;
import com.huoli.ctar.cfesag.client.deligator.coupon.CouponDeligator;
import com.huoli.ctar.cfesag.client.deligator.distributor.TrainDistributorDeligator;
import com.huoli.ctar.cfesag.client.deligator.easypnp.EasypnpOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.feishu.FeiShuDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.coupon.FlightCouponDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.dynamics.svc.FlightDynamicsSvcDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.book.DomesticFlightTicketBookDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.DomesticFlightTicketBookingDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.support.DomesticFlightTicketBookingSupportDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.channel.DomesticFlightTicketChannelDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.dynamic.DomesticFlightTicketDynamicDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz.BizTicketOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz.DomesticFlightTicketBizDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.grabbing.FlightTicketGrabbingDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.DomesticFlightTicketOrderCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.DomesticFlightTicketOrderSensitiveDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.ordercenter.InternationalFlightTicketOrderCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.search.DomesticTicketSearchDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.viphall.DomesticFlightTicketVipHallDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.InternationalFlightTicketDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.InternationalFlightTicketSearchDeligator;
import com.huoli.ctar.cfesag.client.deligator.fulu.FuluDeligator;
import com.huoli.ctar.cfesag.client.deligator.hljx.HljxDeligator;
import com.huoli.ctar.cfesag.client.deligator.hotel.HotelDeligator;
import com.huoli.ctar.cfesag.client.deligator.insurance.InsuranceDeligator;
import com.huoli.ctar.cfesag.client.deligator.invoice.InvoiceDeligator;
import com.huoli.ctar.cfesag.client.deligator.mall.MallDeligator;
import com.huoli.ctar.cfesag.client.deligator.maycur.MaycurDeligator;
import com.huoli.ctar.cfesag.client.deligator.notify.CorpInvoiceNotifyDeligator;
import com.huoli.ctar.cfesag.client.deligator.nyjk.NyjkPostSaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.nyyh.NyyhPostSaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.oyh.YuHongFlightOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.oyh.YuHongHotelOrderDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.account.PaymentAccountSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.gateway.PaymentGatewayDeligator;
import com.huoli.ctar.cfesag.client.deligator.payment.sys.CorpPayDelegator;
import com.huoli.ctar.cfesag.client.deligator.payment.sys.PaymentSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.pnr.PnrDeligator;
import com.huoli.ctar.cfesag.client.deligator.post.service.PostServiceDeligator;
import com.huoli.ctar.cfesag.client.deligator.pushcenter.PushCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.booking.ResaleBookingDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.gateway.ResaleGatewayDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.info.ResaleInfoDeligator;
import com.huoli.ctar.cfesag.client.deligator.settle.money.mgmt.SettleMoneyMgmtSysDeligator;
import com.huoli.ctar.cfesag.client.deligator.sso.SSODeligator;
import com.huoli.ctar.cfesag.client.deligator.tools.apitools.ApiToolsDeligator;
import com.huoli.ctar.cfesag.client.deligator.tools.busapi.BusApiDeligator;
import com.huoli.ctar.cfesag.client.deligator.tourism.TourismDeligator;
import com.huoli.ctar.cfesag.client.deligator.tourism.TourismResaleDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainQPDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainQueryDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainTicketDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainUnifiedDeligator;
import com.huoli.ctar.cfesag.client.deligator.usercenter.UserCenterDeligator;
import com.huoli.ctar.cfesag.client.deligator.vetech.VetechDeligator;
import com.huoli.ctar.cfesag.client.deligator.vetech.invoice.VetechFCDeligator;
import com.huoli.ctar.cfesag.client.deligator.wechat.WechatDeligator;
import com.huoli.ctar.cfesag.client.deligator.xxl.job.admin.JobDeligator;
import com.huoli.ctar.cfesag.client.deligator.yonyou.YonYouTrvlDeligator;
import com.huoli.ctar.cfesag.client.deligator.zhongtai.HuoliZhongtaiDeligator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;

@Configuration
@Import({RestTemplateConfiguration.class, DeligatorConfiguration.class})
@ConditionalOnProperty(prefix = "com.huoli.ctar.cfesag", name = "host")
public class CfesagClientAutoConfiguration {

    @Autowired
    private BaseDataCacheDeligator baseDataCacheDeligator;

    @Autowired
    private InternationalFlightTicketDeligator internationalFlightTicketDeligator;

    @Autowired
    private InternationalFlightTicketSearchDeligator internationalFlightTicketSearchDeligator;

    @Autowired
    private PaymentGatewayDeligator paymentGatewayDeligator;

    @Autowired
    private PaymentSysDeligator paymentSysDeligator;

    @Autowired
    private ResaleBookingDeligator resaleBookingDeligator;

    @Autowired
    private ResaleGatewayDeligator resaleGatewayDeligator;

    @Autowired
    private SSODeligator ssoDeligator;

    @Autowired
    private TrainTicketDeligator trainTicketDeligator;

    @Autowired
    private CarDeligator carDeligator;

    @Autowired
    private CarHailingDeligator carHailingDeligator;

    @Autowired
    private YuHongFlightOrderDeligator yuHongFlightOrderDeligator;

    @Autowired
    private EasypnpOrderDeligator easypnpOrderDeligator;

    @Autowired
    private YuHongHotelOrderDeligator yuHongHotelOrderDeligator;

    @Autowired
    private NyyhPostSaleDeligator nyyhPostSaleDeligator;

    @Autowired
    private NyjkPostSaleDeligator nyjkPostSaleDeligator;

    @Autowired
    private HotelDeligator hotelDeligator;

    @Autowired
    private InvoiceDeligator invoiceDeligator;

    @Autowired
    private PushCenterDeligator pushCenterDeligator;

    @Autowired
    private UserCenterDeligator userCenterDeligator;

    @Autowired
    private DomesticFlightTicketBookingSupportDeligator domesticFlightTicketBookingSupportDeligator;

    @Autowired
    private DomesticFlightTicketBookingDeligator domesticFlightTicketBookingDeligator;

    @Autowired
    private DomesticFlightTicketBookDeligator domesticFlightTicketBookDeligator;

    @Autowired
    private DomesticFlightTicketDynamicDeligator domesticFlightTicketDynamicDeligator;

    @Autowired
    private DomesticFlightTicketChannelDeligator domesticFlightTicketChannelDeligator;

    @Autowired
    private DomesticFlightTicketOrderCenterDeligator domesticFlightTicketOrderCenterDeligator;

    @Autowired
    private DomesticFlightTicketBizDeligator domesticFlightTicketBizDeligator;

    @Autowired
    private DomesticFlightTicketVipHallDeligator domesticFlightTicketVipHallDeligator;

    @Autowired
    private DomesticTicketSearchDeligator domesticTicketSearchDeligator;

    @Autowired
    private BizTicketOrderDeligator bizTicketOrderDeligator;

    @Autowired
    private InsuranceDeligator insuranceDeligator;

    @Autowired
    private BusApiDeligator busApiDeligator;

    @Autowired
    private ApiToolsDeligator apiToolsDeligator;

    @Autowired
    private MaycurDeligator maycurDeligator;

    @Autowired
    private PostServiceDeligator postServiceDeligator;

    @Autowired
    private FlightTicketGrabbingDeligator flightTicketGrabbingDeligator;

    @Autowired
    private ReimbursementReceiptDeligator reimbursementReceiptDeligator;

    @Autowired
    private TrainQueryDeligator trainQueryDeligator;

    @Autowired
    private TrainUnifiedDeligator trainUnifiedDeligator;

    @Autowired
    private TrainQPDeligator trainQPDeligator;

    @Autowired
    private CouponDeligator couponDeligator;

    @Autowired
    private PaymentAccountSysDeligator paymentAccountSysDeligator;

    @Autowired
    private CorpPayDelegator corpPayDelegator;

    @Autowired
    private ResaleInfoDeligator resaleInfoDeligator;

    @Autowired
    private FuluDeligator fuluDeligator;

    @Autowired
    private HljxDeligator hljxDeligator;

    @Autowired
    private JobDeligator jobDeligator;

    @Autowired
    private TrainDistributorDeligator trainDistributorDeligator;

    @Autowired
    private TourismDeligator tourismDeligator;

    @Autowired
    private VetechDeligator vetechDeligator;

    @Autowired
    private FeiShuDeligator feiShuDeligator;

    @Autowired
    private WechatDeligator wechatDeligator;

    @Autowired
    private MallDeligator mallDeligator;

    @Autowired
    private PnrDeligator pnrDeligator;

    @Autowired
    private InternationalFlightTicketOrderCenterDeligator internationalFlightTicketOrderCenterDeligator;

    @Autowired
    private TourismResaleDeligator tourismResaleDeligator;

    @Autowired
    private YonYouTrvlDeligator yonYouTrvlDeligator;

    @Autowired
    private DomesticFlightTicketOrderSensitiveDeligator domesticFlightTicketOrderSensitiveDeligator;

    @Autowired
    private CorpInvoiceNotifyDeligator corpInvoiceNotifyDeligator;

    @Autowired
    private TripNowApiDeligator tripNowApiDeligator;

    @Resource
    private HuoliZhongtaiDeligator huoliZhongtaiDeligator;

    @Resource
    private SettleMoneyMgmtSysDeligator settleMoneyMgmtSysDeligator;

    @Autowired
    private FlightCouponDeligator flightCouponDeligator;

    @Resource
    private FlightDynamicsSvcDeligator flightDynamicsSvcDeligator;

    @Resource
    private VetechFCDeligator vetechFCDeligator;

    @Bean
    public CfesagClient cfesagClient() {
        final DefaultCfesagClient defaultCfesagClient = new DefaultCfesagClient();
        defaultCfesagClient.setBaseDataCacheDeligator(baseDataCacheDeligator);
        defaultCfesagClient.setInternationalFlightTicketDeligator(internationalFlightTicketDeligator);
        defaultCfesagClient.setInternationalFlightTicketSearchDeligator(internationalFlightTicketSearchDeligator);
        defaultCfesagClient.setPaymentGatewayDeligator(paymentGatewayDeligator);
        defaultCfesagClient.setPaymentSysDeligator(paymentSysDeligator);
        defaultCfesagClient.setResaleBookingDeligator(resaleBookingDeligator);
        defaultCfesagClient.setResaleGatewayDeligator(resaleGatewayDeligator);
        defaultCfesagClient.setSsoDeligator(ssoDeligator);
        defaultCfesagClient.setTrainTicketDeligator(trainTicketDeligator);
        defaultCfesagClient.setCarDeligator(carDeligator);
        defaultCfesagClient.setHotelDeligator(hotelDeligator);
        defaultCfesagClient.setInvoiceDeligator(invoiceDeligator);
        defaultCfesagClient.setPushCenterDeligator(pushCenterDeligator);
        defaultCfesagClient.setUserCenterDeligator(userCenterDeligator);
        defaultCfesagClient.setDomesticFlightTicketBookingSupportDeligator(domesticFlightTicketBookingSupportDeligator);
        defaultCfesagClient.setDomesticFlightTicketBookingDeligator(domesticFlightTicketBookingDeligator);
        defaultCfesagClient.setDomesticFlightTicketBookDeligator(domesticFlightTicketBookDeligator);
        defaultCfesagClient.setDomesticFlightTicketChannelDeligator(domesticFlightTicketChannelDeligator);
        defaultCfesagClient.setDomesticFlightTicketOrderCenterDeligator(domesticFlightTicketOrderCenterDeligator);
        defaultCfesagClient.setBizTicketOrderDeligator(bizTicketOrderDeligator);
        defaultCfesagClient.setInsuranceDeligator(insuranceDeligator);
        defaultCfesagClient.setBusApiDeligator(busApiDeligator);
        defaultCfesagClient.setApiToolsDeligator(apiToolsDeligator);
        defaultCfesagClient.setMaycurDeligator(maycurDeligator);
        defaultCfesagClient.setPostServiceDeligator(postServiceDeligator);
        defaultCfesagClient.setFlightTicketGrabbingDeligator(flightTicketGrabbingDeligator);
        defaultCfesagClient.setReimbursementReceiptDeligator(reimbursementReceiptDeligator);
        defaultCfesagClient.setTrainQueryDeligator(trainQueryDeligator);
        defaultCfesagClient.setTrainUnifiedDeligator(trainUnifiedDeligator);
        defaultCfesagClient.setTrainQPDeligator(trainQPDeligator);
        defaultCfesagClient.setDomesticFlightTicketDynamicDeligator(domesticFlightTicketDynamicDeligator);
        defaultCfesagClient.setCarHailingDeligator(carHailingDeligator);
        defaultCfesagClient.setYuHongFlightOrderDeligator(yuHongFlightOrderDeligator);
        defaultCfesagClient.setEasypnpOrderDeligator(easypnpOrderDeligator);
        defaultCfesagClient.setYuHongHotelOrderDeligator(yuHongHotelOrderDeligator);
        defaultCfesagClient.setNyyhPostSaleDeligator(nyyhPostSaleDeligator);
        defaultCfesagClient.setDomesticFlightTicketBizDeligator(domesticFlightTicketBizDeligator);
        defaultCfesagClient.setDomesticFlightTicketVipHallDeligator(domesticFlightTicketVipHallDeligator);
        defaultCfesagClient.setDomesticTicketSearchDeligator(domesticTicketSearchDeligator);
        defaultCfesagClient.setCouponDeligator(couponDeligator);
        defaultCfesagClient.setPaymentAccountSysDeligator(paymentAccountSysDeligator);
        defaultCfesagClient.setCorpPayDelegator(corpPayDelegator);
        defaultCfesagClient.setResaleInfoDeligator(resaleInfoDeligator);
        defaultCfesagClient.setFuluDeligator(fuluDeligator);
        defaultCfesagClient.setHljxDeligator(hljxDeligator);
        defaultCfesagClient.setJobDeligator(jobDeligator);
        defaultCfesagClient.setTrainDistributorDeligator(trainDistributorDeligator);
        defaultCfesagClient.setTourismDeligator(tourismDeligator);
        defaultCfesagClient.setVetechDeligator(vetechDeligator);
        defaultCfesagClient.setFeiShuDeligator(feiShuDeligator);
        defaultCfesagClient.setWechatDeligator(wechatDeligator);
        defaultCfesagClient.setMallDeligator(mallDeligator);
        defaultCfesagClient.setPnrDeligator(pnrDeligator);
        defaultCfesagClient.setInternationalFlightTicketOrderCenterDeligator(internationalFlightTicketOrderCenterDeligator);
        defaultCfesagClient.setTourismResaleDeligator(tourismResaleDeligator);
        defaultCfesagClient.setNyjkPostSaleDeligator(nyjkPostSaleDeligator);
        defaultCfesagClient.setYonYouTrvlDeligator(yonYouTrvlDeligator);
        defaultCfesagClient.setDomesticFlightTicketOrderSensitiveDeligator(domesticFlightTicketOrderSensitiveDeligator);
        defaultCfesagClient.setCorpInvoiceNotifyDeligator(corpInvoiceNotifyDeligator);
        defaultCfesagClient.setTripNowApiDeligator(tripNowApiDeligator);
        defaultCfesagClient.setHuoliZhongtaiDeligator(huoliZhongtaiDeligator);
        defaultCfesagClient.setSettleMoneyMgmtSysDeligator(settleMoneyMgmtSysDeligator);
        defaultCfesagClient.setFlightCouponDeligator(flightCouponDeligator);
        defaultCfesagClient.setFlightDynamicsSvcDeligator(flightDynamicsSvcDeligator);
        defaultCfesagClient.setVetechFCDeligator(vetechFCDeligator);
        return defaultCfesagClient;
    }
}
