package com.huoli.ctar.cfesag.client.deligator.pushcenter.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.pushcenter.PushCenterDeligator;
import com.huoli.ctar.cfesag.external.sys.api.pushcenter.model.PushMessageRequest;
import com.huoli.ctar.cfesag.external.sys.api.pushcenter.model.PushTemplateMessageRequest;
import com.huoli.ctar.cfesag.external.sys.api.pushcenter.model.PushWeChatMessageRequest;
import com.huoli.ctar.cfesag.external.sys.api.pushcenter.model.SendSmsRequest;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultPushCenterDeligator extends BaseDeligator implements PushCenterDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultPushCenterDeligator.class);

    public DefaultPushCenterDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String pushTemplateBizMsg(PushTemplateMessageRequest templateMessageRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PushTemplateMessageRequest> httpEntity = new HttpEntity<>(templateMessageRequest, httpHeaders);

        final String url = this.getCfesagHost() + "/external/sys/api/pushCenter/bizMsg/push";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        }, true);
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String pushMsg(final PushMessageRequest pushMessageRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PushMessageRequest> httpEntity = new HttpEntity<>(pushMessageRequest, httpHeaders);

        final String url = this.getCfesagHost() + "/external/sys/api/pushCenter/msg/push";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        }, true);
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String pushWeChatMsg(final PushWeChatMessageRequest pushWeChatMessageRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PushWeChatMessageRequest> httpEntity = new HttpEntity<>(pushWeChatMessageRequest, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/pushCenter/weChat/msg/push";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String sendSms(final SendSmsRequest sendSmsRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<SendSmsRequest> httpEntity = new HttpEntity<>(sendSmsRequest, httpHeaders);

        final String url = this.getCfesagHost() + "/external/sys/api/pushCenter/sms/send";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
}
