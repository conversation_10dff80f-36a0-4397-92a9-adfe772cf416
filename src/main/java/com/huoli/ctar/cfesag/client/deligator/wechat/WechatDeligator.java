package com.huoli.ctar.cfesag.client.deligator.wechat;

import com.huoli.ctar.cfesag.external.sys.api.wechat.*;

/**
 * <AUTHOR>
 * @Description
 * @create 2021-11-19 15:45
 */
public interface WechatDeligator {

    SuiteAccessTokenResult getSuiteAccessToken(final SuiteTokenParam suiteTokenParam);

    PreAuthCodeResult getPreAuthCode(final String suiteAccessToken);

    CorpPermanentCodeResult getPermanentCode(final String suiteAccessToken,
                                             final PermanentCodeParam permanentCodeParam);

    CorpAccessTokenResult getCorpToken(final String suiteAccessToken,
                                       final CorpTokenParam corpTokenParam);

    WechatBaseResult setSessionInfo(final String suiteAccessToken,
                                    final SessionInfoParam sessionInfoParam);
}
