package com.huoli.ctar.cfesag.client.deligator.train.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainQPDeligator;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.QPMemberActiveParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.QPMemberActiveResult;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.CancelQPOrderParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.CreateQPOrderParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.CreateQPOrderResult;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.GetQPOrderDetailParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.GetQPOrderDetailResult;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.GetQPOrderIdResult;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.GetSuccessOrderResultItem;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.QPPaymentDetailParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.qp.QPPaymentDetailResult;
import com.huoli.ctar.core.infra.model.BaseResult;

/**
 * Created by ZL on 2020/08/19
 */
public class DefaultTrainQPDeligator extends BaseDeligator implements TrainQPDeligator {

    
    private static final Logger log = LoggerFactory.getLogger(DefaultTrainQPDeligator.class);
    /**
     * @param restTemplate
     * @param cfesagHost
     */
    public DefaultTrainQPDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }
   
    @Override
    public QPMemberActiveResult isActivedMember(QPMemberActiveParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QPMemberActiveParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/qp/isActivedMember";
        ResponseEntity<BaseResult<QPMemberActiveResult>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<QPMemberActiveResult>>() {});
        final BaseResult<QPMemberActiveResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public GetQPOrderIdResult getQPOrderId(String phoneid) {
        final String url = this.getCfesagHost() + "/external/sys/api/train/qp/getOrderId?phoneid=" + phoneid;
        ResponseEntity<BaseResult<GetQPOrderIdResult>> response = execute(url, HttpMethod.GET, null,
                new ParameterizedTypeReference<BaseResult<GetQPOrderIdResult>>() {});
        final BaseResult<GetQPOrderIdResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CreateQPOrderResult createQPOrder(CreateQPOrderParam.QPOrderFormData param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CreateQPOrderParam.QPOrderFormData> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/qp/createOrder";
        ResponseEntity<BaseResult<CreateQPOrderResult>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<CreateQPOrderResult>>() {});
        final BaseResult<CreateQPOrderResult> result = checkResponse(response);
        return result.getData();
    }
    
    @Override
    public GetQPOrderDetailResult getQPOrderDetail(GetQPOrderDetailParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<GetQPOrderDetailParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/qp/getOrderDetail";
        ResponseEntity<BaseResult<GetQPOrderDetailResult>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<GetQPOrderDetailResult>>() {});
        final BaseResult<GetQPOrderDetailResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String cancelQP(CancelQPOrderParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CancelQPOrderParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/qp/cancel";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<String>>() {});
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public QPPaymentDetailResult getQPPaymentDetail(QPPaymentDetailParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QPPaymentDetailParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/qp/getPaymentDetail";
        ResponseEntity<BaseResult<QPPaymentDetailResult>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<QPPaymentDetailResult>>() {});
        final BaseResult<QPPaymentDetailResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<GetSuccessOrderResultItem> getQPSuccessOrders() {
        final String url = this.getCfesagHost() + "/external/sys/api/train/qp/getSuccessOrders";
        ResponseEntity<BaseResult<List<GetSuccessOrderResultItem>>> response = execute(url, HttpMethod.POST, null,
                new ParameterizedTypeReference<BaseResult<List<GetSuccessOrderResultItem>>>() {});
        final BaseResult<List<GetSuccessOrderResultItem>> result = checkResponse(response);
        return result.getData();
    }
}
