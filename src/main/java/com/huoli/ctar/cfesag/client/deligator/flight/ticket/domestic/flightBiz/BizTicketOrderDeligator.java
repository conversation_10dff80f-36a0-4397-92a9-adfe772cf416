package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.flightBiz;

import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.flightBiz.BizTicketOrderQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.flightBiz.BizTicketOrderResult;

import java.util.List;

public interface BizTicketOrderDeligator {

    List<BizTicketOrderResult> queryInfoByProduct(BizTicketOrderQueryParam queryParam);

    List<BizTicketOrderResult> queryInfoByChannel(BizTicketOrderQueryParam queryParam);
}
