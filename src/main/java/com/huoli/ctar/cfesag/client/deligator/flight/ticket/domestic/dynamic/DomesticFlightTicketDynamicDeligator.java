package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.dynamic;

import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.dynamic.DynamicBaseParam;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.dynamic.DynamicDetailResult;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface DomesticFlightTicketDynamicDeligator {

    DynamicDetailResult queryFlightWeather(DynamicBaseParam param);

    List<DynamicDetailResult> queryFlightIntegrationBatch(List<DynamicBaseParam> params);
}
