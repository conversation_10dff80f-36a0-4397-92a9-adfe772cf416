package com.huoli.ctar.cfesag.client.deligator.yonyou.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.yonyou.YonYouTrvlDeligator;
import com.huoli.ctar.cfesag.external.sys.api.yonyou.YonYouPageWrapper;
import com.huoli.ctar.cfesag.external.sys.api.yonyou.YonYouSettlementOrder;
import com.huoli.ctar.cfesag.external.sys.api.yonyou.YonYouSettlementOrderQueryReq;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultYonYouTrvlDeligator
        extends BaseDeligator
        implements YonYouTrvlDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultYonYouTrvlDeligator.class);

    public DefaultYonYouTrvlDeligator(final RestTemplate restTemplate, final String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public YonYouPageWrapper<YonYouSettlementOrder> querySettlementOrders(final YonYouSettlementOrderQueryReq queryReq) {
        final HttpHeaders headers = constructCommonHeaders();
        HttpEntity<YonYouSettlementOrderQueryReq> httpEntity = new HttpEntity<>(queryReq, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/yonyou/trvl/settlementOrders/query");
        final ResponseEntity<BaseResult<YonYouPageWrapper<YonYouSettlementOrder>>> responseData = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<YonYouPageWrapper<YonYouSettlementOrder>>>() {
        });
        final  BaseResult<YonYouPageWrapper<YonYouSettlementOrder>> result = checkResponse(responseData);
        return result.getData();
    }
}
