package com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.international.InternationalFlightTicketSearchDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.international.search.model.InFlightTicketHandleUrlQueryParam;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultInternationalFlightTicketSearchDeligator extends BaseDeligator implements InternationalFlightTicketSearchDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultInternationalFlightTicketSearchDeligator.class);

    public DefaultInternationalFlightTicketSearchDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String queryInFlightTicketSearchListUrl(InFlightTicketHandleUrlQueryParam param) {
        final String url = String.format("%s/external/sys/api/international/flight/searchListURL?%s", this.getCfesagHost(), buildUrlParams(param.forGetData()));
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.GET, null, param.forGetData(), new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
}
