package com.huoli.ctar.cfesag.client.deligator.tools.busapi.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.tools.busapi.BusApiDeligator;
import com.huoli.ctar.cfesag.external.sys.api.tools.busapi.model.PostInfoRequest;
import com.huoli.ctar.cfesag.external.sys.api.tools.busapi.model.WorkOrderRequest;
import com.huoli.ctar.cfesag.external.sys.api.tools.model.TktReceiptInfo;
import com.huoli.ctar.cfesag.external.sys.api.tools.model.TktReceiptInfoQueryParam;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

public class DefaultBusApiDeligator extends BaseDeligator implements BusApiDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultBusApiDeligator.class);

    public DefaultBusApiDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String savePostNew(PostInfoRequest postInfo) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<PostInfoRequest> httpEntity = new HttpEntity<>(postInfo, httpHeaders);

        final String url = String.format("%s/external/sys/api/post/savePostNew", this.getCfesagHost());

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public String createWorkOrder(WorkOrderRequest workOrderRequest) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<WorkOrderRequest> httpEntity = new HttpEntity<>(workOrderRequest, httpHeaders);

        final String url = String.format("%s/external/sys/api/workOrder/create", this.getCfesagHost());

        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<TktReceiptInfo> queryTktReceiptInfos(final List<String> ticketNos) {
        final TktReceiptInfoQueryParam queryParam = new TktReceiptInfoQueryParam();
        queryParam.setTicketNos(ticketNos);
        return queryTktReceiptInfos(queryParam);
    }

    public List<TktReceiptInfo> queryTktReceiptInfos(final TktReceiptInfoQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<TktReceiptInfoQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/tools/bus-api/tktReceiptInfos/query", this.getCfesagHost());
        ResponseEntity<BaseResult<List<TktReceiptInfo>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<TktReceiptInfo>>>() {
        });
        final BaseResult<List<TktReceiptInfo>> result = checkResponse(response);
        return result.getData();
    }
}
