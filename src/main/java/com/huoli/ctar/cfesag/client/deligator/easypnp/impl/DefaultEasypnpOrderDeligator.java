package com.huoli.ctar.cfesag.client.deligator.easypnp.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.easypnp.EasypnpOrderDeligator;
import com.huoli.ctar.cfesag.client.exception.CfesagInvokeException;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.cfesag.external.sys.api.oyh.model.CooperateHoterOrderMsg;
import com.huoli.ctar.cfesag.external.sys.api.oyh.model.OYHDftOrderMsg;
import com.huoli.ctar.cfesag.external.sys.api.oyh.model.YuHongNofifResult;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.util.TraceContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;

public class DefaultEasypnpOrderDeligator extends BaseDeligator implements EasypnpOrderDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultEasypnpOrderDeligator.class);

    public DefaultEasypnpOrderDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }


    @Override
    public YuHongNofifResult easypnpHotelOrderNotif(CooperateHoterOrderMsg msg) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());

            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api/pnp");
            urlBuilder.append("/hotel/orderNotif");

            HttpEntity<CooperateHoterOrderMsg> httpEntity = new HttpEntity<>(msg,headers);
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<YuHongNofifResult>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<YuHongNofifResult>>() {
            });

            return checkOyhResponse(response);
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }

    }

    protected YuHongNofifResult checkOyhResponse(ResponseEntity<BaseResult<YuHongNofifResult>> response) {
        final HttpStatus.Series series = HttpStatus.Series.valueOf(response.getStatusCode());
        if (HttpStatus.Series.CLIENT_ERROR == series || HttpStatus.Series.SERVER_ERROR == series) {
            if (Objects.nonNull(response.getBody())) {
                log.info("------{}", response.getBody().getMsg());
                throw new CfesagInvokeException(response.getBody().getMsg());
            } else {
                throw new CfesagInvokeException(response.getStatusCode().getReasonPhrase());
            }
        }
        if (Objects.isNull(response.getBody())) {
            throw new CfesagInvokeException("网关服务器内部错误");
        }
        return response.getBody().getData();
    }
}
