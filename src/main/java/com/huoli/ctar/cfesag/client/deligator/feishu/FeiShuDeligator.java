package com.huoli.ctar.cfesag.client.deligator.feishu;

import com.huoli.ctar.cfesag.external.sys.api.feishu.*;

/**
 * <AUTHOR>
 * @program: cms
 * @description
 * @date 2021-06-16 16:43
 */
public interface FeiShuDeligator {
    TokenResult queryAppAccessToken(AppTokenParam param);

    TokenResult queryTenantAccessToken(TenantTokenParam param);

    TokenResult appTicketResend(TicketResendParam param);

    FeishuPageWrapper<FeiShuDepartInfo> loadDepartInfos(final FeiShuDepartQueryParam departQueryParam);

    FeishuPageWrapper<FeiShuUserInfo> loadUserInfos(final FeiShuUserQueryParam userQueryParam);

    FeiShuUserBasicInfo getUserInfo(final String authorization,
                               final String userId,
                               final String userIdType);

    FeiShuSingleUserSendMsgResult sendFeiShuUserMsg(final String authorization,
                                                    final String receiveIdType,
                                                    final FeiShuSingleUserSendMsgRequest requestParam);

    FeiShuBatchSendMsgResult batchSendFeiShuMsg(final String authorization,
                                                final FeiShuBatchSendMsgRequest requestParam);

    ExternalTokenResult tmcTenantToken(TenantAccessTokenParam param);

    FeiShuCardMsgResult sendFeiShuCardMessage(final String authorization,
                                              final FeiShuCardMsgParam cardMsgParam);

    FeiShuTenant getTenant(final String authorization);

    FeiShuReplyMsgResult replyRobotMessage(final String authorization,
                                           final String messageId,
                                           final FeiShuReplyMessageParam replyMessageParam);

    FeiShuLoginUserInfo getFeiShuLoginUserInfo(final String authorization,
                                               final FeiShuLoginParam feiShuLoginParam);

    ExternalTokenResult tmcAppToken(AppAccessTokenParam param);

}
