package com.huoli.ctar.cfesag.client.deligator.coupon;

import com.huoli.ctar.cfesag.external.sys.api.points.model.*;

import java.util.List;

public interface CouponDeligator {

    /**
     * 个人中心用户信息查询接口
     * @param pointsRequest
     * @return
     */
    UserPointsInfoDTO loadUserAccountInfo(BonusPointsRequest pointsRequest) ;

    /**
     * 用户余额信息查询接口
     * @param pointsRequest
     * @return
     */
    UserPointsInfoDTO loadUserPointsInfo(BonusPointsRequest pointsRequest) ;


    /**
     * 用户消费积分接口
     * @param pointsRequest
     * @return
     */
    String consumptionPoints(ConsumptionPointsRequest pointsRequest);

    /**
     * 航班管家内部系统优惠券领取接口（主动发券）
     * @param exchangeCouponsRequest
     * @return
     */
    List<BonusCouponsInfoDTO> exchangeCoupons(ExchangeCouponsRequest exchangeCouponsRequest);

    GtgjExchangeCouponsResult exchangeGtgjCoupons(ExchangeCouponsRequest exchangeCouponsRequest);

    UpgradeMemberMailResult upgradeMemberMail(UpgradeMemberMailRequest upgradeMemberMailRequest);

    /**
     * 用户积分赠送接口
     * @param pointsRequest
     * @return
     */
    String giveUserBonusPoints(GiveBonusPointsRequest pointsRequest);
}
