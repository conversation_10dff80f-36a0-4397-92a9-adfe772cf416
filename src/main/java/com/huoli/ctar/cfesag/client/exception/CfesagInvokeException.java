package com.huoli.ctar.cfesag.client.exception;

import com.huoli.ctar.core.infra.model.BaseResult;

import java.util.List;

public class CfesagInvokeException extends RuntimeException {
    private Integer code = BaseResult.FAILURE;

    private List<BaseResult.Button> buttons;

    private String title;

    public CfesagInvokeException(String message) {
        super(message);
    }


    public CfesagInvokeException(String message, Throwable cause) {
        super(message, cause);
    }

    public CfesagInvokeException(Throwable cause) {
        super(cause);
    }

    public CfesagInvokeException(Integer code, String message, List<BaseResult.Button> buttons, String title) {
        this(code, message);
        this.buttons = buttons;
        this.title = title;
    }

    public CfesagInvokeException( String message, List<BaseResult.Button> buttons, String title) {
        this(message);
        this.buttons = buttons;
        this.title = title;
    }

    public CfesagInvokeException(Integer code, String message, Throwable cause) {
        this(message, cause);
        this.code = code;
    }

    public CfesagInvokeException(Integer code, String message) {
        this(message);
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public List<BaseResult.Button> getButtons(){
        return buttons;
    }

    public String getTitle(){
        return title;
    }
}
