package com.huoli.ctar.cfesag.client.deligator.train.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.huoli.ctar.cfesag.external.sys.api.train.model.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainQueryDeligator;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.CheckPassengerParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.CheckPassengerResultVO;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.QueryCheckPassengerStatusParam;
import com.huoli.ctar.cfesag.external.sys.api.train.model.account.QueryCheckPassengerStatusResultVO;
import com.huoli.ctar.core.infra.model.BaseResult;

public class DefaultTrainQueryDeligator extends BaseDeligator implements TrainQueryDeligator {
    private static final String TRAIN_STATIONS_CACHE_KEY = "tmc.deligator.train.stations";
    private static final String TRAIN_CITY_STATIONS_CACHE_KEY = "tmc.deligator.train.city.stations";

    private static final Logger log = LoggerFactory.getLogger(DefaultTrainQueryDeligator.class);

    public DefaultTrainQueryDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public TrainQueryRecord queryTrainListByStation(TrainQueryParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<TrainQueryParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + String.format("/external/sys/api/train/query/queryTrainListByStation?departname=%s&arrivename=%s&sid=%s&businessId=%s&departdate=%s&gtgjtime=%s&refmt=%s&phoneid=%s&tmcCode=%s&p=%s"
                , param.getDepartname(), param.getArrivename(), param.getSid(), param.getBusinessId(), param.getDepartdate(), param.getGtgjtime(), param.getRefmt(), param.getPhoneid(), param.getTmcCode(),param.getP());
        ResponseEntity<BaseResult<TrainQueryRecord>> response = execute(url, HttpMethod.GET, httpEntity,
                new ParameterizedTypeReference<BaseResult<TrainQueryRecord>>() {
                });
        final BaseResult<TrainQueryRecord> result = checkResponse(response);
        return result.getData();
    }

    protected String buildUrlParams(Map<String, ?> values) {
        if (CollectionUtils.isEmpty(values)) {
            return null;
        }
        StringBuilder params = new StringBuilder();
        values.keySet().forEach(param -> {
            if (params.length() > 0) {
                params.append("&");
            }
            params.append(String.format("%s={%s}", param, param));
        });
        return params.toString();
    }

    @Override
    public QueryTrainDataResultVO queryTrainData(QueryTrainDataParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QueryTrainDataParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/query/queryTrainData";
        ResponseEntity<BaseResult<QueryTrainDataResultVO>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<QueryTrainDataResultVO>>() {
                });
        final BaseResult<QueryTrainDataResultVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public CheckPassengerResultVO checkPassenger(CheckPassengerParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CheckPassengerParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/passenger/checkPassenger";
        ResponseEntity<BaseResult<CheckPassengerResultVO>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<CheckPassengerResultVO>>() {
                });
        final BaseResult<CheckPassengerResultVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public QueryCheckPassengerStatusResultVO queryCheckPassengerStatus(QueryCheckPassengerStatusParam param) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<QueryCheckPassengerStatusParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/passenger/queryCheckPassengerStatus";
        ResponseEntity<BaseResult<QueryCheckPassengerStatusResultVO>> response = execute(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<BaseResult<QueryCheckPassengerStatusResultVO>>() {
                });
        final BaseResult<QueryCheckPassengerStatusResultVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public List<StationQueryResult.Station> queryTrainStations() {
        List<StationQueryResult.Station> stations = (List<StationQueryResult.Station>) getCacheValue(TRAIN_STATIONS_CACHE_KEY);
        if (Objects.isNull(stations)) {
            synchronized (TRAIN_STATIONS_CACHE_KEY) {
                stations = (List<StationQueryResult.Station>) getCacheValue(TRAIN_STATIONS_CACHE_KEY);
                if (Objects.isNull(stations)) {
                    final HttpHeaders httpHeaders = constructCommonHeaders();
                    final HttpEntity httpEntity = new HttpEntity<>(httpHeaders);
                    final String url = String.format("%s/external/sys/api/train/query/queryTrainStations", this.getCfesagHost());
                    ResponseEntity<BaseResult<List<StationQueryResult.Station>>> response = execute(url, HttpMethod.GET, httpEntity,
                            new ParameterizedTypeReference<BaseResult<List<StationQueryResult.Station>>>() {
                            });
                    final BaseResult<List<StationQueryResult.Station>> result = checkResponse(response);
                    stations = CollectionUtils.isEmpty(result.getData()) ? Collections.emptyList() : result.getData();
                    cacheValue(TRAIN_STATIONS_CACHE_KEY, stations, 4 * 60 * 60);
                }
            }
        }
        return stations;
    }

    @Override
    public List<StationQueryResult.Station> queryTrainStationByCityName(String cityName) {
        if (StringUtils.isBlank(cityName)) {
            return Collections.emptyList();
        }

        Map<String, List<StationQueryResult.Station>> cityStationMap = (Map<String, List<StationQueryResult.Station>>) getCacheValue(TRAIN_CITY_STATIONS_CACHE_KEY);
        if (Objects.isNull(cityStationMap)) {
            List<StationQueryResult.Station> stations = queryTrainStations();
            if (CollectionUtils.isEmpty(stations)) {
                return Collections.emptyList();
            }

            synchronized (TRAIN_CITY_STATIONS_CACHE_KEY) {
                cityStationMap = (Map<String, List<StationQueryResult.Station>>) getCacheValue(TRAIN_CITY_STATIONS_CACHE_KEY);
                if (Objects.isNull(cityStationMap)) {
                    if (!CollectionUtils.isEmpty(stations)) {
                        cityStationMap = stations.stream().collect(Collectors.groupingBy(StationQueryResult.Station::getName));
                        cacheValue(TRAIN_CITY_STATIONS_CACHE_KEY, cityStationMap, 4 * 60 * 60);
                    }
                }
            }

        }

        cityName = cityName.trim();
        List<StationQueryResult.Station> cityStations = cityStationMap.get(cityName);
        return CollectionUtils.isEmpty(cityStations) ? Collections.emptyList() : cityStations;
    }

    @Override
    public List<StationQueryResult.Station> queryTrainStationByCityNameFuzzyMatching(String cityName) {
        if (StringUtils.isBlank(cityName)) {
            return Collections.emptyList();
        }
        cityName = cityName.trim();
        List<StationQueryResult.Station> cityStations = queryTrainStationByCityName(cityName);
        if (!CollectionUtils.isEmpty(cityStations)) {
            return cityStations;
        }

        if (cityName.endsWith("市")) {
            return queryTrainStationByCityName(cityName.substring(0, cityName.length() - 1));
        } else {
            return queryTrainStationByCityName(String.format("%s市", cityName));
        }
    }

    @Override
    public Map<String, String> queryCityNameStationName(String cityList) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity httpEntity = new HttpEntity<>(httpHeaders);
        final String url = String.format("%s/external/sys/api/train/distributor/queryCityNameStationName?cityList=%s", this.getCfesagHost(),cityList);
        ResponseEntity<BaseResult<Map<String, String>>> response = execute(url, HttpMethod.GET, httpEntity,
                new ParameterizedTypeReference<BaseResult<Map<String, String>>>() {
                });
        final BaseResult<Map<String, String>> result = checkResponse(response);
        return result.getData();
    }

}
