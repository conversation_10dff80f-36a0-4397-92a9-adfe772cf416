package com.huoli.ctar.cfesag.client.deligator.fulu;

import com.huoli.ctar.cfesag.external.sys.api.fulu.model.model.*;

import java.util.List;

public interface FuluDeligator {

    FuluCreateDirectOrderResult createDirectOrder(final FuluCreateDirectOrderParam param);

    FuluCreateCarmiOrderResult createCarmiOrder(final FuluCreateCarmiOrderParam param);

    FuluGoodsDetailResult queryGoodsDetail(final FuluGoodsParam param);

    List<FuluGoodsBaseInfoResult> queryFuluAllGoods();

    FuluOrderResult queryFuluOrderDetail(final FuluOrderParam param);
    
}
