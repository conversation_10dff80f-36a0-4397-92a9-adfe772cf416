package com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.book.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.book.DomesticFlightTicketBookDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.ticket.domestic.booking.impl.DefaultDomesticFlightTicketBookingDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.book.model.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultDomesticFlightTicketBookDeligator extends BaseDeligator implements DomesticFlightTicketBookDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultDomesticFlightTicketBookingDeligator.class);

    public DefaultDomesticFlightTicketBookDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }


    @Override
    public String queryFlightHandleUrl(DomesticFlightTicketHandleUrlQueryParam param) {
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/book/list/handurl/query?%s", this.getCfesagHost(), buildUrlParams(param.forGetData()));
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.GET, null, param.forGetData(), new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftBookDetailResult queryCabinDetail(DftQueryTicketSearchParam param) {
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/book/cabin/query/v2?%s", this.getCfesagHost(), buildUrlParams(param.forGetData()));
        ResponseEntity<BaseResult<DftBookDetailResult>> response = execute(url, HttpMethod.GET, null, param.forGetData(), new ParameterizedTypeReference<BaseResult<DftBookDetailResult>>() {
        });
        final BaseResult<DftBookDetailResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftTmcFlightConfirmResult confirmFlight(DftFlightConfirmParam param) {
        HttpHeaders httpHeaders = this.constructCommonHeaders();
        HttpEntity<DftFlightConfirmParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        String url = String.format("%s/external/sys/api/domesticFlightTicket/book/per/flight/confirm", this.getCfesagHost());
        ResponseEntity<BaseResult<DftTmcFlightConfirmResult>> response = this.execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<DftTmcFlightConfirmResult>>() {
        });
        BaseResult<DftTmcFlightConfirmResult> result = this.checkResponse(response);
        return result.getData();
    }

    @Override
    public DftPreBookInfo queryBookCache(DftOrderSubmitParam param){
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/book/api/order/per/book/cache", this.getCfesagHost());
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity<DftOrderSubmitParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        ResponseEntity<BaseResult<DftPreBookInfo>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<DftPreBookInfo>>() {
        });
        final BaseResult<DftPreBookInfo> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftOrderSubmitResult submit(DftOrderSubmitParam param){
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/book/api/order/submit", this.getCfesagHost());
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity<DftOrderSubmitParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        ResponseEntity<BaseResult<DftOrderSubmitResult>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<DftOrderSubmitResult>>() {
        });
        final BaseResult<DftOrderSubmitResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftOrderSubmitResult createOrder(DftOrderSubmitParam param){
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/book/api/order/create", this.getCfesagHost());
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity<DftOrderSubmitParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        ResponseEntity<BaseResult<DftOrderSubmitResult>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<DftOrderSubmitResult>>() {
        });
        final BaseResult<DftOrderSubmitResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftOrderSubmitResult createOrderV1(DftOrderSubmitParam param){
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/book/api/order/create/v1", this.getCfesagHost());
        final HttpHeaders httpHeaders = constructCommonHeaders();
        HttpEntity<DftOrderSubmitParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        ResponseEntity<BaseResult<DftOrderSubmitResult>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<DftOrderSubmitResult>>() {
        });
        final BaseResult<DftOrderSubmitResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftPrepayCheckResult corpPrepayCheck(DftOrderSubmitParam param){
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/book/api/tmc/order/per/corp/prepay/check", this.getCfesagHost());
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftOrderSubmitParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        ResponseEntity<BaseResult<DftPrepayCheckResult>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<DftPrepayCheckResult>>() {
        });
        final BaseResult<DftPrepayCheckResult> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public DftFlightListResult queryFlightList(DftFlightListParam param){
        final String url = String.format("%s/external/sys/api/domesticFlightTicket/middle/search/api/search/flight/list", this.getCfesagHost());
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftFlightListParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        ResponseEntity<BaseResult<DftFlightListResult>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<DftFlightListResult>>() {
        });
        final BaseResult<DftFlightListResult> result = checkResponse(response);
        return result.getData();
    }

}
