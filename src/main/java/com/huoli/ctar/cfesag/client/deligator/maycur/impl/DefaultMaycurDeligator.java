package com.huoli.ctar.cfesag.client.deligator.maycur.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.maycur.MaycurDeligator;
import com.huoli.ctar.cfesag.external.sys.api.maycur.model.*;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Slf4j
public class DefaultMaycurDeligator extends BaseDeligator implements MaycurDeligator {

    public DefaultMaycurDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String authorize(final MaycurAuthRequest maycurAuthRequest) {
        final HttpHeaders headers = constructCommonHeaders();
        final HttpEntity<MaycurAuthRequest> httpEntity = new HttpEntity<>(maycurAuthRequest, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/maycur/auth/authorize");
        final ResponseEntity<BaseResult<String>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> baseResult = checkResponse(result);
        return baseResult.getData();
    }

    @Override
    public String pushFlightTicketOrderInfo(final List<MaycurOrder> orders, final String accessToken) {
        final HttpHeaders headers = constructCommonHeaders();
        headers.add("tokenId", accessToken);

        final HttpEntity<List<MaycurOrder>> httpEntity = new HttpEntity<>(orders, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/maycur/flightTicket/orders/push");
        final ResponseEntity<BaseResult<String>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> baseResult = checkResponse(result);
        return baseResult.getData();
    }


    @Override
    public String companyAuthorized(final MaycurCorpInfoParam maycurCorpInfoParam) {
        final HttpHeaders headers = constructCommonHeaders();
        final HttpEntity<MaycurCorpInfoParam> httpEntity = new HttpEntity<>(maycurCorpInfoParam, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/maycur/flightMaster/company/authorized");
        final ResponseEntity<BaseResult<String>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> baseResult = checkResponse(result);
        return baseResult.getData();
    }

    @Override
    public MaycurTrvlPlatformToken platformAuthLogin() {
        final HttpHeaders headers = constructCommonHeaders();
        final HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/maycur/platform/auth/login");
        final ResponseEntity<BaseResult<MaycurTrvlPlatformToken>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<MaycurTrvlPlatformToken>>() {
        });
        final BaseResult<MaycurTrvlPlatformToken> baseResult = checkResponse(result);
        return baseResult.getData();
    }

    @Override
    public String notifyPlatformCorpOpenStatus(final MaycurCorpCreateResultParam corpCreateResultParam,
                                               final String tokenId) {
        final HttpHeaders headers = constructCommonHeaders();
        headers.add("tokenId", tokenId);

        final HttpEntity<MaycurCorpCreateResultParam> httpEntity = new HttpEntity<>(corpCreateResultParam, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/maycur/platform/corp/create/result");
        final ResponseEntity<BaseResult<String>> result = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {
        });
        final BaseResult<String> baseResult = checkResponse(result);
        return baseResult.getData();
    }


}
