package com.huoli.ctar.cfesag.client.deligator.basedata.cache.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.basedata.cache.BaseDataCacheDeligator;
import com.huoli.ctar.cfesag.external.sys.api.basedata.cache.model.DataResult;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;


@Slf4j
public class DefaultBaseDataCacheDeligator extends BaseDeligator implements BaseDataCacheDeligator {

    public DefaultBaseDataCacheDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public DataResult getCities() {
        final String url = this.getCfesagHost() + "/external/sys/api/basedata/cache/cities";
        ResponseEntity<BaseResult<DataResult>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<DataResult>>() {
        });
        final BaseResult<DataResult> result = checkResponse(response);
        return result.getData();
    }
}
