package com.huoli.ctar.cfesag.client.deligator.payment.sys;

import com.huoli.ctar.cfesag.external.sys.api.common.model.CaissaFlowFileQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.payment.gateway.model.CorpTransRecordNew;
import com.huoli.ctar.cfesag.external.sys.api.payment.sys.model.*;
import com.huoli.ctar.core.infra.model.PageWrapper;

import java.util.List;

public interface PaymentSysDeligator {

    String getPayOrderId(RetrievePayOrderIdRequest retrievePayOrderIdRequest);

    ResultWithRefund refundPayOrder(PayRefundRequest payRefundRequest);

    ResultWithPayOrderId createPayOrder(final RetrievePayOrderIdRequest retrievePayOrderIdRequest);

    String getQrCodeOfPayment(RetrieveQrCodeRequest retrieveQrCodeRequest);

    String corpPay(final CorpPayRequest corpPayRequest);

    BatchPayApplyResult batchPayApply(BatchPayApplyParam batchPayApplyParam);

    PageWrapper<List<CorpTransRecordNew>> queryCorpTradeRecord(Long corpId, List<String> orderIds, Integer phoneId, Long transTimeFrom, Long transTimeTo, Integer pageNo, Integer pageSize);

    PageWrapper<List<CorpTransRecordNew>> queryCorpTradeRecord(final Long corpId, final Integer phoneId, final Long transTimeFrom, final Long transTimeTo, final Integer pageNo, final Integer pageSize);

    List<OrderPaymentInfo> queryOrderPaymentInfoByOrderId(final String orderId);

    List<OrderPaymentInfo> queryOrderPaymentInfoByOrderIds(final List<String> orderIds);

    String generateCaissaFlowFile(CaissaFlowFileQueryParam queryParam);
}
