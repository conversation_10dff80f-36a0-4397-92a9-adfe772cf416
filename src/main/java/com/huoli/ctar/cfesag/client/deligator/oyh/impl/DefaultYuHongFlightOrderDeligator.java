package com.huoli.ctar.cfesag.client.deligator.oyh.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.oyh.YuHongFlightOrderDeligator;
import com.huoli.ctar.cfesag.client.exception.CfesagInvokeException;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.cfesag.external.sys.api.oyh.model.OYHDftOrderMsg;
import com.huoli.ctar.cfesag.external.sys.api.oyh.model.YuHongNofifResult;
import com.huoli.ctar.cfesag.external.sys.api.oyh.model.bill.OYHSettlementBill;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.model.BaseResult;
import com.huoli.ctar.core.infra.util.TraceContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;

public class DefaultYuHongFlightOrderDeligator  extends BaseDeligator implements YuHongFlightOrderDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultYuHongFlightOrderDeligator.class);

    public DefaultYuHongFlightOrderDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }


    @Override
    public YuHongNofifResult yuHongflightOrderNotif(OYHDftOrderMsg msg) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());

            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/oyh/orderNotif");

            HttpEntity<OYHDftOrderMsg> httpEntity = new HttpEntity<>(msg,headers);
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<YuHongNofifResult>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<YuHongNofifResult>>() {
            });

            return checkOyhResponse(response);
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }

    @Override
    public BaseResult<String> syncOYHSettlementBill(OYHSettlementBill bill) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            headers.set(GlobalConstant.HEADER_TRACE_ID_NAME, TraceContextUtil.getTraceId());

            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.getCfesagHost());
            urlBuilder.append("/external/sys/api");
            urlBuilder.append("/dfyh/reconciliationOrders/sync");

            HttpEntity<OYHSettlementBill> httpEntity = new HttpEntity<>(bill,headers);
            String url = urlBuilder.toString();
            ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity,  new ParameterizedTypeReference<BaseResult<String>>() {
            });

            return checkOyhSettlementBillResponse(response);
        } catch (RestClientException e) {
            throw new CfesagUnavailableException(e.getMessage(), e);
        }
    }

    protected BaseResult<String> checkOyhSettlementBillResponse(ResponseEntity<BaseResult<String>> response) {
        final HttpStatus.Series series = HttpStatus.Series.valueOf(response.getStatusCode());
        if (HttpStatus.Series.CLIENT_ERROR == series || HttpStatus.Series.SERVER_ERROR == series) {
            if (Objects.nonNull(response.getBody())) {
                log.info("------{}", response.getBody().getMsg());
                throw new CfesagInvokeException(response.getBody().getMsg());
            } else {
                throw new CfesagInvokeException(response.getStatusCode().getReasonPhrase());
            }
        }
        if (Objects.isNull(response.getBody())) {
            throw new CfesagInvokeException("网关服务器内部错误");
        }
        return response.getBody();
    }

    protected YuHongNofifResult checkOyhResponse(ResponseEntity<BaseResult<YuHongNofifResult>> response) {
        final HttpStatus.Series series = HttpStatus.Series.valueOf(response.getStatusCode());
        if (HttpStatus.Series.CLIENT_ERROR == series || HttpStatus.Series.SERVER_ERROR == series) {
            if (Objects.nonNull(response.getBody())) {
                log.info("------{}", response.getBody().getMsg());
                throw new CfesagInvokeException(response.getBody().getMsg());
            } else {
                throw new CfesagInvokeException(response.getStatusCode().getReasonPhrase());
            }
        }
        if (Objects.isNull(response.getBody())) {
            throw new CfesagInvokeException("网关服务器内部错误");
        }
        return response.getBody().getData();
    }
}
