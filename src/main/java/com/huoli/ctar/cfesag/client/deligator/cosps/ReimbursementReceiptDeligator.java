package com.huoli.ctar.cfesag.client.deligator.cosps;

import com.huoli.ctar.cfesag.external.sys.api.cosps.model.DftReimbursementReceiptRecordVO;
import com.huoli.ctar.cfesag.external.sys.api.cosps.model.ReimbursementReceiptPkgRecordVO;
import com.huoli.ctar.cfesag.external.sys.api.cosps.model.ReimbursementReceiptSummitRecordVO;

import java.util.List;

public interface ReimbursementReceiptDeligator {

    String generateCorpReimbursementReport(Long corpId, String month, Integer type, String loginName);

    ReimbursementReceiptPkgRecordVO getReimbursementReceiptPkgRecord(Long corpId, String calStartDate, String calEndDate);

    String pkgRecord(Long corpId, String calStartDate, String calEndDate, List<String> invoiceList);

    String getCorpPostId(Long corpId, String calStartDate, String calEndDate);

    List<DftReimbursementReceiptRecordVO> getDftReimbursementRecords(List<String> orderIds);

    List<ReimbursementReceiptSummitRecordVO> getSummitRecords(List<String> postIds);

}
