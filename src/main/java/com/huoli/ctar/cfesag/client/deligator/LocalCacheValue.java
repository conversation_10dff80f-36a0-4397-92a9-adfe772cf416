package com.huoli.ctar.cfesag.client.deligator;

import lombok.Getter;

@Getter
public class LocalCacheValue {
    /**
     * 存储缓存时间
     */
    protected long cacheTime;

    /**
     * 缓存过期时间（秒，-1永不过期）
     */
    protected long expireSeconds;

    /**
     * 缓存对象
     */
    protected Object value;


    public static LocalCacheValue unExpire(final Object value) {
        return new LocalCacheValue(value, -1L);
    }

    public static LocalCacheValue expire(final Object value,
                                         final long expireSeconds) {
        return new LocalCacheValue(value, expireSeconds);
    }

    protected LocalCacheValue(final Object value,
                              final long expireSeconds) {
        this.cacheTime = System.currentTimeMillis();
        this.value = value;
        this.expireSeconds = expireSeconds <= 0L ? -1L : expireSeconds;
    }

    public boolean expired() {
        if (expireSeconds == -1L) {
            return false;
        }
        return cacheTime + (expireSeconds * 1000L) <= System.currentTimeMillis();
    }
}
