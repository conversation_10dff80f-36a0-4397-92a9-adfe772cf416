package com.huoli.ctar.cfesag.client.deligator.agitech.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.agitech.TripNowApiDeligator;
import com.huoli.ctar.cfesag.external.sys.api.agitech.model.ChatParam;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

@Slf4j
public class DefaultTripNowApiDeligator
        extends BaseDeligator
        implements TripNowApiDeligator {

    public DefaultTripNowApiDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public String chatByTripNow(final ChatParam chatParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<ChatParam> httpEntity = new HttpEntity<>(chatParam, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/agi-tech/trip-now/chat";
        ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {});
        final BaseResult<String> result = checkResponse(response);
        return result.getData();
    }
}
