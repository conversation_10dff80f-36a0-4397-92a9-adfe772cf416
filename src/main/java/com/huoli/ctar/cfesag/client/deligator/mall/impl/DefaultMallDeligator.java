package com.huoli.ctar.cfesag.client.deligator.mall.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.mall.MallDeligator;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.MallCorpOrderRecord;
import com.huoli.ctar.core.infra.model.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

@Slf4j
public class DefaultMallDeligator extends BaseDeligator implements MallDeligator {

    public DefaultMallDeligator(final RestTemplate restTemplate, final String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    /*
     * 查询旅游业务方企业订单消费数据
     */
    public CorpOrderRecordsWrapper<MallCorpOrderRecord> queryMallCorpOrderRecords(final CorpOrderRecordsQueryParam queryParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<CorpOrderRecordsQueryParam> httpEntity = new HttpEntity<>(queryParam, httpHeaders);
        final String url = String.format("%s/huolitrip/tmc/queryOrders.action", this.getCfesagHost());
        final ResponseEntity<BaseResult<CorpOrderRecordsWrapper<MallCorpOrderRecord>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<CorpOrderRecordsWrapper<MallCorpOrderRecord>>>() {
        });
        final BaseResult<CorpOrderRecordsWrapper<MallCorpOrderRecord>> result = checkResponse(response);
        return result.getData();
    }
}
