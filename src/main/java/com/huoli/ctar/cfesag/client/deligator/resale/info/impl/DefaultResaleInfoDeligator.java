package com.huoli.ctar.cfesag.client.deligator.resale.info.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.booking.impl.DefaultResaleBookingDeligator;
import com.huoli.ctar.cfesag.client.deligator.resale.info.ResaleInfoDeligator;
import com.huoli.ctar.cfesag.external.sys.api.resale.ticket.domestic.model.DftOrderBoardingInfo;
import com.huoli.ctar.cfesag.external.sys.api.resale.ticket.domestic.model.DftResaleInfoParam;
import com.huoli.ctar.cfesag.external.sys.api.resale.ticket.domestic.model.DftResaleInfoVO;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

public class DefaultResaleInfoDeligator extends BaseDeligator implements ResaleInfoDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultResaleBookingDeligator.class);

    public DefaultResaleInfoDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public List<DftResaleInfoVO> queryDFTResaleInfo(DftResaleInfoParam dftResaleInfoParam) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftResaleInfoParam> httpEntity = new HttpEntity<>(dftResaleInfoParam, httpHeaders);
        final String url = String.format("%s/external/sys/api/resale/domesticFlightTicket/queryDFTResaleInfo", this.getCfesagHost());
        ResponseEntity<BaseResult<List<DftResaleInfoVO>>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<List<DftResaleInfoVO>>>() {});
        final BaseResult<List<DftResaleInfoVO>> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public void doBoardingNotification(final DftOrderBoardingInfo boardingInfo) {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<DftOrderBoardingInfo> httpEntity = new HttpEntity<>(boardingInfo, httpHeaders);
        final String url = String.format("%s/external/sys/api/resale/domesticFlightTicket/order/boarding/notify", this.getCfesagHost());
        final ResponseEntity<BaseResult<String>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<String>>() {});
        checkResponse(response);
    }
}
