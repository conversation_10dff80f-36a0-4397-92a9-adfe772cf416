package com.huoli.ctar.cfesag.client.deligator.tourism;

import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.TourismResaleOrderConsumption;


public interface TourismResaleDeligator {

    CorpResaleOrderRecsWrapper<TourismResaleOrderConsumption> queryTourismCorpResaleOrderRecords(final CorpResaleOrderRecsQueryParam queryParam);
}
