package com.huoli.ctar.cfesag.client.deligator.flight.dynamics.svc.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.flight.dynamics.svc.FlightDynamicsSvcDeligator;
import com.huoli.ctar.cfesag.external.sys.api.flight.dymanics.svc.model.FlightBoardingInfo;
import com.huoli.ctar.cfesag.external.sys.api.flight.dymanics.svc.model.FlightBoardingInfoQueryParam;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class DefaultFlightDynamicsSvcDeligator
        extends BaseDeligator
        implements FlightDynamicsSvcDeligator {

    private static final Logger log = LoggerFactory.getLogger(DefaultFlightDynamicsSvcDeligator.class);

    public DefaultFlightDynamicsSvcDeligator(final RestTemplate restTemplate, final String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public FlightBoardingInfo queryFlightBoardingInfo(final FlightBoardingInfoQueryParam queryParam) {
        final HttpHeaders headers = constructCommonHeaders();
        HttpEntity<FlightBoardingInfoQueryParam> httpEntity = new HttpEntity<>(queryParam, headers);
        final String url = String.format("%s%s", this.getCfesagHost(), "/external/sys/api/flightDynamicsSvc/flightBoardingInfo/query");
        final ResponseEntity<BaseResult<FlightBoardingInfo>> responseData = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<FlightBoardingInfo>>() {});
        final  BaseResult<FlightBoardingInfo> result = checkResponse(responseData);
        return result.getData();
    }
}