package com.huoli.ctar.cfesag.client.deligator.payment.gateway;

import com.huoli.ctar.cfesag.external.sys.api.payment.gateway.model.*;
import com.huoli.ctar.core.infra.model.PageWrapper;

import java.util.List;

public interface PaymentGatewayDeligator {

    String verifySSOUser(final VerifySSOUserParam verifySSOUserParam);

    DistPaymentAccount getDistPaymentAccount(final QueryDistPaymentAccountRequest queryDistPaymentAccountRequest);

    PageWrapper<List<DistPaymentAccountInfo>> loadDistPaymentAccountInfos(final QueryDistPaymentAccountInfoRequest queryDistPaymentAccountInfoRequest);

    String updateDistPaymentAccount(final UpdateDistPaymentAccountRequest updateDistPaymentAccountRequest);

    String createDistPaymentAccount(final CreateDistPaymentAccountRequest createDistPaymentAccountRequest);

    String rechargeDistPaymentAccount(final DistPaymentAccountRechargeRequest distPaymentAccountRechargeRequest);

    CorpPaymentAccount getCorpPaymentAccount(final Long corpId);

    String createCorpPaymentAccount(final CreateCorpPaymentAccountRequest createCorpPaymentAccountRequest);

    String updateCorpPaymentAccount(final UpdateCorpPaymentAccountRequest updateCorpPaymentAccountRequest);

    String createCorpMemberPaymentInfo(final CreateCorpMemberPaymentInfoRequest createCorpMemberPaymentInfoRequest);

    String updateCorpMemberPaymentInfo(final UpdateCorpMemberPaymentInfoRequest updateCorpMemberPaymentInfoRequest);

    String mergeCorpMemberPaymentInfo(final MergeCorpMemberPaymentInfoRequest mergeCorpMemberPaymentInfoRequest);

    String rechargeCorpPaymentAccount(final RechargeCorpPaymentAccountRequest rechargeCorpPaymentAccountRequest);

    PageWrapper<List<CorpPaymentAccount>> loadCorpPaymentAccounts(final List<String> corpIds, final Integer pageNo, final Integer pageSize);

    CorpConsumptionInfo getCorpConsumptionInfo(final Long corpId, final String productIds);

    PageWrapper<List<CorpMemberPaymentInfo>> loadCorpMemberPaymentInfos(final Long corpId, final List<String> phoneIds, final Integer pageNo, final Integer pageSize);

    CorpMemberPaymentInfo getCorpMemberPaymentInfo(final Long corpId, final Integer phoneId);

    PageWrapper<List<CorpTransRecord>> loadCorpTransRecords(final Long corpId, final Integer phoneId, final Long transTimeFrom, final Long transTimeTo, final Integer pageNo, final Integer pageSize);

    PageWrapper<List<CorpTransRecord>> loadCorpTransRecordsNew(final Long corpId, final Integer phoneId, final Long transTimeFrom, final Long transTimeTo, final Integer pageNo, final Integer pageSize);

    PageWrapper<List<CorpRechargeRecord>> loadCorpRechargeRecords(final Long corpId, final Long transTimeFrom, final Long transTimeTo, final Integer pageNo, final Integer pageSize);

    String emergencyQueryRegister(EmergencyQueryRegisterParams emergencyQueryRegisterParams);

    PageWrapper<List<CaissaCorpTransRecord>> loadCaissaTransRecords(final Long corpId, final String orderId, final String payOrderId, final String tradeStart, final String tradeEnd, final String timeStart, final String timeEnd, final Integer pageNo, final Integer pageSize);

    List<MixPayStatusResult> getMixPayStatus(final String payOrderId);
}
