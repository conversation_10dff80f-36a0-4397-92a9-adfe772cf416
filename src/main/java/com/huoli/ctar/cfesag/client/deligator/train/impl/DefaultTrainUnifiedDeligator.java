package com.huoli.ctar.cfesag.client.deligator.train.impl;

import com.huoli.ctar.cfesag.client.deligator.BaseDeligator;
import com.huoli.ctar.cfesag.client.deligator.train.TrainUnifiedDeligator;
import com.huoli.ctar.cfesag.client.exception.CfesagInvokeException;
import com.huoli.ctar.cfesag.external.sys.api.train.model.CostCentersResultVO;
import com.huoli.ctar.cfesag.external.sys.api.train.model.PlatformBuyOrderCreate;
import com.huoli.ctar.cfesag.external.sys.api.train.model.TrainCreateOrderParam;
import com.huoli.ctar.core.infra.exception.ServiceException;
import com.huoli.ctar.core.infra.model.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;

public class DefaultTrainUnifiedDeligator extends BaseDeligator implements TrainUnifiedDeligator {
    private static final Logger log = LoggerFactory.getLogger(DefaultTrainUnifiedDeligator.class);

    public DefaultTrainUnifiedDeligator(RestTemplate restTemplate, String cfesagHost) {
        super(restTemplate, cfesagHost, log);
    }

    @Override
    public CostCentersResultVO queryCostCenters(String phoneIdOfBookedBy) {
        String url = this.getCfesagHost() + "/external/sys/api/train/costCenters?phoneIdOfBookedBy=" + phoneIdOfBookedBy;
        ResponseEntity<BaseResult<CostCentersResultVO>> response = execute(url, HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<CostCentersResultVO>>() {
        });
        final BaseResult<CostCentersResultVO> result = checkResponse(response);
        return result.getData();
    }

    @Override
    public PlatformBuyOrderCreate createOrderUnified(TrainCreateOrderParam param)
    {
        final HttpHeaders httpHeaders = constructCommonHeaders();
        final HttpEntity<TrainCreateOrderParam> httpEntity = new HttpEntity<>(param, httpHeaders);
        final String url = this.getCfesagHost() + "/external/sys/api/train/createOrderV2";
        ResponseEntity<BaseResult<PlatformBuyOrderCreate>> response = execute(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<BaseResult<PlatformBuyOrderCreate>>() {
        });
        final BaseResult<PlatformBuyOrderCreate> result = checkResponse(response);
        return result.getData();
    }

    protected <T> BaseResult<T> checkResponse(ResponseEntity<BaseResult<T>> response) {
        final HttpStatus.Series series = HttpStatus.Series.valueOf(response.getStatusCode());
        if (HttpStatus.Series.CLIENT_ERROR == series || HttpStatus.Series.SERVER_ERROR == series) {
            if (Objects.nonNull(response.getBody())) {
                throw new CfesagInvokeException(response.getBody().getMsg());
            } else {
                throw new CfesagInvokeException(response.getStatusCode().getReasonPhrase());
            }
        }
        if (Objects.isNull(response.getBody())) {
            throw new CfesagInvokeException("网关服务器内部错误");
        }
        if (BaseResult.SUCCESS != response.getBody().getCode()) {
            throw new ServiceException(response.getBody().getCode(), response.getBody().getMsg());
        }
        return response.getBody();
    }
}
