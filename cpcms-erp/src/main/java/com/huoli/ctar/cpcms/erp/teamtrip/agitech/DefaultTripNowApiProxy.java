package com.huoli.ctar.cpcms.erp.teamtrip.agitech;

import com.huoli.ctar.cfesag.client.CfesagClient;
import com.huoli.ctar.cfesag.client.exception.CfesagInvokeException;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.cfesag.external.sys.api.agitech.constant.ChatCmd;
import com.huoli.ctar.cfesag.external.sys.api.agitech.constant.ChatDomain;
import com.huoli.ctar.cfesag.external.sys.api.agitech.model.ChatContent;
import com.huoli.ctar.cfesag.external.sys.api.agitech.model.ChatParam;
import com.huoli.ctar.cfesag.external.sys.api.agitech.model.ChatRecord;
import com.huoli.ctar.common.utils.DateUtil;
import com.huoli.ctar.common.utils.JsonUtil;
import com.huoli.ctar.common.utils.ListUtil;
import com.huoli.ctar.core.infra.constant.GlobalConstant;
import com.huoli.ctar.core.infra.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("tripNowApiProxy")
public class DefaultTripNowApiProxy implements TripNowApiProxy {

    private static final Logger log = LoggerFactory.getLogger(DefaultTripNowApiProxy.class);

    @Autowired
    private CfesagClient cfesagClient;

    @Override
    public ChatResult chatByTripNow(final ChatReq chatReq) {
        final ChatParam chatParam = new ChatParam();
        chatParam.setCmd(ChatCmd.AI_CHAT.getCode());
        chatParam.setDomain(ChatDomain.TEST.getCode());
        chatParam.setStreamPattern(GlobalConstant.FLAG_NO_VALUE);
        chatParam.setMsg(chatReq.getMsg());
        chatParam.setSessionId(chatReq.getSessionId());

        final String rawResult;
        try {
            rawResult = cfesagClient.chatByTripNow(chatParam);
        } catch (CfesagUnavailableException | CfesagInvokeException e) {
            log.error(String.format("TripNow智能对话发生故障, 错误原因: %s", e.getMessage()), e);
            throw new ServiceException("系统发生了一点问题, 请稍后再试。", e);
        }

        final ChatContent chatContent = JsonUtil.parse(rawResult, ChatContent.class);
        if (log.isInfoEnabled()) {
            log.info(String.format("chatContent ======> %s", JsonUtil.toJson(chatContent)));
        }
        return transfer(chatContent);
    }

    private ChatResult transfer(final ChatContent chatContent) {
        final ChatResult chatResult = new ChatResult();
        if (Objects.nonNull(chatContent)) {
            chatResult.setSessionId(chatContent.getSessionId());
            if (ListUtil.isNotEmpty(chatContent.getChatRecords())) {
                final ChatRecord latestChatRecord = chatContent.getChatRecords().get(chatContent.getChatRecords().size() - 1);
                chatResult.setReplyContent(latestChatRecord.getContent());

                final List<ChatMsg> chatMsgs = new LinkedList<>();
                chatResult.setChatMsgs(chatMsgs);
                for (final ChatRecord chatRecord : chatContent.getChatRecords()) {
                    final ChatMsg chatMsg = new ChatMsg();
                    if (StringUtils.isNotBlank(chatRecord.getTime())) {
                        final Date chatTime = DateUtil.parse(chatRecord.getTime(), new String[] {"yyyy-MM-dd'T'HH:mm:ss.SSS", "yyyy-MM-dd'T'HH:mm:ss"});
                        chatMsg.setChatTime(Objects.isNull(chatTime) ? null : chatTime.getTime());
                    }
                    chatMsg.setRole(chatRecord.getRole());
                    chatMsg.setContent(chatRecord.getContent());
                    chatMsgs.add(chatMsg);
                }
            } else {
                chatResult.setChatMsgs(Collections.emptyList());
            }
        } else {
            chatResult.setChatMsgs(Collections.emptyList());
        }
        return chatResult;
    }
}
