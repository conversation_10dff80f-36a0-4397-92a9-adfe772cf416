package com.huoli.ctar.cpcms.erp.teamtrip.trip;

import com.huoli.ctar.cpcms.erp.teamtrip.ticket.OrderTicket;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * Author:   caijc
 * Date:     2022/10/20 14:13
 * Description: 行程明细
 */
@Getter
@Setter
@Accessors(chain = true)
public abstract class TeamTrip <T extends OrderTicket>{
    // 序号
    Long id;
    // 订单号
    String orderId;
    // 订单状态
    String status;
    // 支付合计
    BigDecimal paymentTotal;
    // 退款合计
    BigDecimal refundTotal;
    // 交易时间
    String transTime;
    // 票信息
    List<T> tickets;
}
