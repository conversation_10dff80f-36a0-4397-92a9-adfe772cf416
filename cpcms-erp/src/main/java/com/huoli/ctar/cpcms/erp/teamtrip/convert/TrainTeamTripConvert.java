package com.huoli.ctar.cpcms.erp.teamtrip.convert;

import com.google.common.base.Converter;
import com.huoli.ctar.cfesag.external.sys.api.common.model.TrainCorpOrderDetailConsRecord;
import com.huoli.ctar.cfesag.external.sys.api.common.model.TrainCorpOrderRecord;
import com.huoli.ctar.cfesag.external.sys.api.train.constant.TrainOrderStatus;
import com.huoli.ctar.common.utils.SensitiveInfoHandleUtil;
import com.huoli.ctar.cpcms.erp.teamtrip.item.PayItem;
import com.huoli.ctar.cpcms.erp.teamtrip.item.RefundItem;
import com.huoli.ctar.cpcms.erp.teamtrip.ticket.TrainOrderTicket;
import com.huoli.ctar.cpcms.erp.teamtrip.trip.TrainTeamTrip;
import com.huoli.ctar.tmccbs.biz.domain.cms.ctm.tsm.constant.ClassLevel;
import com.huoli.ctar.tmccbs.biz.domain.cms.ras.reconciliation.constant.TransType;
import com.huoli.ctar.tmccbs.biz.domain.constant.ServiceId;
import com.huoli.ctar.tmccbs.biz.domain.csa.constant.CmsCorpTrainTicketStatus;
import com.huoli.ctar.tmctfs.beans.constant.TmcConsumeType;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Author:   caijc
 * Date:     2022/10/26 10:02
 * Description:
 */
public class TrainTeamTripConvert extends Converter<TrainCorpOrderRecord, TrainTeamTrip> {
    @Override
    protected TrainTeamTrip doForward(TrainCorpOrderRecord order) {
        TrainTeamTrip trip = new TrainTeamTrip();
        List<TrainOrderTicket> tickets = new ArrayList<>();
        trip.setOrderId(order.getOrderId());
        trip.setStatus(TrainOrderStatus.getNamebyStatus(String.valueOf(order.getOrderStatus())));
        trip.setTransTime(order.getBookedTime());
        // 购票
        List<TrainCorpOrderDetailConsRecord> tks = order.getConsumes().stream()
                .filter(item -> TmcConsumeType.TRAIN_CONSUME_TYPE_3.getId().equals(item.getConsumeType()))
                .collect(Collectors.toList());
        for(TrainCorpOrderDetailConsRecord subOrderVO : tks){
            TrainOrderTicket ticket = new TrainOrderTicket();
            ticket.setSubOrderId(subOrderVO.getSubOrderId());
            ticket.setTrainNo(subOrderVO.getTrainNo());
            ticket.setDepTime(subOrderVO.getDepTime());
            ticket.setArrTime(subOrderVO.getArrTime());
            ticket.setDepPlace(subOrderVO.getDepStation());
            ticket.setArrPlace(subOrderVO.getArrStation());
            ticket.setClassLevel(ClassLevel.getByCode(subOrderVO.getSeatType(), ServiceId.TRAIN_TICKET.getCode()).getName());
            ticket.setTravellerName(SensitiveInfoHandleUtil.decryptName(subOrderVO.getPassengerIdName()));
            // 购保险
            BigDecimal payInsurance = order.getConsumes().stream()
                    .filter(item ->
                            TransType.PAY.getCode().equals(item.getTransType())
                            && item.getSubOrderId().equals(subOrderVO.getSubOrderId())
                            && TmcConsumeType.INSURANCE.getId().equals(item.getConsumeType()))
                    .map(TrainCorpOrderDetailConsRecord::getConsumeAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 退保险
            BigDecimal refundInsurance = order.getConsumes().stream()
                    .filter(item -> TransType.REFUND.getCode().equals(item.getTransType())
                            && item.getSubOrderId().equals(subOrderVO.getSubOrderId())
                            && TmcConsumeType.INSURANCE.getId().equals(item.getConsumeType()))
                    .map(TrainCorpOrderDetailConsRecord::getConsumeAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 票退款
            BigDecimal ticketRefund = order.getConsumes().stream()
                    .filter(item -> TransType.REFUND.getCode().equals(item.getTransType())
                            && item.getSubOrderId().equals(subOrderVO.getSubOrderId())
                            && TmcConsumeType.TRAIN_CONSUME_TYPE_5.getId().equals(item.getConsumeType()))
                    .map(TrainCorpOrderDetailConsRecord::getConsumeAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // ==========支付产品项目
            List<PayItem> payItems = new ArrayList<>();
            order.getConsumes().stream().filter(item ->
                            TransType.PAY.getCode().equals(item.getTransType())
                            && item.getSubOrderId().equals(subOrderVO.getSubOrderId())
                            && TmcConsumeType.INSURANCE.getId().equals(item.getConsumeType()))
                    .forEach(item -> {
                        PayItem payItem = new PayItem();
                        payItem.setItemType(String.valueOf(item.getConsumeType()))
                                .setPrice(item.getConsumeAmount());
                        payItems.add(payItem);
                    });
            ticket.setPayProduceItem(payItems);
            // ==========退款产品项目
            List<RefundItem> refundItems = new ArrayList<>();
            order.getConsumes().stream().filter(item ->
                            TransType.REFUND.getCode().equals(item.getTransType())
                            && item.getSubOrderId().equals(subOrderVO.getSubOrderId())
                            && TmcConsumeType.INSURANCE.getId().equals(item.getConsumeType()))
                    .forEach(item -> {
                        RefundItem refundItem = new RefundItem();
                        refundItem.setItemType(String.valueOf(item.getConsumeType()))
                                .setPrice(item.getConsumeAmount());
                        refundItems.add(refundItem);
                    });
            ticket.setRefundProduceItem(refundItems);
            ticket.setStatus(CmsCorpTrainTicketStatus.getNameByStatus(subOrderVO.getTicketStatus()));
            // 保险
            ticket.setInsurance(payInsurance);
            // 支付金额
            ticket.setTicketPrice(subOrderVO.getConsumeAmount());
            // 票退款金额
            ticket.setTicketRefund(ticketRefund);
            // 保险退款
            ticket.setInsuranceRefund(refundInsurance);
            tickets.add(ticket);
        }
        trip.setTickets(tickets);
        return trip;
    }

    @Override
    protected TrainCorpOrderRecord doBackward(TrainTeamTrip trainTeamTrip) {
        return null;
    }
}
