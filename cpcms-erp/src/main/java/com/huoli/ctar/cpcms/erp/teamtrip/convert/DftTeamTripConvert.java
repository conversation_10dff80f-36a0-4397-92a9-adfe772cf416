package com.huoli.ctar.cpcms.erp.teamtrip.convert;

import com.google.common.base.Converter;
import com.huoli.ctar.cfesag.external.sys.api.common.constant.TicketStatus;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.constant.DomesticFlightTicketOrderStatus;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.ordercenter.model.DftOrderDetailInfoVO;
import com.huoli.ctar.common.utils.DateUtil;
import com.huoli.ctar.cpcms.erp.teamtrip.item.PayItem;
import com.huoli.ctar.cpcms.erp.teamtrip.item.RefundItem;
import com.huoli.ctar.cpcms.erp.teamtrip.ticket.DftOrderTicket;
import com.huoli.ctar.cpcms.erp.teamtrip.trip.DftTeamTrip;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class DftTeamTripConvert extends Converter<DftOrderDetailInfoVO, DftTeamTrip> {

    @Override
    protected DftTeamTrip doForward(DftOrderDetailInfoVO order) {
        DftTeamTrip teamTrip = new DftTeamTrip();
        // 乘客信息map
        Map<Long, DftOrderDetailInfoVO.DftOrderPassengerInfo> passengerInfoMap = order.getPassengers().stream()
                .collect(Collectors.toMap(DftOrderDetailInfoVO.DftOrderPassengerInfo::getId, item -> item));

        // 订单信息
        teamTrip.setOrderId(order.getOrderId());
        teamTrip.setStatus(DomesticFlightTicketOrderStatus.getById(order.getStatus()) != null ?
                DomesticFlightTicketOrderStatus.getById(order.getStatus()).getName(): "未知状态");
        teamTrip.setTransTime(DateUtil.format(new Date(order.getCreateTime()), DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS));
        // 客票信息
        List<DftOrderTicket> tickets = new ArrayList<>();
        order.getTickets().forEach(ticket -> {
            DftOrderTicket dftOrderTicket = new DftOrderTicket();
            // ==========支付项目
            // 基建
            dftOrderTicket.setAirportFee(ticket.getAirportFee());
            // 燃油
            dftOrderTicket.setRateFee(ticket.getRateFee());
            // 保险费
            if (!CollectionUtils.isEmpty(order.getProducts())){
                dftOrderTicket.setInsurance(order.getProducts().stream()
                        .filter(item -> item.getPrdType().compareTo(3) == 0 && item.getTicketId().equals(ticket.getTicketId()))
                        .map(DftOrderDetailInfoVO.DftOrderAddonInfo::getPayPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            // 票价（支付价）
            dftOrderTicket.setTicketPrice(ticket.getPayPrice());

            // ==========支付产品项目
            if (!CollectionUtils.isEmpty(order.getProducts())){
                List<PayItem> payItems = new ArrayList<>();
                order.getProducts().forEach(item -> {
                    PayItem payItem = new PayItem();
                    payItem.setItemType(String.valueOf(item.getPrdType()))
                            .setItemCode(item.getPrdCode())
                            .setPrice(item.getPayPrice());
                    payItems.add(payItem);
                });
                dftOrderTicket.setPayProduceItem(payItems);
            }
            //==========退款产品项目
            if (!CollectionUtils.isEmpty(order.getRefundProducts())){
                List<RefundItem> refundItems = new ArrayList<>();
                order.getRefundProducts().forEach(item -> {
                    RefundItem refundItem = new RefundItem();
                    refundItem.setItemType(String.valueOf(item.getType()))
                            .setItemCode(item.getPrdCode())
                            .setPrice(item.getPayPrice());
                    refundItems.add(refundItem);
                });
                dftOrderTicket.setRefundProduceItem(refundItems);
            }
            // 票退款
            if (!CollectionUtils.isEmpty(order.getRefunds())){
                dftOrderTicket.setTicketRefund(order.getRefunds().stream()
                        .filter(item -> item.getTicketId().equals(ticket.getTicketId()))
                        .map(DftOrderDetailInfoVO.DftOrderRefundInfo::getRefundPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            dftOrderTicket.setTicketNo(ticket.getTicketNo() == null? "" : ticket.getTicketNo());
            dftOrderTicket.setTravellerName(passengerInfoMap.get(ticket.getPsgId()).getName());
            dftOrderTicket.setStatus(TicketStatus.get(ticket.getStatus()).getName());
            dftOrderTicket.setDep(ticket.getSegment().getDep());
            dftOrderTicket.setArr(ticket.getSegment().getArr());
            dftOrderTicket.setDepTime(DateUtil.format(new Date(ticket.getSegment().getDepTime()), DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS));
            dftOrderTicket.setArrTime(DateUtil.format(new Date(ticket.getSegment().getArrTime()), DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS));
            dftOrderTicket.setFlyNo(ticket.getSegment().getFlyNo());
            tickets.add(dftOrderTicket);
        });
        teamTrip.setTickets(tickets);
        return teamTrip;
    }

    @Override
    protected DftOrderDetailInfoVO doBackward(DftTeamTrip teamTrip) {
        return null;
    }
}
