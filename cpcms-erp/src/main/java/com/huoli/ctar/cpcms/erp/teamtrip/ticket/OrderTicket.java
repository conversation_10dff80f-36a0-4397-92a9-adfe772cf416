package com.huoli.ctar.cpcms.erp.teamtrip.ticket;

import com.huoli.ctar.cpcms.erp.teamtrip.item.PayItem;
import com.huoli.ctar.cpcms.erp.teamtrip.item.RefundItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Author:   caijc
 * Date:     2022/10/20 14:57
 * Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public abstract class OrderTicket {
    // 乘客姓名
    String travellerName;
    // 票价
    BigDecimal ticketPrice = BigDecimal.ZERO;
    // 票退款
    BigDecimal ticketRefund = BigDecimal.ZERO;
    // 支付产品信息
    List<PayItem> payProduceItem = new ArrayList<>();
    // 退款产品信息
    List<RefundItem> refundProduceItem = new ArrayList<>();
    // 客票状态
    String status;
    // 仓位级别
    String classLevel;
    // 出发时间
    String depTime;
    // 到达时间时间
    String arrTime;
    // 出发地
    String depPlace;
    // 到达地
    String arrPlace;

    // 产品支付合计：包括保险及其他附加产品
    public BigDecimal getProductPayTotal() {
        return payProduceItem.stream()
                .map(PayItem::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    // 产品退款合计：包括保险及其他附加产品
    public BigDecimal getProductRefundTotal(){
        // 产品退款
        return refundProduceItem.stream()
                .map(RefundItem::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
