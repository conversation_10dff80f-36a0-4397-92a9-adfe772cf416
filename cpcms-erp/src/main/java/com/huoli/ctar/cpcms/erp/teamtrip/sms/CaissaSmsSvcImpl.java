package com.huoli.ctar.cpcms.erp.teamtrip.sms;

import com.huoli.ctar.cfesag.client.CfesagClient;
import com.huoli.ctar.cfesag.client.exception.CfesagInvokeException;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.cfesag.external.sys.api.pushcenter.model.SendSmsRequest;
import com.huoli.ctar.core.infra.constant.AppName;
import com.huoli.ctar.core.infra.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("caissaSmsSvc")
public class CaissaSmsSvcImpl implements ICaissaSmsSvc {

    private static final Logger log = LoggerFactory.getLogger(CaissaSmsSvcImpl.class);

    @Resource
    private CfesagClient cfesagClient;

    public String sendSms(final CaissaSmsSendReq caissaReq) {
        final SendSmsRequest sendSmsReq = buildSendSmsReq(caissaReq);
        String result;
        try {
            result = cfesagClient.sendSms(sendSmsReq);
        } catch (CfesagInvokeException | CfesagUnavailableException e) {
            final String errMsg = String.format("短信发送失败, 错误原因: %s", e.getMessage());
            log.error(errMsg, e);
            throw new ServiceException(errMsg, e);
        }
        return result;
    }

    private SendSmsRequest buildSendSmsReq(final CaissaSmsSendReq caissaReq) {
        final SendSmsRequest sendSmsReq = new SendSmsRequest();
        sendSmsReq.setPhones(caissaReq.getPhoneNumbers());
        final SendSmsRequest.Sms sms = new SendSmsRequest.Sms();
        sms.setType(AppName.KSSL.getNormalSmsType());
        sms.setContent(caissaReq.getContent());
        sendSmsReq.setSms(sms);
        sendSmsReq.setTemplateId(caissaReq.getTemplateId());
        return sendSmsReq;
    }
}
