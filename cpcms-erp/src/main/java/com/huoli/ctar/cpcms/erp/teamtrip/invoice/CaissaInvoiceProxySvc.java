package com.huoli.ctar.cpcms.erp.teamtrip.invoice;

import com.huoli.ctar.cfesag.external.sys.api.invoice.model.EleInvoDto;
import com.huoli.ctar.cfesag.external.sys.api.invoice.model.InvoiceBatchDto;
import com.huoli.ctar.cfesag.external.sys.api.invoice.model.InvoiceBatchRequest;

import java.util.List;

public interface CaissaInvoiceProxySvc {

    InvoiceBatchDto issueInvoices(final InvoiceBatchRequest issueReq);

    String cancelInvoice(final Long invoiceId);

    List<EleInvoDto> queryInvoiceInfos(final String email,
                                       final Long phoneId,
                                       final Long invoiceId,
                                       final String orderId,
                                       final Integer invoiceItemCode,
                                       final Integer source);
}
