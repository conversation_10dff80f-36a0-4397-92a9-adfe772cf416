package com.huoli.ctar.cpcms.erp.teamtrip.invoice;

import com.huoli.ctar.cfesag.client.CfesagClient;
import com.huoli.ctar.cfesag.client.exception.CfesagInvokeException;
import com.huoli.ctar.cfesag.client.exception.CfesagUnavailableException;
import com.huoli.ctar.cfesag.external.sys.api.invoice.model.EleInvoDto;
import com.huoli.ctar.cfesag.external.sys.api.invoice.model.InvoiceBatchDto;
import com.huoli.ctar.cfesag.external.sys.api.invoice.model.InvoiceBatchRequest;
import com.huoli.ctar.cfesag.external.sys.api.invoice.model.QueryInvoiceRequest;
import com.huoli.ctar.common.utils.JsonUtil;
import com.huoli.ctar.core.infra.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("caissaInvoiceProxySvc")
public class CaissaInvoiceProxySvcImpl implements CaissaInvoiceProxySvc {

    private static final Logger log = LoggerFactory.getLogger(CaissaInvoiceProxySvcImpl.class);

    @Resource
    private CfesagClient cfesagClient;

    @Override
    public InvoiceBatchDto issueInvoices(final InvoiceBatchRequest issueReq) {
        final InvoiceBatchDto result;
        try {
            result = cfesagClient.invoiceSaves(issueReq);
        } catch (CfesagUnavailableException | CfesagInvokeException e) {
            final String errMsg = String.format("提交开票失败 =====> issueReq: %s, 错误原因: %s",
                    JsonUtil.toJson(issueReq), e.getMessage()
            );
            log.error(errMsg, e);
            throw new ServiceException(errMsg, e);
        }
        return result;
    }

    @Override
    public String cancelInvoice(final Long invoiceId) {
        final String result;
        try {
            result = cfesagClient.cancelInvoice(invoiceId);
        } catch (CfesagUnavailableException | CfesagInvokeException e) {
            final String errMsg = String.format("作废发票失败 =====> invoiceId: %s, 错误原因: %s", invoiceId, e.getMessage());
            log.error(errMsg, e);
            throw new ServiceException(errMsg, e);
        }
        return result;
    }

    @Override
    public List<EleInvoDto> queryInvoiceInfos(final String email,
                                              final Long phoneId,
                                              final Long invoiceId,
                                              final String orderId,
                                              final Integer invoiceItemCode,
                                              final Integer source) {
        final QueryInvoiceRequest queryReq = new QueryInvoiceRequest();
        queryReq.setId(invoiceId);
        queryReq.setEmail(email);
        queryReq.setInvoiceItemCode(invoiceItemCode);
        queryReq.setPhoneid(phoneId);
        queryReq.setOrderId(orderId);
        queryReq.setSource(source);

        final List<EleInvoDto> result;
        try {
            result = cfesagClient.queryInvoice(queryReq);
        } catch (CfesagUnavailableException | CfesagInvokeException e) {
            final String errMsg = String.format("查询发票信息失败 =====> queryReq: %s, 错误原因: %s", JsonUtil.toJson(queryReq), e.getMessage());
            log.error(errMsg, e);
            throw new ServiceException(errMsg, e);
        }
        return result;
    }
}