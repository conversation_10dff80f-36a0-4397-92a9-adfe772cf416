package com.huoli.ctar.cpcms.erp.teamtrip.trip;

import com.huoli.ctar.cpcms.erp.teamtrip.ticket.DftOrderTicket;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Author:   caijc
 * Date:     2022/10/20 15:00
 * Description: 机票行程明细
 */
@Getter
@Setter
@Accessors(chain = true)
public class DftTeamTrip extends TeamTrip<DftOrderTicket> implements Serializable {
}
