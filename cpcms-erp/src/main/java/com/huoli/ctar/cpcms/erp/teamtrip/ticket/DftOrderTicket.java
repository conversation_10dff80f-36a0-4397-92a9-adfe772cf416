package com.huoli.ctar.cpcms.erp.teamtrip.ticket;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * Author:   caijc
 * Date:     2022/10/20 15:02
 * Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class DftOrderTicket extends OrderTicket {
    // 票号
    String ticketNo;
    // 基建
    BigDecimal airportFee = BigDecimal.ZERO;
    // 燃油，税费
    BigDecimal rateFee = BigDecimal.ZERO;
    // 保险
    BigDecimal insurance = BigDecimal.ZERO;

    // 出发地三字码
    String dep;
    // 到达地三字码
    String arr;
    // 航班号
    String flyNo;
}
