package com.huoli.ctar.cpcms.erp.teamtrip;

import cn.hutool.core.collection.CollectionUtil;
import com.huoli.ctar.cfesag.client.CfesagClient;
import com.huoli.ctar.cfesag.external.sys.api.common.model.CorpOrderRecordsWrapper;
import com.huoli.ctar.cfesag.external.sys.api.common.model.TrainCorpOrderRecord;
import com.huoli.ctar.cfesag.external.sys.api.common.model.resale.CorpResaleOrderRecsQueryParam;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.ordercenter.model.DftOrderDetailInfoVO;
import com.huoli.ctar.cfesag.external.sys.api.flight.ticket.domestic.ordercenter.model.DftOrderDetailParam;
import com.huoli.ctar.cfesag.external.sys.api.payment.sys.model.OrderPaymentInfo;
import com.huoli.ctar.cpcms.erp.teamtrip.convert.DftTeamTripConvert;
import com.huoli.ctar.cpcms.erp.teamtrip.convert.TrainTeamTripConvert;
import com.huoli.ctar.cpcms.erp.teamtrip.enums.TeamTripType;
import com.huoli.ctar.cpcms.erp.teamtrip.req.QueryCostReq;
import com.huoli.ctar.cpcms.erp.teamtrip.trip.DftTeamTrip;
import com.huoli.ctar.cpcms.erp.teamtrip.trip.TrainTeamTrip;
import com.huoli.ctar.tmc.svc.clients.TmcSvcClient;
import com.huoli.ctar.tmccbs.biz.domain.cms.bdm.model.AirportData;
import com.huoli.ctar.tmccbs.biz.domain.cms.bm.entity.OrderCostRuleEntryInfo;
import com.huoli.ctar.tmccbs.biz.domain.cms.bm.vo.std.OrderCostRuleEntryInfoQueryParam;
import com.huoli.ctar.tmccbs.biz.domain.cms.bm.vo.std.OrderCostRuleEntryInfoStdVO;
import com.huoli.ctar.tmccbs.biz.domain.cms.ras.reconciliation.constant.TransType;
import com.huoli.ctar.tmccbs.biz.domain.constant.ServiceId;
import com.huoli.ctar.tmccbs.biz.domain.vo.DefaultStdDataQueryCfgBuilder;
import com.huoli.ctar.tmccbs.biz.domain.vo.StdDataQueryCfg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Author:   caijc
 * Date:     2022/10/20 15:12
 * Description:
 */
@Service
@Slf4j
public class TeamTripServiceImpl implements ITeamTripService{
    @Autowired
    private TmcSvcClient tmcSvcClient;

    @Autowired
    private CfesagClient cfesagClient;

    @Autowired
    private AirportData airportData;

    private final Long corpId = 10498L;

    @Override
    public List<DftTeamTrip> getDftTeamTrip(List<String> orderIds) {
        List<DftOrderDetailInfoVO> dftOrderDetailInfoVOS = new ArrayList<>();
        final Map<String, List<OrderPaymentInfo>> paymentMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(orderIds)){
            DftOrderDetailParam dftOrderDetailParam = new DftOrderDetailParam();
            dftOrderDetailParam.setOrderIds(orderIds);
            // 查询订单数据
            dftOrderDetailInfoVOS = cfesagClient.queryDftOrderDetailInfo(dftOrderDetailParam);
            // 查询支付数据
            paymentMap.putAll(cfesagClient.queryOrderPaymentInfoByOrderIds(orderIds).stream()
                    .collect(Collectors.groupingBy(OrderPaymentInfo::getOrderId)));
        }

        List<DftTeamTrip> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(dftOrderDetailInfoVOS)) {
            return Collections.emptyList();
        }
        // 转换业务数据
        DftTeamTripConvert convert = new DftTeamTripConvert();
        dftOrderDetailInfoVOS.forEach(item -> {
            DftTeamTrip trip = convert.convert(item);
            // 查询支付信息
            List<OrderPaymentInfo> orderPaymentInfos = paymentMap.get(item.getOrderId());
            if (orderPaymentInfos != null && CollectionUtil.isNotEmpty(orderPaymentInfos)){
                BigDecimal paymentTotal = BigDecimal.ZERO;
                BigDecimal refundTotal = BigDecimal.ZERO;
                for (OrderPaymentInfo orderPaymentInfo : orderPaymentInfos) {
                    if(!CollectionUtils.isEmpty(orderPaymentInfo.getTradeRecords())){
                        paymentTotal = paymentTotal.add(orderPaymentInfo.getTradeRecords().stream()
                                .filter(tradeRecord -> TransType.PAY.getId() == tradeRecord.getTradeType())
                                .map(OrderPaymentInfo.TransRecord::getPayPrice)
                                .reduce(BigDecimal.ZERO, BigDecimal::add));
                        refundTotal = refundTotal.add(orderPaymentInfo.getTradeRecords().stream()
                                .filter(tradeRecord -> TransType.REFUND.getId() == tradeRecord.getTradeType())
                                .map(OrderPaymentInfo.TransRecord::getPayPrice)
                                .reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                }
                trip.setPaymentTotal(paymentTotal);
                trip.setRefundTotal(refundTotal);
            }
            // 转换机票数据
            trip.getTickets().forEach(ticket -> {
                ticket.setDepPlace(airportData.getAirportName(ticket.getDep(), 2, ticket.getDepTime().substring(0, 10)));
                ticket.setArrPlace(airportData.getAirportName(ticket.getArr(), 2, ticket.getArrTime().substring(0, 10)));
            });
            list.add(trip);
        });

        return list;
    }

    @Override
    public List<String> getOrderIdFromCostCenter(QueryCostReq req) {
        final StdDataQueryCfg cfg = new DefaultStdDataQueryCfgBuilder()
                .addShowColumns(Arrays.asList(OrderCostRuleEntryInfo.ShowColumn.ORDER_ID.getName(),
                        OrderCostRuleEntryInfo.ShowColumn.SERVICE_ID.getName(),
                        OrderCostRuleEntryInfo.ShowColumn.CORP_ID.getName(),
                        OrderCostRuleEntryInfo.ShowColumn.CORP_ID.getName(),
                        OrderCostRuleEntryInfo.ShowColumn.COST_ITEM_NAME.getName(),
                        "createdTime"))
                .build();

        OrderCostRuleEntryInfoQueryParam param = new OrderCostRuleEntryInfoQueryParam();
        param.setCorpId(req.getCorpId());
        param.setCostItemNameLike(req.getTeamOrderId());
        if (TeamTripType.DFT.getCode().equals(req.getTripType())){
            param.setServiceId(ServiceId.DOMESTIC_FLIGHT_TICKET.getCode());
        } else if (TeamTripType.TRAIN.getCode().equals(req.getTripType())){
            param.setServiceId(ServiceId.TRAIN_TICKET.getCode());
        } else {
            return Collections.emptyList();
        }
        param.setCreatedTimeFrom(req.getTransTimeStart());
        param.setCreatedTimeTo(req.getTransTimeEnd());
        final List<OrderCostRuleEntryInfoStdVO> orderCostRuleEntryInfoList;
        List<String> orderIds = new ArrayList<>();

        orderCostRuleEntryInfoList = tmcSvcClient.queryOrderCostRuleEntryInfos(param, cfg);
        orderIds = orderCostRuleEntryInfoList.stream()
                .map(OrderCostRuleEntryInfoStdVO::getOrderId)
                .distinct()
                .collect(Collectors.toList());
        return orderIds;
    }

    @Override
    public List<TrainTeamTrip> getTrainTeamTrip(List<String> orderIds) {
        List<TrainTeamTrip> list = new ArrayList<>();
        TrainTeamTripConvert convert = new TrainTeamTripConvert();
        final Map<String, List<OrderPaymentInfo>> paymentMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(orderIds)){
            DftOrderDetailParam dftOrderDetailParam = new DftOrderDetailParam();
            dftOrderDetailParam.setOrderIds(orderIds);
            // 查询支付数据
            paymentMap.putAll(cfesagClient.queryOrderPaymentInfoByOrderIds(orderIds).stream()
                    .collect(Collectors.groupingBy(OrderPaymentInfo::getOrderId)));
        }
        for (String orderId : orderIds) {
            final CorpResaleOrderRecsQueryParam queryParam = new CorpResaleOrderRecsQueryParam();
            queryParam.setOrderId(orderId);
            CorpOrderRecordsWrapper<TrainCorpOrderRecord> wrapper = cfesagClient.queryTrainCorpOrderRecords(queryParam);
            List<TrainCorpOrderRecord> orderList = wrapper.getOrderList();
            for (TrainCorpOrderRecord trainCorpOrderRecord : orderList) {
                TrainTeamTrip trip = convert.convert(trainCorpOrderRecord);
                // 查询支付信息
                List<OrderPaymentInfo> orderPaymentInfos = paymentMap.get(trainCorpOrderRecord.getOrderId());
                if (orderPaymentInfos != null && CollectionUtil.isNotEmpty(orderPaymentInfos)){
                    BigDecimal paymentTotal = BigDecimal.ZERO;
                    BigDecimal refundTotal = BigDecimal.ZERO;
                    for (OrderPaymentInfo orderPaymentInfo : orderPaymentInfos) {
                        paymentTotal = paymentTotal.add(orderPaymentInfo.getTradeRecords().stream()
                                .filter(tradeRecord -> TransType.PAY.getId() == tradeRecord.getTradeType())
                                .map(OrderPaymentInfo.TransRecord::getPayPrice)
                                .reduce(BigDecimal.ZERO, BigDecimal::add));
                        refundTotal = refundTotal.add(orderPaymentInfo.getTradeRecords().stream()
                                .filter(tradeRecord -> TransType.REFUND.getId() == tradeRecord.getTradeType())
                                .map(OrderPaymentInfo.TransRecord::getPayPrice)
                                .reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                    trip.setPaymentTotal(paymentTotal);
                    trip.setRefundTotal(refundTotal);
                }
                list.add(trip);
            }
        }
        return list;
    }
}
