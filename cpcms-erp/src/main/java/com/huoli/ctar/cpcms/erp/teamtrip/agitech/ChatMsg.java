package com.huoli.ctar.cpcms.erp.teamtrip.agitech;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChatMsg {
    /**
     * role==user的记录是用户的输入
     * role=assistant的记录是GPT产生的应答
     */
    private String role;
    /**
     * 对话时间
     */
    private Long chatTime;
    /**
     * 对话内容
     */
    private String content;
}
