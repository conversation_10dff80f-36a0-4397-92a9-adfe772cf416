package com.huoli.ctar.cpcms.erp.teamtrip.ticket;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * Author:   caijc
 * Date:     2022/10/26 10:04
 * Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class TrainOrderTicket extends OrderTicket {
    // 车次号
    String trainNo;

    String subOrderId;

    // 保险
    BigDecimal insurance = BigDecimal.ZERO;

    // 保险退款
    BigDecimal insuranceRefund = BigDecimal.ZERO;

}
