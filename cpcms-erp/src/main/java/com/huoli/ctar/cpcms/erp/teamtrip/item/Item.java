package com.huoli.ctar.cpcms.erp.teamtrip.item;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * Author:   caijc
 * Date:     2022/10/27 9:49
 * Description: 费用
 */
@Getter
@Setter
@Accessors(chain = true)
public abstract class Item {
    // 产品类型
    String itemType;
    // 产品编码
    String itemCode;
    // 产品名称
    String itemName;
    // 产品金额
    BigDecimal price = BigDecimal.ZERO;

    abstract String getPayType();
}
