package com.huoli.ctar.cpcms.erp.teamtrip.enums;

/**
 * <AUTHOR>
 * @Date 2022/10/20 15:12
 */
public interface BaseEnum<T extends Enum<T> & BaseEnum<T>> {

  /**
   *
   * @return 获取编码
   */
  Integer getCode();

  /**
   *
   * @return 获取编码名称
   */
  String getName();

  /**
   * 根据code码获取枚举
   *
   * @param cls enum class
   * @param code enum code
   * @return get enum
   */
  static <T extends Enum<T> & BaseEnum<T>> T parseByCode(Class<T> cls, Integer code) {
    for (T t : cls.getEnumConstants()) {
      if (t.getCode().intValue() == code.intValue()) {
        return t;
      }
    }
    return null;
  }

}