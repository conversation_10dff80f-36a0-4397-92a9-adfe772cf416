package com.huoli.ctar.cpcms.erp.teamtrip.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Author:   caijc
 * Date:     2022/10/28 12:03
 * Description:
 */
@Getter
@Setter
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class QueryCostReq extends SignBaseReq{
    // 团号
    String teamOrderId;
    // 出行类型
    Integer tripType;
    // 要同步的订单交易开始时间
    String transTimeStart;
    // 要同步的订单交易开始时间
    String transTimeEnd;
    // 企业id
    Long corpId;
}
