package com.huoli.ctar.cpcms.erp.teamtrip.sms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CaissaSmsSendReq {
    /**
     * 接收短信的电话号码
     */
    private List<String> phoneNumbers;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 短信模版ID
     */
    private String templateId;
}
