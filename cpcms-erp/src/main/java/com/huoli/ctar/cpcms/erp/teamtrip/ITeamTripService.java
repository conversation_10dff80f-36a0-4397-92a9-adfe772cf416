package com.huoli.ctar.cpcms.erp.teamtrip;


import com.huoli.ctar.cpcms.erp.teamtrip.req.QueryCostReq;
import com.huoli.ctar.cpcms.erp.teamtrip.trip.DftTeamTrip;
import com.huoli.ctar.cpcms.erp.teamtrip.trip.TrainTeamTrip;

import java.util.List;

public interface ITeamTripService {

    // 获取国内机票行程信息
    List<DftTeamTrip> getDftTeamTrip(List<String> orderIds);

    // 获取火车票行程信息
    List<TrainTeamTrip> getTrainTeamTrip(List<String> orderIds);

    List<String> getOrderIdFromCostCenter(QueryCostReq req);
}
