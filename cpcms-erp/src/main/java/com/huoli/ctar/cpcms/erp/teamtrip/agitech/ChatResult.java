package com.huoli.ctar.cpcms.erp.teamtrip.agitech;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChatResult {

    /**
     * 当前会话ID
     */
    private String sessionId;

    /**
     * 最新回复的内容
     */
    private String replyContent;

    /**
     * 当前会话的对话内容
     */
    private List<ChatMsg> chatMsgs;
}
