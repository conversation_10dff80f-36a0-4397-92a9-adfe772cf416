package com.huoli.ctar.cpcms.erp.teamtrip.enums;

import java.util.Optional;

public enum TeamTripType implements BaseEnum<TeamTripType> {
  
  DFT(1, "国内机票"),
  TRAIN(2, "火车票");

  TeamTripType(Integer code, String name) {
    this.code = code;
    this.name = name;
  }

  private Integer code;
  private String name;

  @Override
  public Integer getCode() {
    return this.code;
  }

  @Override
  public String getName() {
    return this.name;
  }

  public static Optional<TeamTripType> of(Integer code) {
    return Optional.ofNullable(BaseEnum.parseByCode(TeamTripType.class, code));
  }

}